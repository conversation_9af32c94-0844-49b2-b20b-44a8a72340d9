# Application hostname and networking settings
global:
  baseDomain: us.dev.yunextraffic.cloud
subdomain: insights
contextPath: /user-service
clusterDomain: svc.cluster.local
service:
  type: ClusterIP
  port: 80
nginx:
  port: 8001

javaOptions: -XX:+UseG1GC -XX:MaxMetaspaceSize=512m -XX:MaxRAMPercentage=85.0

# Replication and Autoscaling
replicaCount: 1
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

namespace: insights

# Container Image source
image:
  repository: tactics-registry.artifactory.mocca.yunextraffic.cloud
  pullPolicy: Always
imagePullSecrets:
  - name: tactics-registry.artifactory.mocca.yunextraffic.cloud

# Container security settings
podSecurityContext: { }
securityContext: { }

# Container init required resources database
database:
  host: studio--postgres-cluster-pooler.studio
  port: 5432
  user: "insights.postgres"
  createdb:
    enabled: "true"
    database_name: "spm-users-db"
    timescaledb_enabled: false
    image:
      repository: bitnami/postgresql
      pullPolicy: IfNotPresent
      tag: "16.4.0"
envfrompgsecrets:
  PG_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: insights.postgres.postgres-cred
        key: password
envfromsecrets:
  REDIS_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: insights--central-redis-credentials
        key: auth
# Container resources settings
resources:
  limits:
    memory: "4Gi"
  requests:
    cpu: "300m"
    memory: "1Gi"

application:
  logging:
    level:
      root: INFO

  # Port configuration
  server:
    port: 9100
    servlet:
      context-path: /user-service
    max-http-request-header-size: 100KB

  spring:
    profiles:
      active: dev

    data:
      redis:
        host: redis-master.studio
        port: 6379
        password: #ENV inject
    datasource:
      master:
        hikari:
          driver-class-name: org.postgresql.Driver
          jdbc-url: ************************************************************************************************************************
          username: "insights.postgres"
          password: # ENV Inject
          enabled: true
          maximum-pool-size: 10
          minimum-idle: 5
          idle-timeout: 10000 #10 seconds
          connection-timeout: 30000 #30 seconds
          pool-name: master-pool
      #Agency schema
      agency:
        hikari:
          driver-class-name: org.postgresql.Driver
          jdbc-url: ************************************************************************************************************************
          username: "insights.postgres"
          password: # ENV Inject
          enabled: true
          maximum-pool-size: 30
          minimum-idle: 5
          idle-timeout: 10000 #10 seconds
          connection-timeout: 30000 #30 seconds
          pool-name: agency-pool

    sql:
      init:
        mode: never
        continue-on-error: true

    jpa:
      database: POSTGRESQL
      hibernate:
        ddl-auto: none
        naming:
          physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
      show-sql: false
      properties:
        hibernate:
          dialect: org.hibernate.dialect.PostgreSQLDialect
          jdbc:
            batch_size: 50
          order_inserts: true

    messages:
      basename: i18n/messages

    servlet:
      multipart:
        max-file-size: 25MB
        max-request-size: 25MB

    scheduling:
      core_pool_size: 10
      max_pool_size: 30
      queue_size: 1000

    security:
      oauth2:
        resourceserver:
          jwt:
            issuer-uri: https://iam.us.dev.yunextraffic.cloud/auth/realms/mos
        client:
          registration:
            idp:
              authorization-grant-type: client_credentials
          provider:
            idp:
              token-uri: https://iam.us.dev.yunextraffic.cloud/auth/realms/mos/protocol/openid-connect/token

    kafka:
      enable: true
      bootstrap-servers: redflash-kafka-bootstrap.flash:9092
      topic:
        agency:
          group-id: insights-user-service-agency
          name: datahub-delegate-agency-changed
          concurrency: 3
        intersection:
          name: studio-signal-changed
          concurrency: 3
        perflog-uploaded-changed:
          name: datahub-data-perflog-uploaded-changed
          concurrency: 3
      consumer:
        group-id: insights-user-management   #this is default consumer group

  # SPM Platform configuration
  spm:
    web-ui:
      endpoint: https://insights.us.dev.yunextraffic.cloud
    notification:
      #day
      life-time: 7

    analysis:
      endpoint: http://insights--spm-analysis-service/analysis-service

    rule-service:
      endpoint: http://insights--spm-rule-service/alarm-rule-service

    perflog-crawler-service:
      endpoint: http://insights--spm-perflog-crawler/perflog-crawler-service

  # Spring actuator
  management:
    endpoints:
      web:
        exposure:
          include: health, loggers, threaddump
      loggers:
        enabled: true
    health:
      mail:
        enabled: false

  health:
    config:
      enabled: false

  user:
    user-key:
      lifetime-in-day: 90
      expiration:
        notification-before-days: 10

  studio:
    user-service:
      endpoint: http://studio--user-service.studio:8080
    agency-service:
      endpoint: http://studio--agency-service.studio:8080

  data-hub:
    integration-service:
      endpoint: http://datahub--spm-integration-service.datahub/integration

  mail:
    provider:
      ms-graph:
        # client-id: ENV Inject
        # client-secret: ENV Inject
        # tenant-id: ENV Inject
        sender: <EMAIL>
        save-to-sent: false
    config:
      sender: <EMAIL>
      faviconUrl: https://insights.us.dev.yunextraffic.cloud

  cors:
    allowed-origins: https://*.yunextraffic.cloud

  cache:
    config:
      cache-prefix: "[INSIGHTS-USERS]"
      default-ttl-minutes: 60
      agency:
        ttl-in-minutes: 10
      timezone:
        ttl-in-days: 30

  multi-agency:
    provision:
      agency:
        liquibase:
          enabled: true
          secure-parsing: false
          change-log: classpath:db/changelog/agency/db.master.changelog.xml
          fail-on-error: true
        batch-config:
          batch-size: 5
      master:
        liquibase:
          enabled: true
          secure-parsing: false
          change-log: classpath:db/changelog/master/db.master.changelog.xml
          fail-on-error: true
        batch-config:
          batch-size: 1

  data-retention:
    retention-days:
      enabled-data: 180
      disabled-data: 60
    schedulers:
      clean-up-notification-data:
        enabled: true
        cron: 0 0 1 * * ?