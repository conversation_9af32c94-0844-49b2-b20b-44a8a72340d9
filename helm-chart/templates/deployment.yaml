apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Chart.Name }}
  labels:
    app: {{ .Chart.Name }}
    release: {{ .Release.Name }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      app: {{ .Chart.Name }}
      release: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app: {{ .Chart.Name }}
        release: {{ .Release.Name }}
      annotations:
        dockerSha: {{ .Files.Get "docker-sha.txt" }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if eq .Values.database.createdb.enabled "true" }}
      initContainers:
        - name: init-createdb
          image: "{{ .Values.database.createdb.image.repository }}:{{ .Values.database.createdb.image.tag }}"
          imagePullPolicy: {{ .Values.database.createdb.image.pullPolicy }}
          # To use baseline pod security standard, istio cni plugin is needed. cni plugin mandates us to run init container
          # as user 1337 as it will bypass istio proxy in case an init container accesses another pod container like pgpool in this case
          # Please refer https://istio.io/latest/docs/setup/additional-setup/cni/ section Compatibility with application init containers
          # for more details
          # Now running as 1337 does not have enough privileges to create extract directory for bundle extraction so
          # this extra env variable DOTNET_BUNDLE_EXTRACT_BASE_DIR is needed to be setup
          command: [
            "bash",
            "-c",
            "
            export PGPASSWORD=$PG_PASSWORD;
            until pg_isready -h {{ .Values.database.host }} -p {{ .Values.database.port }} -U {{ .Values.database.user }};
            do echo waiting for {{ .Values.database.host }}; sleep 5; done;
            if [ $(psql -U {{ .Values.database.user }} -h {{ .Values.database.host }} -tAc 'select 1' -d {{ .Values.database.createdb.database_name }} || echo 0 ) == '1' ];
            then
              echo 'Database {{ .Values.database.createdb.database_name }} already exists';
            else
              createdb -h {{ .Values.database.host }} -p {{ .Values.database.port }} -U {{ .Values.database.user }} {{ .Values.database.createdb.database_name }};
              case {?} in 0) { echo 'Database {{ .Values.database.createdb.database_name }} created successfully';} ;;
                          *) { exit 1;};;
              esac;
            fi;
            if [ '{{ .Values.database.createdb.timescaledb_enabled }}' == true ];
            then
              psql  -h {{ .Values.database.host }} -p {{ .Values.database.port }} -U {{ .Values.database.user }} -d {{ .Values.database.createdb.database_name }} -c \"create extension timescaledb\";
              echo 'timescaledb add on added.';
            fi;
            "
          ]
          securityContext:
            # Need to add this as init container can't use istio-proxy for redirection to connect to another pod container like postgres
            # to skip that need to run this init container as user 1337
            # Please refer https://istio.io/latest/docs/setup/additional-setup/cni/ section Compatibility with application init containers
            # for more details
            runAsUser: 1337
          env:
            {{- range $name, $item := .Values.envfromsecrets }}
            - name: {{ $name }}
              {{- $item | toYaml | nindent 14 }}
            {{- end }}
            {{- range $name, $item := .Values.envfrompgsecrets }}
            - name: {{ $name }}
              {{- $item | toYaml | nindent 14 }}
            {{- end }}
      {{- end }}
      containers:
        - name: {{ .Chart.Name }}
          env:
            - name: JAVA_TOOL_OPTIONS
              value: {{.Values.javaOptions}}
            - name: SPRING_DATA_REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: insights--central-redis-credentials
                  key: auth
            - name: SPRING_DATASOURCE_MASTER_HIKARI_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: insights.postgres.postgres-cred
                  key: password
            - name: SPRING_DATASOURCE_AGENCY_HIKARI_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: insights.postgres.postgres-cred
                  key: password
            - name: SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_IDP_CLIENTID
              valueFrom:
                secretKeyRef:
                  name: idp
                  key: SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_IDP_CLIENTID
            - name: SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_IDP_CLIENTSECRET
              valueFrom:
                secretKeyRef:
                  name: idp
                  key: SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_IDP_CLIENTSECRET
            - name: MAIL_PROVIDER_MSGRAPH_CLIENTID
              valueFrom:
                secretKeyRef:
                  name: mail-provider
                  key: MAIL_PROVIDER_MSGRAPH_CLIENTID
            - name: MAIL_PROVIDER_MSGRAPH_CLIENTSECRET
              valueFrom:
                secretKeyRef:
                  name: mail-provider
                  key: MAIL_PROVIDER_MSGRAPH_CLIENTSECRET
            - name: MAIL_PROVIDER_MSGRAPH_TENANTID
              valueFrom:
                secretKeyRef:
                  name: mail-provider
                  key: MAIL_PROVIDER_MSGRAPH_TENANTID
          image: "{{ .Values.image.repository }}/{{ .Chart.Name }}:{{ .Values.image.tag | default .Chart.Version }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.nginx.port }}
              protocol: TCP
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          # Commenting below 2 lines as the nginxbase image needs root privileges to run
          # runAsNonRoot: true
          # runAsUser: 1001
          readinessProbe:
            httpGet:
              path: /actuator/health/readiness
              # do not change port
              port: 9193
            initialDelaySeconds: 60
            periodSeconds: 15
          livenessProbe:
            httpGet:
              path: /actuator/health/liveness
              # do not change port
              port: 9193
            initialDelaySeconds: 180
            periodSeconds: 15
          lifecycle:
            preStop:
              exec:
                command: ["sh", "-c", "sleep 10"]
          volumeMounts:
            - name: config-volume
              mountPath: /app/config
      volumes:
        - name: config-volume
          configMap:
            name: {{.Chart.Name}}-configmap
  revisionHistoryLimit: 1
                  
