apiVersion: networking.istio.io/v1alpha3
kind: Gateway
metadata:
  name: {{ .Chart.Name }}-gateway
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: 80
      name: http
      protocol: HTTP
    hosts:
    - {{ .Values.subdomain }}.{{ .Values.global.baseDomain }}
---    
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: {{ .Chart.Name }}
spec:
  hosts:
  - {{ .Values.subdomain }}.{{ .Values.global.baseDomain }}
  gateways:
  - {{ .Chart.Name }}-gateway
  http:
  - directResponse:
      status: 404
    match:
      - uri:
          prefix: {{ .Values.contextPath }}/internal-api
  - match:
    - uri:
        prefix: {{ .Values.contextPath }}
    route:
    - destination:
        host: {{ .Chart.Name }}.{{ .Release.Namespace }}.{{ .Values.clusterDomain }}
        port:
          number: {{ .Values.service.port }}
