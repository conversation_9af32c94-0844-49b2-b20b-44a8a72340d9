/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : create_intersection_metadata.sql
 * Project     : SPM Platform
 */

-- add agency
INSERT INTO agency_settings (agency_id)
VALUES (1000);

-- existed intersection scenario
INSERT INTO intersection (id, created_at, created_by, last_modified_at, last_modified_by, latitude, longitude,
                          model, name, perflog_recent_time, status, timezone, version)
VALUES ('ae9efee2-1288-464d-8ac1-7816963b31c6', '2023-09-12 03:00:29.197460', 'test',
        '2024-04-04 08:15:09.913922', 'test', 38.9715, -95.2883, 'Test model', 'Existed intersection',
        '2021-06-08 10:00:00.000000', 'AVAILABLE', 'America/Chicago', '5.2.2');

INSERT INTO agency_intersection (agency_id, intersection_id)
VALUES (1000, 'ae9efee2-1288-464d-8ac1-7816963b31c6')