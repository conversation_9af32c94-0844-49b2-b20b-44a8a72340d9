/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : NotificationVOBuilderTest.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.notificationdata.util;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.siemens.spm.common.constant.NotificationConstants;
import com.siemens.spm.usermanagementservice.api.vo.NotificationSimpleVO;
import com.siemens.spm.usermanagementservice.domain.Notification;
import com.siemens.spm.usermanagementservice.domain.NotificationMessage;
import com.siemens.spm.usermanagementservice.domain.NotificationMessageKey;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * <AUTHOR>
 */
class NotificationVOBuilderTest {

    /**
     * Expects Map<String, String> for both name and description
     */
    @Test
    void test_buildSimpleNotificationVO_with_name_key_and_description_key() {
        NotificationMessage message = NotificationMessage.builder()
                .description(NotificationConstants.ALARM_RULE_CREATED_DESC)
                .name(NotificationConstants.ALARM_RULE_CREATED)
                .id(new NotificationMessageKey(1L, "en"))
                .build();
        List<NotificationMessage> messages = new ArrayList<>();
        messages.add(message);

        Notification notification = Notification.builder()
                .notificationMessage(messages)
                .build();

        NotificationSimpleVO vo = NotificationVOBuilder.buildNotificationSimpleVO(notification);

        assertThat(vo.getName()).isInstanceOf(Map.class);
        assertThat(vo.getDescription()).isInstanceOf(Map.class);
    }

}
