package com.siemens.spm.usermanagementservice.notificationdata.strategy;

import com.siemens.spm.common.message.MessageService;
import com.siemens.spm.common.shared.domaintype.ActionTarget;
import com.siemens.spm.common.shared.domaintype.IntersectionStatus;
import com.siemens.spm.common.shared.domaintype.notification.NotificationType;
import com.siemens.spm.common.shared.vo.ActionVO;
import com.siemens.spm.common.shared.vo.NotificationMessageRequestVO;
import com.siemens.spm.common.util.BeanFinder;
import com.siemens.spm.datahub.api.boundary.DataIntegrationService;
import com.siemens.spm.datahub.api.vo.response.AgencySchemaResponseVO;
import com.siemens.spm.datahub.exception.DataHubException;
import com.siemens.spm.spmstudiosdk.dto.StudioAgencyDto;
import com.siemens.spm.spmstudiosdk.dto.StudioUserDto;
import com.siemens.spm.spmstudiosdk.exception.StudioException;
import com.siemens.spm.spmstudiosdk.service.StudioAgencyService;
import com.siemens.spm.spmstudiosdk.service.StudioUserService;
import com.siemens.spm.usermanagementservice.api.vo.ReleaseNoteVO;
import com.siemens.spm.usermanagementservice.api.vo.request.NotificationSearchRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.NotificationUpdatedRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.NotificationsCreateRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.*;
import com.siemens.spm.usermanagementservice.domain.Intersection;
import com.siemens.spm.usermanagementservice.domain.Notification;
import com.siemens.spm.usermanagementservice.intersectiondata.vo.IntersectionStatusHistoryV0;
import com.siemens.spm.usermanagementservice.repository.IntersectionRepository;
import com.siemens.spm.usermanagementservice.repository.NotificationRepository;
import com.siemens.spm.usermanagementservice.repository.NotificationTypeRepository;
import com.siemens.spm.usermanagementservice.repository.filterdata.NotificationFilterDataVO;
import com.siemens.spm.usermanagementservice.util.BeanFinderMocker;
import org.junit.jupiter.api.*;
import org.mockito.*;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Random;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class NotificationStrategyBeanTest {

    @InjectMocks
    private NotificationStrategyBean notificationStrategyBean;

    @Mock
    private NotificationRepository notificationRepository;

    @Mock
    private IntersectionRepository intersectionRepository;

    @Mock
    private RealtimeNotificationSender realtimeNotificationSender;

    @Mock
    private MessageService messageService;

    @Mock
    private NotificationTypeRepository notificationTypeRepository;

    @Mock
    private StudioAgencyService studioAgencyService;


    @Mock
    private StudioUserService studioUserService;

    @Mock
    private DataIntegrationService dataIntegrationService;

    private static MockedStatic<BeanFinder> beanFinderMock;

    private static final int AGENCY_ID = 1;

    private static final String AVAILABLE_INTERSECTION_ID = UUID.randomUUID().toString();
    private static final String UNAVAILABLE_INTERSECTION_ID = UUID.randomUUID().toString();

    @BeforeEach
    void beforeEach() {
        MockitoAnnotations.openMocks(this);
    }

    @BeforeAll
    static void initClass() {
        beanFinderMock = Mockito.mockStatic(BeanFinder.class);
        beanFinderMock.when(BeanFinder::getDefaultObjectMapper).thenReturn(BeanFinderMocker.objectMapper());
        beanFinderMock.when(BeanFinder::getDefaultMessageService).thenReturn(BeanFinderMocker.messageService());
    }

    @AfterAll
    static void afterTestClass() {
        beanFinderMock.close();
    }

    @Test
    void testCreateNotificationsForUsersInternalAsync_invalidRequest() {
        NotificationsCreateRequestVO createRequestVO = new NotificationsCreateRequestVO();
        notificationStrategyBean.createNotificationsForUsersInternalAsync(createRequestVO);

        Mockito.verifyNoInteractions(notificationRepository);
    }

    @Test
    void testCreateNotificationsValidForUsersInternalAsync_Request() throws DataHubException {
        NotificationsCreateRequestVO createRequestVO = new NotificationsCreateRequestVO();
        createRequestVO.setAction(ActionVO.builder().btnTitle("Notification Valid")
                .data("hihi")
                .label("ok")
                .target(ActionTarget.NOTIFICATIONS).build());
        createRequestVO.setNotificationMessages(List.of(NotificationMessageRequestVO.builder()
                .description("ok")
                .language("VN")
                .name("Alo").build()));
        createRequestVO.setAgencyId(1738);
        createRequestVO.setTypeId(2L);
        createRequestVO.setContent("{\"analysis_type_id\":\"aor\",\"alarm_category_id\":2," +
                "\"alarm_condition\":\"{\\\"conjunction\\\":\\\"NONE\\\",\\\"conditions\\\":" +
                "[{\\\"category_id\\\":\\\"cat_no_data\\\",\\\"metric_id\\\":\\\"no_data\\\"," +
                "\\\"value\\\":\\\"1\\\",\\\"operator\\\":\\\">=\\\"}]}\",\"intersection_id\":" +
                "\"9e42f720-96f6-4e18-b1d9-789d5b63ccff\",\"intersection_name\":\"6th & Rockledge" +
                "\",\"alarm_content\":\"{\\\"{\\\\\\\"metric_id\\\\\\\":\\\\\\\"no_data\\\\\\\"," +
                "\\\\\\\"target\\\\\\\":null}\\\":1.0}\",\"alarm_time\":\"2021-03-02T02:00:00\"," +
                "\"zone_offset\":\"-06:00\"}");
        createRequestVO.setUserIds(List.of(21));
        beanFinderMock.when(BeanFinder::getDefaultObjectMapper).thenReturn(BeanFinderMocker.objectMapper());
        when(dataIntegrationService.getAgencySchema(anyInt())).thenReturn(AgencySchemaResponseVO.builder()
                .lastModifiedAt(LocalDateTime.now())
                .agencyName("MAT")
                .createdAt(LocalDateTime.now())
                .timezoneId("America/New_York")
                .lastModifiedAt(LocalDateTime.now()).build());
        notificationStrategyBean.createNotificationsForUsersInternalAsync(createRequestVO);
    }

    @Test
    void testCreateNotificationsForUsersInternalAsync_success() {
        final String content = "{\"analysis_type_id\":\"aor\",\"alarm_category_id\":2," +
                               "\"alarm_condition\":\"{\\\"conjunction\\\":\\\"NONE\\\",\\\"conditions\\\":" +
                               "[{\\\"category_id\\\":\\\"cat_no_data\\\",\\\"metric_id\\\":\\\"no_data\\\"," +
                               "\\\"value\\\":\\\"1\\\",\\\"operator\\\":\\\">=\\\"}]}\",\"intersection_id\":" +
                               "\"9e42f720-96f6-4e18-b1d9-789d5b63ccff\",\"intersection_name\":\"6th & Rockledge" +
                               "\",\"alarm_content\":\"{\\\"{\\\\\\\"metric_id\\\\\\\":\\\\\\\"no_data\\\\\\\"," +
                               "\\\\\\\"target\\\\\\\":null}\\\":1.0}\",\"alarm_time\":\"2021-03-02T02:00:00\"," +
                               "\"zone_offset\":\"-06:00\"}";
        NotificationsCreateRequestVO createRequestVO = NotificationsCreateRequestVO.builder()
                .agencyId(AGENCY_ID)
                .userIds(List.of(0))
                .notificationMessages(List.of(new NotificationMessageRequestVO()))
                .typeId(2L)
                .content(content)
                .build();
        notificationStrategyBean.createNotificationsForUsersInternalAsync(createRequestVO);

        Mockito.verify(realtimeNotificationSender, atLeast(1)).sendToUsersAsync(any());
    }

    @Test
    void testCreateReleaseNoteNotifications() throws StudioException {

        Mockito.when(studioAgencyService.getAllActiveAgencies()).thenReturn(List.of(mockStudioAgencyDto()));
        Mockito.when(studioUserService.findAllByAgencyId(anyInt())).thenReturn(List.of(mockStudioUserDto()));
        notificationStrategyBean.createReleaseNoteNotifications(new ReleaseNoteVO());

        Mockito.verify(notificationRepository, atLeast(1)).saveAll(anyIterable());
    }

    @Test
    void testGetLatestUnreadNotifications() throws StudioException {
        Mockito.when(notificationRepository
                        .findPageByUserIdAndAgencyIdAndLatestUnread(anyLong(), anyInt(), anyInt(), anyInt()))
                .thenReturn(List.of(mockNotification(AVAILABLE_INTERSECTION_ID)));

        Mockito.when(notificationRepository
                        .countTotalByUserIdAndAgencyIdAndUnreadStatus(anyLong(), anyInt()))
                .thenReturn(8L);
        Mockito.when(studioUserService.findByEmail(any())).thenReturn(Optional.of(mockStudioUserDto()));
        NotificationLatestUnreadResultObject resultObject = notificationStrategyBean
                .getLatestUnreadNotifications(AGENCY_ID, 1, 10);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(NotificationLatestUnreadResultObject.StatusCode.SUCCESS,
                resultObject.getStatusCode());

        NotificationLatestUnreadResultObject.ResponseData responseData = resultObject.getData();
        Assertions.assertNotNull(responseData);
        Assertions.assertEquals(8L, responseData.getTotalCount());
    }

    @Test
    void testSearchNotifications_AvailableIntersection() throws StudioException {
        Mockito.when(intersectionRepository.findAllByFilter(any()))
                .thenReturn(List.of(mockIntersection(AVAILABLE_INTERSECTION_ID)));
        Mockito.when(notificationRepository
                        .findPageByFilter(
                                NotificationFilterDataVO
                                        .builder()
                                        .intIds(List.of(AVAILABLE_INTERSECTION_ID))
                                        .build(),
                                null, new Random().nextInt(), new Random().nextInt()))
                .thenReturn(List.of(mockNotification(AVAILABLE_INTERSECTION_ID)));
        Mockito.when(notificationRepository.countTotalByFilter(any()))
                .thenReturn(8L);
        Mockito.when(studioUserService.findByEmail(any())).thenReturn(Optional.of(mockStudioUserDto()));
        NotificationSearchRequestVO searchRequest = NotificationSearchRequestVO.builder()
                .intersection(AVAILABLE_INTERSECTION_ID)
                .page(2)
                .size(10)
                .build();
        NotificationSearchResultObject resultObject = notificationStrategyBean.searchNotifications(searchRequest);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(NotificationSearchResultObject.StatusCode.SUCCESS, resultObject.getStatusCode());

        NotificationSearchResultObject.ResponseData responseData = resultObject.getData();
        Assertions.assertNotNull(responseData);
        Assertions.assertEquals(8L, responseData.getTotalCount());
    }

    @Test
    void testSearchNotifications_UnavailableIntersection() throws StudioException {
        Mockito.when(intersectionRepository.findAllByFilter(any()))
                .thenReturn(List.of());

        Mockito.when(studioUserService.findByEmail(any())).thenReturn(Optional.of(mockStudioUserDto()));

        NotificationSearchRequestVO searchRequest = NotificationSearchRequestVO.builder()
                .intersection(UNAVAILABLE_INTERSECTION_ID)
                .build();
        NotificationSearchResultObject resultObject = notificationStrategyBean.searchNotifications(searchRequest);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(NotificationSearchResultObject.StatusCode.SUCCESS, resultObject.getStatusCode());

        NotificationSearchResultObject.ResponseData responseData = resultObject.getData();
        Assertions.assertNotNull(responseData);
        Assertions.assertTrue(responseData.getNotifications().isEmpty());
        Assertions.assertEquals(0L, responseData.getTotalCount());
    }

    @Test
    void testSearchNotifications_MoreThan1Intersection_AndHas_AvailableIntersection() throws StudioException {
        Mockito.when(studioUserService.findByEmail(any())).thenReturn(Optional.of(mockStudioUserDto()));
        //find out more than 1 intersection by text searching (id or name)
        Mockito.when(intersectionRepository.findAllByFilter(any()))
                .thenReturn(List.of(
                        mockIntersection(UNAVAILABLE_INTERSECTION_ID),
                        mockIntersection(AVAILABLE_INTERSECTION_ID)));
        Mockito.when(notificationRepository
                        .findPageByFilter(
                                NotificationFilterDataVO
                                        .builder()
                                        .intIds(List.of(AVAILABLE_INTERSECTION_ID))
                                        .build(),
                                null, new Random().nextInt(), new Random().nextInt()))
                .thenReturn(List.of(mockNotification(AVAILABLE_INTERSECTION_ID)));
        Mockito.when(notificationRepository.countTotalByFilter(any()))
                .thenReturn(8L);
        Mockito.when(studioUserService.findByEmail(any())).thenReturn(Optional.of(mockStudioUserDto()));
        NotificationSearchRequestVO searchRequest = NotificationSearchRequestVO.builder()
                .intersection(UNAVAILABLE_INTERSECTION_ID)
                .page(2)
                .size(10)
                .build();
        NotificationSearchResultObject resultObject = notificationStrategyBean.searchNotifications(searchRequest);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(NotificationSearchResultObject.StatusCode.SUCCESS, resultObject.getStatusCode());

        NotificationSearchResultObject.ResponseData responseData = resultObject.getData();
        Assertions.assertNotNull(responseData);
        Assertions.assertEquals(8L, responseData.getTotalCount());
    }

    @Test
    void testSearchNotifications_MoreThan1Intersection_AllUnavailableIntersection() throws StudioException {
        //all intersections are unavailable
        Mockito.when(intersectionRepository.findAllByFilter(any()))
                .thenReturn(List.of());

        Mockito.when(studioUserService.findByEmail(any())).thenReturn(Optional.of(mockStudioUserDto()));

        NotificationSearchRequestVO searchRequest = NotificationSearchRequestVO.builder()
                .intersection(UNAVAILABLE_INTERSECTION_ID)
                .page(2)
                .size(10)
                .build();
        NotificationSearchResultObject resultObject = notificationStrategyBean.searchNotifications(searchRequest);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(NotificationSearchResultObject.StatusCode.SUCCESS, resultObject.getStatusCode());

        NotificationSearchResultObject.ResponseData responseData = resultObject.getData();
        Assertions.assertNotNull(responseData);
        Assertions.assertTrue(responseData.getNotifications().isEmpty());
        Assertions.assertEquals(0L, responseData.getTotalCount());
    }

    @Test
    void testUpdateNotifications_invalidReadStatus() {
        NotificationUpdatedRequestVO updateRequest = NotificationUpdatedRequestVO.builder()
                .readStatus("foo")
                .build();
        NotificationManipulateResultObject resultObject = notificationStrategyBean.updateNotifications(updateRequest);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(NotificationManipulateResultObject.StatusCode.INVALID_READ_STATUS,
                resultObject.getStatusCode());
    }

    @Test
    void testUpdateNotifications_invalidFlagStatus() {
        NotificationUpdatedRequestVO updateRequest = NotificationUpdatedRequestVO.builder()
                .flagStatus("foo")
                .build();
        NotificationManipulateResultObject resultObject = notificationStrategyBean.updateNotifications(updateRequest);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(NotificationManipulateResultObject.StatusCode.INVALID_FLAG_STATUS,
                resultObject.getStatusCode());
    }

    @Test
    void testUpdateNotifications_notificationNotFound() {
        Mockito.when(notificationRepository.countTotalByFilter(any()))
                .thenReturn(0L);

        NotificationUpdatedRequestVO updateRequest = NotificationUpdatedRequestVO.builder()
                .readStatus("READ")
                .flagStatus("FLAG")
                .build();
        NotificationManipulateResultObject resultObject = notificationStrategyBean.updateNotifications(updateRequest);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(NotificationManipulateResultObject.StatusCode.NOTIFICATION_NOT_FOUND,
                resultObject.getStatusCode());
    }

    @Test
    void testUpdateNotifications_successNoContent() throws StudioException {
        Mockito.when(notificationRepository.countTotalByFilter(any()))
                .thenReturn(1L);
        Mockito.when(studioUserService.findByEmail(any())).thenReturn(Optional.of(mockStudioUserDto()));
        NotificationUpdatedRequestVO updateRequest = NotificationUpdatedRequestVO.builder()
                .readStatus("READ")
                .flagStatus("FLAG")
                .notiIds(List.of(1L))
                .build();

        NotificationManipulateResultObject resultObject = notificationStrategyBean
                .updateNotifications(updateRequest);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(NotificationManipulateResultObject.StatusCode.NO_CONTENT,
                resultObject.getStatusCode());

        Mockito.verify(notificationRepository, atLeast(1))
                .changeReadStatusOfNotificationsByIdIn(anyLong(), anyList(), anyString());
        Mockito.verify(notificationRepository, atLeast(1))
                .changeFlagStatusOfNotificationsByIdIn(anyLong(), anyList(), anyString());
    }

    @Test
    void testMarkAllNotificationsAsRead() throws StudioException {
        Mockito.when(studioUserService.findByEmail(any())).thenReturn(Optional.of(mockStudioUserDto()));
        NotificationManipulateResultObject resultObject = notificationStrategyBean.markAllNotificationsAsRead(
                AGENCY_ID);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(NotificationManipulateResultObject.StatusCode.NO_CONTENT,
                resultObject.getStatusCode());
        Mockito.verify(notificationRepository, times(1))
                .markAllUnreadNotificationsAsReadByUserIdAndAgencyId(anyLong(), anyInt());
    }

    @Test
    void testDeleteNotifications_notificationNotFound() {
        Mockito.when(notificationRepository.countTotalByFilter(any()))
                .thenReturn(0L);

        NotificationManipulateResultObject resultObject = notificationStrategyBean.deleteNotifications(List.of(0L));

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(NotificationManipulateResultObject.StatusCode.NOTIFICATION_NOT_FOUND,
                resultObject.getStatusCode());
    }

    @Test
    void testDeleteNotifications_successNoContent() throws StudioException {
        Mockito.when(studioUserService.findByEmail(any())).thenReturn(Optional.of(mockStudioUserDto()));
        Mockito.when(notificationRepository.countTotalByFilter(any()))
                .thenReturn(1L);
        NotificationManipulateResultObject resultObject = notificationStrategyBean.deleteNotifications(List.of(0L));

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(NotificationManipulateResultObject.StatusCode.NO_CONTENT,
                resultObject.getStatusCode());
    }

    @Test
    void testGetNotificationDetails_notificationNotFound() throws StudioException {
        Mockito.when(studioUserService.findByEmail(any())).thenReturn(Optional.of(mockStudioUserDto()));
        Mockito.when(notificationRepository.findByIdAndUserId(anyLong(), anyLong()))
                .thenReturn(null);
        NotificationDetailsResultObject resultObject = notificationStrategyBean.getNotificationDetails(0L);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(NotificationDetailsResultObject.StatusCode.NOTIFICATION_NOT_FOUND,
                resultObject.getStatusCode());
    }

    @Test
    void testGetNotificationDetails_success() throws StudioException {
        Mockito.when(studioUserService.findByEmail(any())).thenReturn(Optional.of(mockStudioUserDto()));
        Mockito.when(notificationRepository.findByIdAndUserId(anyLong(), anyLong()))
                .thenReturn(mockNotification(AVAILABLE_INTERSECTION_ID));
        Mockito.when(intersectionRepository.findById(any()))
                .thenReturn(Optional.of(mockIntersection(AVAILABLE_INTERSECTION_ID)));
        NotificationDetailsResultObject resultObject = notificationStrategyBean.getNotificationDetails(0L);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(NotificationDetailsResultObject.StatusCode.SUCCESS,
                resultObject.getStatusCode());
        Mockito.verifyNoInteractions(messageService);
    }

    @Test
    void testGetNotificationDetails_no_data() throws StudioException {
        Mockito.when(studioUserService.findByEmail(any())).thenReturn(Optional.of(mockStudioUserDto()));
        Mockito.when(notificationRepository.findByIdAndUserId(anyLong(), anyLong()))
                .thenReturn(mockNotification(UNAVAILABLE_INTERSECTION_ID));
        Mockito.when(intersectionRepository.findById(any()))
                .thenReturn(Optional.empty());
        NotificationDetailsResultObject resultObject = notificationStrategyBean.getNotificationDetails(0L);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(NotificationDetailsResultObject.StatusCode.NO_DATA,
                resultObject.getStatusCode());
        Mockito.verifyNoInteractions(messageService);
    }

    @Test
    void testGetAllNotificationTypes() {
        Mockito.when(notificationTypeRepository.findAll())
                .thenReturn(List.of(NotificationType.ALARM_NOTIFICATION));

        NotificationTypeListResultObject resultObject = notificationStrategyBean.getAllNotificationTypes();

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(NotificationTypeListResultObject.StatusCode.SUCCESS,
                resultObject.getStatusCode());
    }

    @Test
    void testCreateNotificationForIntersectionStatusChanged() throws StudioException {
        when(studioUserService.findAllByAgencyId(AGENCY_ID)).thenReturn(List.of(mockStudioUserDto()));

        when(notificationRepository.saveAll(anyList())).thenReturn(List.of(mockNotification(AVAILABLE_INTERSECTION_ID)));
        notificationStrategyBean.createNotificationForIntersectionStatusChanged(List.of(mockIntersectionStatusHistoryV0()), AGENCY_ID);

        verify(notificationRepository).saveAll(anyList());
        verify(realtimeNotificationSender).sendToUsersAsync(any());
    }

    private IntersectionStatusHistoryV0 mockIntersectionStatusHistoryV0() {
        return IntersectionStatusHistoryV0.builder()
                .intersectionId("IntersectionId")
                .toTime(Timestamp.valueOf(LocalDateTime.now()))
                .fromTime(Timestamp.valueOf(LocalDateTime.now()))
                .intersectionName("intersection name")
                .status(IntersectionStatus.AVAILABLE.getInsight())
                .build();
    }

    private StudioAgencyDto mockStudioAgencyDto() {
        StudioAgencyDto studioAgencyDto = new StudioAgencyDto();
        studioAgencyDto.setAgencyNo(1);
        studioAgencyDto.setAgencyName("Test Agency");
        studioAgencyDto.setActivated(true);
        return studioAgencyDto;
    }

    private StudioUserDto mockStudioUserDto() {
        StudioUserDto studioUserDto = new StudioUserDto();
        studioUserDto.setIsEnabled(true);
        studioUserDto.setId(1);
        studioUserDto.setEmail("<EMAIL>");
        return studioUserDto;
    }

    private Notification mockNotification(String iid) {
        return Notification.builder()
                .id(0L)
                .intersectionId(iid)
                .agencyId(AGENCY_ID)
                .userId(0L)
                .typeId(2L)
                .content("{\"analysis_type_id\":\"aor\",\"alarm_category_id\":2," +
                         "\"alarm_condition\":\"{\\\"conjunction\\\":\\\"NONE\\\",\\\"conditions\\\":" +
                         "[{\\\"category_id\\\":\\\"cat_no_data\\\",\\\"metric_id\\\":\\\"no_data\\\"," +
                         "\\\"value\\\":\\\"1\\\",\\\"operator\\\":\\\">=\\\"}]}\",\"intersection_id\":" +
                         "\"9e42f720-96f6-4e18-b1d9-789d5b63ccff\",\"intersection_name\":\"6th & Rockledge" +
                         "\",\"alarm_content\":\"{\\\"{\\\\\\\"metric_id\\\\\\\":\\\\\\\"no_data\\\\\\\"," +
                         "\\\\\\\"target\\\\\\\":null}\\\":1.0}\",\"alarm_time\":\"2021-03-02T02:00:00\"," +
                         "\"zone_offset\":\"-06:00\"}")
                .build();
    }

    private Intersection mockIntersection(String iid) {
        return Intersection.builder()
                .id(iid)
                .status(iid.equals(AVAILABLE_INTERSECTION_ID) ? IntersectionStatus.AVAILABLE.getInsight() : IntersectionStatus.UNAVAILABLE.getInsight())
                .build();
    }

}
