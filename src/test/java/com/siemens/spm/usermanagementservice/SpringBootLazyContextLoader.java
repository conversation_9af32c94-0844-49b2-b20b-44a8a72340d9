/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : SpringBootLazyContextLoader.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.test.context.SpringBootContextLoader;

/**
 * <AUTHOR>
 *
 */
public class SpringBootLazyContext<PERSON>oa<PERSON> extends SpringBootContextLoader {

    @Override
    protected SpringApplication getSpringApplication() {
        SpringApplication app = super.getSpringApplication();
        app.setLazyInitialization(true);
        return app;
    }
}
