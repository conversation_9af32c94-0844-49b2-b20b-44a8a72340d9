/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AgencyKafkaListenerTest.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.agencydata.listener;

import com.siemens.spm.usermanagementservice.agencydata.boundary.AgencyKafkaProcessorFacade;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;

import java.util.HashMap;

import static org.mockito.ArgumentMatchers.any;

@ExtendWith(MockitoExtension.class)
class AgencyKafkaListenerTest {

    @InjectMocks
    AgencyKafkaListener agencyKafkaListener;

    @Mock
    LocalValidatorFactoryBean validator;

    @Mock
    AgencyKafkaProcessorFacade agencyKafkaFacade;

    @Mock
    KafkaConsumer<String, String> kafkaConsumer;

    @Test
    void receiveParsableMessageAndBusinessLogicDoesNotThrow_whenListen_throwNothing() {


        Mockito.doNothing().when(agencyKafkaFacade).handleAgencyKafkaEvent(
                any(),
                any()
        );

        Assertions.assertDoesNotThrow(() -> agencyKafkaListener.listen(
                        new HashMap<>(),
                        new ConsumerRecord<>("test", 1, 1, "test", jsonContent()),
                        kafkaConsumer
                )
        );
    }

    private String jsonContent() {
        return """
                {
                    "id": "1000",
                    "eventType": "DELETE",
                    "schema": null,
                    "agencyName": null,
                    "activated": true
                }""";
    }
}