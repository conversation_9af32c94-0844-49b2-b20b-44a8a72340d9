/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AgencyKafkaFacadeBeanTest.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.agencydata.boundary;

import com.siemens.spm.common.agency.kafka.dto.AgencySchemaPayloadDto;
import com.siemens.spm.common.agency.master.AgencySchemaWriteService;
import com.siemens.spm.common.kafka.common.ChangeTypeEnum;
import com.siemens.spm.usermanagementservice.agencydata.strategy.AgencyBaseInfoMgmtStrategy;
import com.siemens.spm.usermanagementservice.agencydata.strategy.AgencyBaseInfoMgmtStrategyBean;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class AgencyKafkaFacadeBeanTest {

    @InjectMocks
    AgencyKafkaProcessorFacade agencyKafkaFacadeBean;

    @Mock
    AgencySchemaWriteService agencySchemaChangeService;

    @Mock
    AgencyBaseInfoMgmtStrategyBean agencyBaseInfoMgmtStrategy;

    @Test
    void givenCreateEvent_whenHandleAgencyKafkaEvent_invokeCreateMethod() {
        Mockito.doNothing().when(agencyBaseInfoMgmtStrategy).updateAgencySettingBaseInfoFromAgencySchema(any());

        agencyKafkaFacadeBean.handleAgencyKafkaEvent(ChangeTypeEnum.CREATE,
                buildAgencyEventVO(ChangeTypeEnum.CREATE));

        verify(agencySchemaChangeService, times(1)).createAgencySchema(any());
    }

    @Test
    void givenDeleteEvent_whenHandleAgencyKafkaEvent_invokeDeleteMethods() {
        agencyKafkaFacadeBean.handleAgencyKafkaEvent(ChangeTypeEnum.DELETE,
                buildAgencyEventVO(ChangeTypeEnum.DELETE));

        verify(agencySchemaChangeService, times(1)).deleteAgencySchema(any());
    }

    @Test
    void createNotificationFail_givenDeleteEvent_whenHandleAgencyKafkaEvent_failToSendNotificationSilently() {

        Mockito.doNothing().when(agencyBaseInfoMgmtStrategy).updateAgencySettingBaseInfoFromAgencySchema(any());

        Assertions.assertDoesNotThrow(
                () -> agencyKafkaFacadeBean.handleAgencyKafkaEvent(
                        ChangeTypeEnum.CREATE,
                        buildAgencyEventVO(ChangeTypeEnum.DELETE)));
    }

    private AgencySchemaPayloadDto buildAgencyEventVO(ChangeTypeEnum changeTypeEnum) {
        return AgencySchemaPayloadDto.builder()
                .id("1")
                .agencyName("test")
                .eventType(changeTypeEnum.name())
                .activated(true)
                .schema("agency_1")
                .build();
    }
}