//package com.siemens.spm.usermanagementservice.agencydata.strategy.management;
//
//import java.util.Optional;
//
//import com.siemens.spm.usermanagementservice.api.vo.request.AgencyUpdatedRequestVO;
//import com.siemens.spm.usermanagementservice.api.vo.response.AgencyResultObject;
//import com.siemens.spm.usermanagementservice.api.vo.response.AgencyResultObject.AgencyStatusCode;
//import com.siemens.spm.usermanagementservice.domain.Agency;
//import com.siemens.spm.usermanagementservice.repository.AgencyRepository;
//import org.junit.jupiter.api.Assertions;
//import org.junit.jupiter.api.Test;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//
//import static org.mockito.ArgumentMatchers.anyString;
//
//class AgencyMgmtOnSpecificAgencyTest {
//
//    @InjectMocks
//    private AgencyMgmtOnSpecificAgency agencyMgmtOnSpecificAgency;
//
//    @Mock
//    private AgencyRepository agencyRepository;
//
//    private static final String AGENCY_ID = "45298a51-4d20-45bf-be87-0e17de8245bb";
//
//    @Test
//    void test_updateAgency_agencyNotFound() {
//        AgencyUpdatedRequestVO requestVO = AgencyUpdatedRequestVO.builder().build();
//        AgencyRes2ultObject resultObject = agencyMgmtOnSpecificAgency.updateAgency(AGENCY_ID, requestVO);
//
//        Assertions.assertNotNull(resultObject);
//        Assertions.assertEquals(AgencyStatusCode.AGENCY_NOT_FOUND, resultObject.getStatusCode());
//    }
//
//    @Test
//    void test_updateAgency_success() {
//        Mockito.when(agencyRepository.findById(anyString()))
//                .thenReturn(Optional.of(mockAgency(AGENCY_ID)));
//
//        AgencyUpdatedRequestVO requestVO = AgencyUpdatedRequestVO.builder().build();
//        AgencyResultObject resultObject = agencyMgmtOnSpecificAgency.updateAgency(AGENCY_ID, requestVO);
//
//        Assertions.assertNotNull(resultObject);
//        Assertions.assertEquals(AgencyStatusCode.NO_CONTENT, resultObject.getStatusCode());
//    }
//
//    private Agency mockAgency(String agencyId) {
//        return Agency.builder()
//                .id(agencyId)
//                .name("Agency Name")
//                .address("Huong Canh, Binh Xuyen, Vinh Phuc")
//                .build();
//    }
//
//}
