package com.siemens.spm.usermanagementservice.agencydata.strategy;

import java.util.Optional;

import com.siemens.spm.spmstudiosdk.dto.StudioAgencyDto;
import com.siemens.spm.spmstudiosdk.exception.StudioException;
import com.siemens.spm.spmstudiosdk.service.StudioAgencyService;
import com.siemens.spm.usermanagementservice.api.vo.response.AgencyDetailResultObject;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import static org.mockito.ArgumentMatchers.anyInt;

@Slf4j
class AgencySearchStrategyBeanTest {

    @InjectMocks
    private AgencySearchStrategyBean agencySearchStrategyBean;

    @Mock
    private StudioAgencyService studioAgencyService;

    private static final int AGENCY_ID = 1;

    @BeforeEach
    void beforeEach() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testGetAgencyDetail_agencyNotFoundCurrentAgencyNotMatch() {
        AgencyDetailResultObject resultObject = agencySearchStrategyBean.getAgencyDetail(AGENCY_ID);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(AgencyDetailResultObject.StatusCode.AGENCY_NOT_FOUND, resultObject.getStatusCode());
    }

    @Test
    void testGetAgencyDetail_agencyNotFoundInDB() throws StudioException {
        Mockito.when(studioAgencyService.getAgencyById(anyInt())).thenReturn(Optional.empty());

        AgencyDetailResultObject resultObject = agencySearchStrategyBean.getAgencyDetail(AGENCY_ID);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(AgencyDetailResultObject.StatusCode.AGENCY_NOT_FOUND, resultObject.getStatusCode());
    }

    @Test
    void testGetAgencyDetail_success() throws StudioException {
        Mockito.when(studioAgencyService.getAgencyById(anyInt())).thenReturn(Optional.of(mockStudioAgencyDto()));

        AgencyDetailResultObject resultObject = agencySearchStrategyBean.getAgencyDetail(AGENCY_ID);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(AgencyDetailResultObject.StatusCode.SUCCESS, resultObject.getStatusCode());
    }

    private StudioAgencyDto mockStudioAgencyDto() {
        StudioAgencyDto studioAgencyDto = new StudioAgencyDto();
        studioAgencyDto.setAgencyNo(1);
        studioAgencyDto.setAgencyName("Test Agency");
        studioAgencyDto.setActivated(true);
        return studioAgencyDto;
    }

}
