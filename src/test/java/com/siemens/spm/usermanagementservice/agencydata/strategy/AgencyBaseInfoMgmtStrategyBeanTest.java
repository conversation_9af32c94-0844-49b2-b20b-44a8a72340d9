/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AgencyBaseInfoMgmtStrategyBeanTest.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.agencydata.strategy;

import com.siemens.spm.common.util.BeanFinder;
import com.siemens.spm.usermanagementservice.agencydata.vo.AgencyEventVO;
import com.siemens.spm.usermanagementservice.common.ChangeTypeEnum;
import com.siemens.spm.usermanagementservice.config.SpmConfig;
import com.siemens.spm.usermanagementservice.repository.AgencySettingsRepository;
import com.siemens.spm.usermanagementservice.util.BeanFinderMocker;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AgencyBaseInfoMgmtStrategyBeanTest {

    @InjectMocks
    AgencyBaseInfoMgmtStrategyBean agencyBaseInfoMgmtStrategyBean;

    @Mock
    AgencySettingsRepository agencySettingsRepository;

    @Mock
    SpmConfig spmConfig;

    private MockedStatic<BeanFinder> beanFinderMock;

    @BeforeEach
    public void initClass() {
        beanFinderMock = Mockito.mockStatic(BeanFinder.class);
        beanFinderMock.when(BeanFinder::getDefaultObjectMapper).thenReturn(BeanFinderMocker.objectMapper());
        beanFinderMock.when(BeanFinder::getDefaultRestTemplate).thenReturn(BeanFinderMocker.restTemplate());
    }

    @AfterEach
    public void afterClass() {
        beanFinderMock.close();
    }
}