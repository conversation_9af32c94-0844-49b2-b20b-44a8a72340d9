package com.siemens.spm.usermanagementservice.agencydata.strategy;

import com.siemens.spm.common.message.MessageService;
import com.siemens.spm.common.shared.vo.SimpleResultObject;
import com.siemens.spm.common.util.BeanFinder;
import com.siemens.spm.rule.api.intercom.AlarmCategoryInterComController;
import com.siemens.spm.rule.api.vo.response.AlarmCategoryListResponseVO;
import com.siemens.spm.usermanagementservice.api.vo.request.SetAgencySettingsRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.AgencySettingsResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.AgencySettingsResultObject.AgencySettingsStatusCode;
import com.siemens.spm.usermanagementservice.config.SpmConfig;
import com.siemens.spm.usermanagementservice.repository.AgencySettingsRepository;
import com.siemens.spm.usermanagementservice.repository.IntersectionRepository;
import com.siemens.spm.usermanagementservice.util.BeanFinderMocker;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.http.ResponseEntity;

import java.util.Collections;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.atLeast;

class AgencyMgmtStrategyTest {

    @InjectMocks
    private AgencyMgmtStrategy agencyMgmtStrategy;

    @Mock
    private AgencySettingsRepository agencySettingsRepo;

    @Mock
    private IntersectionRepository intersectionRepository;

    @Mock
    private SpmConfig spmConfig;

    @Mock
    private MessageService translator;

    private static MockedStatic<BeanFinder> beanFinderMock;

    private static final Integer AGENCY_ID = 1;
    private static final String INT_UUID = "4515820a-429c-4705-9b7d-f8101ed48856";

    @BeforeEach
    void beforeEach() {
        MockitoAnnotations.openMocks(this);
    }

    @BeforeAll
    static void initClass() {
        beanFinderMock = Mockito.mockStatic(BeanFinder.class);
        beanFinderMock.when(BeanFinder::getDefaultObjectMapper).thenReturn(BeanFinderMocker.objectMapper());
    }

    @AfterAll
    static void afterClass() {
        beanFinderMock.close();
    }

    @Test
    void test_getAgencySettings_error() {
        AgencySettingsResultObject resultObject = agencyMgmtStrategy.getAgencySettings(AGENCY_ID);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(AgencySettingsStatusCode.UNKNOWN_ERROR, resultObject.getStatusCode());
        Assertions.assertNull(resultObject.getData());
    }

    @Test
    void test_getMapSettings_success() {
        Mockito.when(spmConfig.getRuleServiceEndpoint()).thenReturn("rule-endpoint");

        try (MockedStatic<AlarmCategoryInterComController> alarmCategoryInterComControllerMocked =
                Mockito.mockStatic(AlarmCategoryInterComController.class)) {
            AlarmCategoryListResponseVO responseVO = new AlarmCategoryListResponseVO(
                    new AlarmCategoryListResponseVO.ResponseData(0L, Collections.emptyList()),
                    AlarmCategoryListResponseVO.StatusCode.SUCCESS);
            alarmCategoryInterComControllerMocked
                    .when(() -> AlarmCategoryInterComController.invokeGetAlarmCategories(anyString(), any()))
                    .thenReturn(ResponseEntity.ok(responseVO));

            AgencySettingsResultObject resultObject = agencyMgmtStrategy.getMapSettings(AGENCY_ID);

            Assertions.assertNotNull(resultObject);
            Assertions.assertEquals(AgencySettingsStatusCode.SUCCESS, resultObject.getStatusCode());
            Assertions.assertNotNull(resultObject.getData());

            Mockito.verify(translator, atLeast(1)).getMessage(anyString());
        }
    }

    @Test
    void test_setAgencySettings_unknownError() {
        SetAgencySettingsRequestVO requestVO = new SetAgencySettingsRequestVO();
        AgencySettingsResultObject resultObject = agencyMgmtStrategy.setAgencySettings(AGENCY_ID, requestVO);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(AgencySettingsStatusCode.UNKNOWN_ERROR, resultObject.getStatusCode());
        Assertions.assertNull(resultObject.getData());
    }

    @Test
    void test_setAgencySettings_success() {
        Mockito.when(agencySettingsRepo.findById(anyInt())).thenReturn(Optional.empty());
        Mockito.when(spmConfig.getRuleServiceEndpoint()).thenReturn("rule-endpoint");

        try (MockedStatic<AlarmCategoryInterComController> alarmCategoryInterComControllerMocked =
                Mockito.mockStatic(AlarmCategoryInterComController.class)) {
            alarmCategoryInterComControllerMocked
                    .when(() -> AlarmCategoryInterComController.invokeUpdateAlarmCategories(anyString(), any()))
                    .thenReturn(ResponseEntity.ok(new SimpleResultObject(SimpleResultObject.SimpleStatusCode.SUCCESS)));

            SetAgencySettingsRequestVO requestVO = new SetAgencySettingsRequestVO();
            AgencySettingsResultObject resultObject = agencyMgmtStrategy.setAgencySettings(AGENCY_ID, requestVO);

            Assertions.assertNotNull(resultObject);
            Assertions.assertEquals(AgencySettingsStatusCode.NO_CONTENT, resultObject.getStatusCode());
            Assertions.assertNull(resultObject.getData());
        }
    }

}
