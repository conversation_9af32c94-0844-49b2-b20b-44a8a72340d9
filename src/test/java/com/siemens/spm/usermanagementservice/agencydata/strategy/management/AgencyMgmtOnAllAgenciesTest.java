//package com.siemens.spm.usermanagementservice.agencydata.strategy.management;
//
//import java.util.Optional;
//
//import com.siemens.spm.usermanagementservice.api.vo.request.AgencyUpdatedRequestVO;
//import com.siemens.spm.usermanagementservice.api.vo.response.AgencyResultObject;
//import com.siemens.spm.usermanagementservice.api.vo.response.AgencyResultObject.AgencyStatusCode;
//import com.siemens.spm.usermanagementservice.domain.Agency;
//import com.siemens.spm.usermanagementservice.repository.AgencyRepository;
//import org.junit.jupiter.api.Assertions;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.mockito.MockitoAnnotations;
//
//import static org.mockito.ArgumentMatchers.anyString;
//
//class AgencyMgmtOnAllAgenciesTest {
//
//    @InjectMocks
//    private AgencyMgmtOnAllAgencies agencyMgmtOnAllAgencies;
//
//    @Mock
//    private AgencyRepository agencyRepository;
//
//    private static final String AGENCY_ID = "45298a51-4d20-45bf-be87-0e17de8245bb";
//
//    @BeforeEach
//    void beforeEach() {
//        MockitoAnnotations.openMocks(this);
//    }
//
//    @Test
//    void test_updateAgency_agencyNotFound() {
//        Mockito.when(agencyRepository.findById(anyString())).thenReturn(Optional.empty());
//
//        AgencyResultObject resultObject = agencyMgmtOnAllAgencies.updateAgency(AGENCY_ID, new AgencyUpdatedRequestVO());
//        Assertions.assertNotNull(resultObject);
//        Assertions.assertEquals(AgencyStatusCode.AGENCY_NOT_FOUND, resultObject.getStatusCode());
//    }
//
//    @Test
//    void test_updateAgency_invalidAgencyName() {
//        Mockito.when(agencyRepository.findById(anyString()))
//                .thenReturn(Optional.of(mockAgency(AGENCY_ID)));
//
//        AgencyResultObject resultObject = agencyMgmtOnAllAgencies.updateAgency(AGENCY_ID, new AgencyUpdatedRequestVO());
//        Assertions.assertNotNull(resultObject);
//        Assertions.assertEquals(AgencyStatusCode.INVALID_AGENCY_NAME, resultObject.getStatusCode());
//    }
//
//    @Test
//    void test_updateAgency_agencyNameAlreadyUsed() {
//        Mockito.when(agencyRepository.findById(anyString()))
//                .thenReturn(Optional.of(mockAgency(AGENCY_ID)));
//        Mockito.when(agencyRepository.existsByName(anyString()))
//                .thenReturn(true);
//
//        AgencyUpdatedRequestVO requestVO = AgencyUpdatedRequestVO.builder()
//                .name("Lawrence")
//                .build();
//        AgencyResultObject resultObject = agencyMgmtOnAllAgencies.updateAgency(AGENCY_ID, requestVO);
//
//        Assertions.assertNotNull(resultObject);
//        Assertions.assertEquals(AgencyStatusCode.AGENCYNAME_ALREADY_USED, resultObject.getStatusCode());
//    }
//
//    @Test
//    void test_updateAgency_successNoContent() {
//        Mockito.when(agencyRepository.findById(anyString()))
//                .thenReturn(Optional.of(mockAgency(AGENCY_ID)));
//        Mockito.when(agencyRepository.existsByName(anyString()))
//                .thenReturn(false);
//
//        AgencyUpdatedRequestVO requestVO = AgencyUpdatedRequestVO.builder()
//                .name("Lawrence")
//                .build();
//        AgencyResultObject resultObject = agencyMgmtOnAllAgencies.updateAgency(AGENCY_ID, requestVO);
//
//        Assertions.assertNotNull(resultObject);
//        Assertions.assertEquals(AgencyStatusCode.NO_CONTENT, resultObject.getStatusCode());
//    }
//
//    private Agency mockAgency(String agencyId) {
//        return Agency.builder()
//                .id(agencyId)
//                .name("Agency Name")
//                .address("Huong Canh, Binh Xuyen, Vinh Phuc")
//                .build();
//    }
//
//}
