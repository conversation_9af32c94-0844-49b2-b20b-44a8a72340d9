/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : SpringBootTestLazyContextBootstrapper.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice;

import org.springframework.boot.test.context.SpringBootTestContextBootstrapper;
import org.springframework.test.context.ContextLoader;

/**
 * <AUTHOR>
 *
 */
public class SpringBootTestLazyContextBootstrapper extends SpringBootTestContextBootstrapper {
    @Override
    protected Class<? extends ContextLoader> getDefaultContextLoaderClass(Class<?> testClass) {
        return SpringBootLazyContextLoader.class;
    }
}
