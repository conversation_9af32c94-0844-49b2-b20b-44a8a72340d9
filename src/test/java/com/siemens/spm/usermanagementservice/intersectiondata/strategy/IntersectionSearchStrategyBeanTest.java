package com.siemens.spm.usermanagementservice.intersectiondata.strategy;

import com.siemens.spm.common.message.MessageService;
import com.siemens.spm.common.shared.domaintype.IntersectionStatus;
import com.siemens.spm.common.shared.exception.InvalidSortColumnException;
import com.siemens.spm.common.shared.exception.InvalidSortOrderException;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionSearchVO;
import com.siemens.spm.usermanagementservice.api.vo.request.IntersectionSearchRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionDetailResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionInternalSearchResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionSearchResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionSimpleListResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionStatusHistoriesResultObject;
import com.siemens.spm.usermanagementservice.config.SpmConfig;
import com.siemens.spm.usermanagementservice.dashboarddata.strategy.intercom.IntersectionPerflogStrategy;
import com.siemens.spm.usermanagementservice.domain.Intersection;
import com.siemens.spm.usermanagementservice.domain.IntersectionStatusHistory;
import com.siemens.spm.usermanagementservice.intersectiondata.util.IntersectionSearchHelper;
import com.siemens.spm.usermanagementservice.repository.IntersectionRepository;
import com.siemens.spm.usermanagementservice.repository.IntersectionStatusHistoryRepository;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;

class IntersectionSearchStrategyBeanTest {

    @InjectMocks
    private IntersectionSearchStrategyBean intersectionSearchStrategyBean;

    @Mock
    private IntersectionRepository intersectionRepository;

    @Mock
    private IntersectionStatusHistoryRepository intersectionStatusHistoryRepository;

    @Mock
    private MessageService translator;

    @Mock
    private IntersectionPerflogStrategy intersectionPerflogStrategy;

    @Mock
    private SpmConfig spmConfig;

    private static final int AGENCY_ID = 1;
    private static final String INT_UUID = "4515820a-429c-4705-9b7d-f8101ed48856";
    private static final String PERLOG_SERVICE_ENDPOINT = "http://localhost:8004";

    @BeforeEach
    void beforeEach() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void test_getIntersectionDetail_intersectionNotFound() {
        Mockito.when(intersectionRepository.findById(anyString())).thenReturn(Optional.empty());

        IntersectionDetailResultObject resultObject = intersectionSearchStrategyBean
                .getIntersectionDetail(AGENCY_ID, INT_UUID);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(IntersectionDetailResultObject.StatusCode.INTERSECTION_NOT_FOUND,
                resultObject.getStatusCode());
        Assertions.assertNull(resultObject.getData());
    }

    @Test
    void test_getIntersectionDetail_success() {
        Mockito.when(intersectionRepository.findById(anyString()))
                .thenReturn(Optional.of(mockIntersection()));
        IntersectionDetailResultObject resultObject = intersectionSearchStrategyBean
                .getIntersectionDetail(AGENCY_ID, INT_UUID);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(IntersectionDetailResultObject.StatusCode.SUCCESS,
                resultObject.getStatusCode());
        Assertions.assertNotNull(resultObject.getData());
    }

    @Test
    void test_searchIntersections_invalidSortColumn() {
        IntersectionSearchRequestVO searchRequest = IntersectionSearchRequestVO.builder()
                .agencyId(AGENCY_ID)
                .build();

        try (MockedStatic<IntersectionSearchHelper> intersectionSearchHelperMocked =
                Mockito.mockStatic(IntersectionSearchHelper.class)) {
            intersectionSearchHelperMocked.when(() -> IntersectionSearchHelper.createOrderBy(any()))
                    .thenThrow(InvalidSortColumnException.class);

            IntersectionSearchResultObject resultObject = intersectionSearchStrategyBean
                    .searchIntersections(searchRequest);

            Assertions.assertNotNull(resultObject);
            Assertions.assertEquals(IntersectionSearchResultObject.StatusCode.INVALID_SORT_COLUMN,
                    resultObject.getStatusCode());
        }
    }

    @Test
    void test_searchIntersections_invalidSortOrder() {
        IntersectionSearchRequestVO searchRequest = IntersectionSearchRequestVO.builder()
                .agencyId(AGENCY_ID)
                .build();

        try (MockedStatic<IntersectionSearchHelper> intersectionSearchHelperMocked =
                Mockito.mockStatic(IntersectionSearchHelper.class)) {
            intersectionSearchHelperMocked.when(() -> IntersectionSearchHelper.createOrderBy(any()))
                    .thenThrow(InvalidSortOrderException.class);

            IntersectionSearchResultObject resultObject = intersectionSearchStrategyBean
                    .searchIntersections(searchRequest);

            Assertions.assertNotNull(resultObject);
            Assertions.assertEquals(IntersectionSearchResultObject.StatusCode.INVALID_SORT_ORDER,
                    resultObject.getStatusCode());
        }
    }

    @Test
    void test_searchIntersections_successEmptyIntersection() {
        Mockito.when(intersectionRepository.findPageByFilter(any(), any(), anyInt(), anyInt()))
                .thenReturn(Collections.emptyList());
        Mockito.when(intersectionRepository.countTotalByFilter(any()))
                .thenReturn(10L);

        IntersectionSearchRequestVO searchRequest = IntersectionSearchRequestVO.builder()
                .agencyId(AGENCY_ID)
                .intersectionIds(List.of(INT_UUID))
                .shouldPaginate(true)
                .page(3)
                .size(10)
                .build();
        IntersectionSearchResultObject resultObject = intersectionSearchStrategyBean
                .searchIntersections(searchRequest);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(IntersectionSearchResultObject.StatusCode.SUCCESS,
                resultObject.getStatusCode());
        IntersectionSearchResultObject.ResponseData responseData = resultObject.getData();
        Assertions.assertNotNull(responseData);

        List<IntersectionSearchVO> intersections = responseData.getIntersections();

        Assertions.assertNotNull(intersections);
        Assertions.assertEquals(0, intersections.size());
        Assertions.assertEquals(10, responseData.getTotalCount());
    }

    @Test
    void test_searchIntersections_successHaveIntersection() {
        Mockito.when(intersectionRepository.findPageByFilter(any(), any(), anyInt(), anyInt()))
                .thenReturn(List.of(mockIntersection()));
        Mockito.when(intersectionRepository.countTotalByFilter(any()))
                .thenReturn(10L);
        Mockito.when(intersectionPerflogStrategy.searchIntersectionPerflog(any()))
                .thenReturn(
                        Collections.emptyMap()
                );

        IntersectionSearchRequestVO searchRequest = IntersectionSearchRequestVO.builder()
                .agencyId(AGENCY_ID)
                .intersectionIds(List.of(INT_UUID))
                .shouldPaginate(true)
                .page(1)
                .size(10)
                .build();

        IntersectionSearchResultObject resultObject = intersectionSearchStrategyBean
                .searchIntersections(searchRequest);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(IntersectionSearchResultObject.StatusCode.SUCCESS,
                resultObject.getStatusCode());

        IntersectionSearchResultObject.ResponseData responseData = resultObject.getData();
        Assertions.assertNotNull(responseData);
        Assertions.assertEquals(10, responseData.getTotalCount());
        Assertions.assertNotNull(responseData.getIntersections());
        Assertions.assertEquals(1, responseData.getIntersections().size());

        Mockito.verify(translator, Mockito.atLeast(1)).getMessage(anyString());
    }

    @Test
    void test_getAllSimpleIntersections_success() {
        Mockito.when(intersectionRepository.findAllByIdIn(anyList()))
                .thenReturn(List.of(mockIntersection()));

        IntersectionSimpleListResultObject resultObject =
                intersectionSearchStrategyBean.getAllSimpleIntersections(AGENCY_ID, null);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(IntersectionSimpleListResultObject.StatusCode.SUCCESS,
                resultObject.getStatusCode());
    }

    @Test
    void test_searchIntersectionsInternal_invalidSortColumn() {
        IntersectionSearchRequestVO searchRequest = IntersectionSearchRequestVO.builder().build();

        try (MockedStatic<IntersectionSearchHelper> intersectionSearchHelperMocked =
                Mockito.mockStatic(IntersectionSearchHelper.class)) {
            intersectionSearchHelperMocked.when(() -> IntersectionSearchHelper.createOrderBy(any()))
                    .thenThrow(InvalidSortColumnException.class);

            IntersectionInternalSearchResultObject resultObject = intersectionSearchStrategyBean
                    .searchIntersectionsInternal(searchRequest);
            Assertions.assertNotNull(resultObject);
            Assertions.assertEquals(IntersectionInternalSearchResultObject.StatusCode.INVALID_SORT_COLUMN,
                    resultObject.getStatusCode());
        }
    }

    @Test
    void test_searchIntersectionsInternal_invalidSortOrder() {
        IntersectionSearchRequestVO searchRequest = IntersectionSearchRequestVO.builder().build();

        try (MockedStatic<IntersectionSearchHelper> intersectionSearchHelperMocked =
                Mockito.mockStatic(IntersectionSearchHelper.class)) {
            intersectionSearchHelperMocked.when(() -> IntersectionSearchHelper.createOrderBy(any()))
                    .thenThrow(InvalidSortOrderException.class);

            IntersectionInternalSearchResultObject resultObject = intersectionSearchStrategyBean
                    .searchIntersectionsInternal(searchRequest);
            Assertions.assertNotNull(resultObject);
            Assertions.assertEquals(IntersectionInternalSearchResultObject.StatusCode.INVALID_SORT_ORDER,
                    resultObject.getStatusCode());
        }
    }

    @Test
    void test_searchIntersectionsInternal_success() {
        Mockito.when(intersectionRepository.findPageByFilter(any(), any(), anyInt(), anyInt()))
                .thenReturn(List.of(mockIntersection()));
        Mockito.when(intersectionRepository.countTotalByFilter(any()))
                .thenReturn(10L);

        IntersectionSearchRequestVO searchRequest = IntersectionSearchRequestVO.builder().build();
        IntersectionInternalSearchResultObject resultObject = intersectionSearchStrategyBean

                .searchIntersectionsInternal(searchRequest);
        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(IntersectionInternalSearchResultObject.StatusCode.SUCCESS,
                resultObject.getStatusCode());
    }

    @Test
    void test_getIntersectionStatusHistory_success() {
        Mockito.when(intersectionStatusHistoryRepository.findAllByIntersectionIdAndStatus(eq(INT_UUID),
                        eq(IntersectionStatus.UNAVAILABLE.getInsight()), any(PageRequest.class)))
                .thenReturn(new PageImpl<>(List.of(mock(IntersectionStatusHistory.class))));

        var intersectionStatusHistoryResultObject = intersectionSearchStrategyBean
                .getIntersectionStatusHistories(AGENCY_ID, INT_UUID, 0, 10);

        verify(intersectionStatusHistoryRepository).findAllByIntersectionIdAndStatus(eq(INT_UUID),
                eq(IntersectionStatus.UNAVAILABLE.getInsight()), any(PageRequest.class));

        Assertions.assertNotNull(intersectionStatusHistoryResultObject);
        Assertions.assertEquals(IntersectionStatusHistoriesResultObject.StatusCode.SUCCESS,
                intersectionStatusHistoryResultObject.getStatusCode());
    }

    private Intersection mockIntersection() {
        return Intersection.builder()
                .id(INT_UUID)
                .name("6Th & Champion")
                .build();
    }

}
