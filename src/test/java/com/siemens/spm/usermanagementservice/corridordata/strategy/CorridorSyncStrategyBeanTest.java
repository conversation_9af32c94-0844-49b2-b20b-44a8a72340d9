/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : CorridorSyncStrategyBeanTest.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.corridordata.strategy;

import com.siemens.spm.usermanagementservice.domain.Corridor;
import com.siemens.spm.usermanagementservice.domain.CorridorIntersection;
import com.siemens.spm.usermanagementservice.domain.Intersection;
import com.siemens.spm.usermanagementservice.repository.CorridorIntersectionRepository;
import com.siemens.spm.usermanagementservice.repository.CorridorRepository;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Set;

import static org.mockito.ArgumentMatchers.any;

@ExtendWith(MockitoExtension.class)
class CorridorSyncStrategyBeanTest {

    @InjectMocks
    CorridorSyncStrategyBean corridorSyncStrategyBean;

    @Mock
    CorridorRepository corridorRepository;

    @Mock
    CorridorIntersectionRepository corridorIntersectionRepository;

    @Test
    void syncCorridorStatusByIntersectionInactiveIds_givenListIds_returnSavedCorridors() {

        Set<String> inactiveIds = Set.of("intersectionId");

        Corridor corridor = Corridor.builder()
                .id("corridorId")
                .build();

        Intersection intersection = Intersection.builder()
                .id("intersectionId")
                .build();

        CorridorIntersection corridorIntersection = CorridorIntersection.builder()
                .corridor(corridor)
                .intersection(intersection)
                .build();

        Mockito.when(corridorIntersectionRepository.findAllByIntersectionIdIn(inactiveIds))
                .thenReturn(Set.of(corridorIntersection));

        Mockito.when(corridorRepository.saveAll(any()))
                .thenAnswer(invocation -> invocation.getArgument(0));

        List<Corridor> result = corridorSyncStrategyBean.syncCorridorStatusByIntersectionInactiveIds(inactiveIds);

        Assertions.assertEquals("corridorId", result.get(0).getId());
    }
}