package com.siemens.spm.usermanagementservice.corridordata.strategy;

import com.siemens.spm.common.shared.domaintype.CorridorStatus;
import com.siemens.spm.common.shared.domaintype.IntersectionStatus;
import com.siemens.spm.common.util.BeanFinder;
import com.siemens.spm.datahub.api.boundary.DataIntegrationService;
import com.siemens.spm.datahub.api.vo.DataHubIntersectionVO;
import com.siemens.spm.datahub.api.vo.response.DataHubIntersectionResponseVO;
import com.siemens.spm.datahub.exception.DataHubIntersectionsException;
import com.siemens.spm.spmstudiosdk.dto.StudioAgencyDto;
import com.siemens.spm.spmstudiosdk.exception.StudioException;
import com.siemens.spm.spmstudiosdk.service.StudioAgencyService;
import com.siemens.spm.usermanagementservice.api.vo.request.CorridorCreateRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.CorridorIntersectionCreateRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.CorridorStatusUpdateRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.CorridorUpdateRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.CorridorResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.CorridorResultObject.CorridorStatusCode;
import com.siemens.spm.usermanagementservice.domain.Corridor;
import com.siemens.spm.usermanagementservice.domain.CorridorIntersection;
import com.siemens.spm.usermanagementservice.domain.Intersection;
import com.siemens.spm.usermanagementservice.repository.CorridorIntersectionRepository;
import com.siemens.spm.usermanagementservice.repository.CorridorRepository;
import com.siemens.spm.usermanagementservice.repository.IntersectionRepository;
import com.siemens.spm.usermanagementservice.util.BeanFinderMocker;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;

class CorridorManagementStrategyBeanTest {

    @InjectMocks
    private CorridorManagementStrategyBean corridorManagementStrategyBean;

    @Mock
    private IntersectionRepository intersectionRepository;

    @Mock
    private CorridorRepository corridorRepository;

    @Mock
    private CorridorIntersectionRepository corridorIntersectionRepository;

    @Mock
    private StudioAgencyService studioAgencyService;

    @Mock
    private DataIntegrationService dataIntegrationService;

    private static MockedStatic<BeanFinder> beanFinderMock;

    private static final int AGENCY_ID = 1;
    private static final String FIRST_INTERSECTION_ID = "b4558d34-d636-4f85-93f0-1dbe3c2fc89c";
    private static final String SECOND_INTERSECTION_ID = "a27d896f-cdfb-4014-a7f5-6bd563797379";
    private static final String CORRIDOR_ID = "ac14e433-6606-4180-be51-66392fcab6fa";
    private static final String CORRIDOR_INTERSECTION_ID = "bffecfc8-a6c5-4a63-b524-54dae45d7940";

    @BeforeEach
    void beforeEach() {
        MockitoAnnotations.openMocks(this);
    }

    @BeforeAll
    static void initClass() {
        beanFinderMock = Mockito.mockStatic(BeanFinder.class);
        beanFinderMock.when(BeanFinder::getDefaultObjectMapper).thenReturn(BeanFinderMocker.objectMapper());
    }

    @AfterAll
    static void afterClass() {
        beanFinderMock.close();
    }

    @Test
    void test_createCorridor_success() throws StudioException, DataHubIntersectionsException {
        DataHubIntersectionResponseVO responseVO = new DataHubIntersectionResponseVO();
        responseVO.setItems(List.of(mockDataHubIntersectionVO(FIRST_INTERSECTION_ID), mockDataHubIntersectionVO(SECOND_INTERSECTION_ID)));
        responseVO.setTotalItems(2L);

        Mockito.when(dataIntegrationService.getAllIntersections(AGENCY_ID))
                .thenReturn(responseVO);

        Mockito.when(intersectionRepository.findAllByIdIn(Mockito.anyList()))
                .thenReturn(
                        List.of(this.initIntersection(FIRST_INTERSECTION_ID),
                                this.initIntersection(SECOND_INTERSECTION_ID))
                );

        Mockito.when(studioAgencyService.getAgencyById(AGENCY_ID)).thenReturn(Optional.of(mockStudioAgencyDto()));
        Mockito.when(corridorRepository.save(Mockito.any()))
                .thenReturn(this.initCorridor());

        Mockito.when(corridorIntersectionRepository.saveAll(Mockito.any()))
                .thenReturn(List.of(this.initCorridorIntersection(), this.initCorridorIntersection()));

        CorridorCreateRequestVO createRequestVO = this.initCorridorCreateRequestVO();
        CorridorResultObject resultObject = corridorManagementStrategyBean.createCorridor(createRequestVO);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(CorridorStatusCode.CREATED, resultObject.getStatusCode());

        Mockito.verify(corridorRepository, times(1)).save(any());
        Mockito.verify(corridorIntersectionRepository, times(1)).saveAll(any());
    }

    @Test
    void test_createCorridor_corridorNameDuplicated() throws DataHubIntersectionsException {
        Mockito.when(corridorRepository.existsByName(Mockito.anyString()))
                .thenReturn(true);

        CorridorCreateRequestVO createRequestVO = this.initCorridorCreateRequestVO();
        CorridorResultObject resultObject = corridorManagementStrategyBean.createCorridor(createRequestVO);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(CorridorStatusCode.CORRIDOR_NAME_DUPLICATED, resultObject.getStatusCode());
    }

    @Test
    void test_createCorridor_agencyNotFound() throws DataHubIntersectionsException {
        DataHubIntersectionResponseVO responseVO = new DataHubIntersectionResponseVO();
        responseVO.setItems(List.of(mockDataHubIntersectionVO(FIRST_INTERSECTION_ID), mockDataHubIntersectionVO(SECOND_INTERSECTION_ID)));
        responseVO.setTotalItems(2L);

        Mockito.when(dataIntegrationService.getAllIntersections(AGENCY_ID))
                .thenReturn(responseVO);
        Mockito.when(corridorRepository.existsByName(Mockito.anyString()))
                .thenReturn(false);

        Mockito.when(intersectionRepository.findAllByIdIn(Mockito.anyList()))
                .thenReturn(
                        List.of(this.initIntersection(FIRST_INTERSECTION_ID),
                                this.initIntersection(SECOND_INTERSECTION_ID))
                );

        CorridorCreateRequestVO createRequestVO = this.initCorridorCreateRequestVO();
        CorridorResultObject resultObject = corridorManagementStrategyBean.createCorridor(createRequestVO);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(CorridorStatusCode.AGENCY_NOT_FOUND, resultObject.getStatusCode());
    }

    @Test
    void test_createCorridor_intersectionsNotFound() throws DataHubIntersectionsException {
        Mockito.when(corridorRepository.existsByName(Mockito.anyString()))
                .thenReturn(false);

        Mockito.when(intersectionRepository.findAllByIdIn(Mockito.anyList()))
                .thenReturn(Collections.emptyList());

        CorridorCreateRequestVO createRequestVO = this.initCorridorCreateRequestVO();
        CorridorResultObject resultObject = corridorManagementStrategyBean.createCorridor(createRequestVO);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(CorridorStatusCode.INTERSECTION_NOT_FOUND, resultObject.getStatusCode());
    }

    @Test
    void test_createCorridor_containsDisabledIntersection() throws DataHubIntersectionsException {
        Mockito.when(corridorRepository.existsByName(Mockito.anyString()))
                .thenReturn(false);

        Mockito.when(intersectionRepository.findAllByIdIn(Mockito.anyList()))
                .thenReturn(
                        List.of(this.initIntersectionWithStatus(FIRST_INTERSECTION_ID,
                                        IntersectionStatus.AVAILABLE.name()),
                                this.initIntersectionWithStatus(SECOND_INTERSECTION_ID,
                                        IntersectionStatus.UNAVAILABLE.name()))
                );

        CorridorCreateRequestVO createRequestVO = this.initCorridorCreateRequestVO();
        CorridorResultObject resultObject = corridorManagementStrategyBean.createCorridor(createRequestVO);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(CorridorStatusCode.CORRIDOR_CONTAINS_DISABLED_INTERSECTIONS,
                resultObject.getStatusCode());
    }

    @Test
    void test_updateCorridor_success() throws DataHubIntersectionsException {
        DataHubIntersectionResponseVO responseVO = new DataHubIntersectionResponseVO();
        responseVO.setItems(List.of(mockDataHubIntersectionVO(FIRST_INTERSECTION_ID), mockDataHubIntersectionVO(SECOND_INTERSECTION_ID)));
        responseVO.setTotalItems(2L);

        Mockito.when(dataIntegrationService.getAllIntersections(AGENCY_ID))
                .thenReturn(responseVO);
        Mockito.when(corridorRepository.findById(Mockito.anyString()))
                .thenReturn(Optional.of(this.initCorridor()));

        Mockito.when(corridorRepository.existsByName(Mockito.anyString()))
                .thenReturn(false);

        Mockito.when(intersectionRepository.findAllByIdIn(Mockito.anyList()))
                .thenReturn(
                        List.of(this.initIntersection(FIRST_INTERSECTION_ID),
                                this.initIntersection(SECOND_INTERSECTION_ID))
                );

        Mockito.when(corridorRepository.save(Mockito.any()))
                .thenReturn(this.initCorridor());

        Mockito.when(corridorIntersectionRepository.saveAll(Mockito.any()))
                .thenReturn(List.of(this.initCorridorIntersection()));

        CorridorIntersectionCreateRequestVO firstCorridorIntersection = this.initCorridorIntersectionCreateRequestVO(
                FIRST_INTERSECTION_ID);
        CorridorIntersectionCreateRequestVO lastCorridorIntersection = this.initSecondCorridorIntersectionCreateRequestVO(
                SECOND_INTERSECTION_ID);

        CorridorUpdateRequestVO updateRequestVO = this.initCorridorUpdateRequestVO(
                List.of(firstCorridorIntersection, lastCorridorIntersection)
        );

        CorridorResultObject resultObject = corridorManagementStrategyBean.updateCorridor(CORRIDOR_ID, updateRequestVO);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(CorridorStatusCode.SUCCESS, resultObject.getStatusCode());
    }

    @Test
    void test_updateCorridor_successWithCorridorIntersectionEmpty() throws DataHubIntersectionsException {
        DataHubIntersectionResponseVO responseVO = new DataHubIntersectionResponseVO();
        responseVO.setItems(List.of(mockDataHubIntersectionVO(FIRST_INTERSECTION_ID), mockDataHubIntersectionVO(SECOND_INTERSECTION_ID)));
        responseVO.setTotalItems(2L);

        Mockito.when(dataIntegrationService.getAllIntersections(AGENCY_ID))
                .thenReturn(responseVO);
        Mockito.when(corridorRepository.findById(Mockito.anyString()))
                .thenReturn(Optional.of(this.initCorridor()));

        Mockito.when(corridorRepository.existsByName(Mockito.anyString()))
                .thenReturn(false);

        Mockito.when(intersectionRepository.findAllByIdIn(Mockito.anyList()))
                .thenReturn(
                        List.of(this.initIntersection(FIRST_INTERSECTION_ID),
                                this.initIntersection(SECOND_INTERSECTION_ID))
                );

        Mockito.when(corridorRepository.save(Mockito.any()))
                .thenReturn(this.initCorridor());

        Mockito.when(corridorIntersectionRepository.saveAll(Mockito.any()))
                .thenReturn(List.of(this.initCorridorIntersection()));

        CorridorIntersectionCreateRequestVO firstCorridorIntersection = this.initCorridorIntersectionCreateRequestVO(
                FIRST_INTERSECTION_ID);
        CorridorIntersectionCreateRequestVO lastCorridorIntersection = this.initSecondCorridorIntersectionCreateRequestVO(
                SECOND_INTERSECTION_ID);

        CorridorUpdateRequestVO updateRequestVO = this.initCorridorUpdateRequestVO(
                List.of(firstCorridorIntersection, lastCorridorIntersection)
        );
        CorridorResultObject resultObject = corridorManagementStrategyBean.updateCorridor(CORRIDOR_ID, updateRequestVO);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(CorridorStatusCode.SUCCESS, resultObject.getStatusCode());
    }

    @Test
    void test_updateCorridor_whenCorridorIntersectionSizeLessThanTwo() throws DataHubIntersectionsException {
        Mockito.when(corridorRepository.findById(Mockito.anyString()))
                .thenReturn(Optional.of(this.initCorridor()));

        Mockito.when(corridorRepository.existsByName(Mockito.anyString()))
                .thenReturn(false);

        CorridorIntersectionCreateRequestVO firstCorridorIntersection = this.initCorridorIntersectionCreateRequestVO(
                FIRST_INTERSECTION_ID);

        CorridorUpdateRequestVO updateRequestVO = this.initCorridorUpdateRequestVO(
                List.of(firstCorridorIntersection)
        );

        CorridorResultObject resultObject = corridorManagementStrategyBean.updateCorridor(CORRIDOR_ID, updateRequestVO);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(CorridorStatusCode.INVALID_CORRIDOR_INTERSECTIONS_SIZE, resultObject.getStatusCode());
    }

    @Test
    void test_updateCorridor_whenLastCorridorIntersectionDistanceDifferentZero() throws DataHubIntersectionsException {
        Mockito.when(corridorRepository.findById(Mockito.anyString()))
                .thenReturn(Optional.of(this.initCorridor()));

        Mockito.when(corridorRepository.existsByName(Mockito.anyString()))
                .thenReturn(false);

        CorridorIntersectionCreateRequestVO firstCorridorIntersection = this.initCorridorIntersectionCreateRequestVO(
                FIRST_INTERSECTION_ID);
        CorridorIntersectionCreateRequestVO lastCorridorIntersection = this.initCorridorIntersectionCreateRequestVO(
                FIRST_INTERSECTION_ID);

        CorridorUpdateRequestVO updateRequestVO = this.initCorridorUpdateRequestVO(
                List.of(firstCorridorIntersection, lastCorridorIntersection)
        );

        CorridorResultObject resultObject = corridorManagementStrategyBean.updateCorridor(CORRIDOR_ID, updateRequestVO);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(CorridorStatusCode.INVALID_LAST_CORRIDOR_INTERSECTION_DISTANCE,
                resultObject.getStatusCode());
    }

    @Test
    void test_updateCorridor_corridorNotFound() throws DataHubIntersectionsException {
        Mockito.when(corridorRepository.findById(Mockito.anyString()))
                .thenReturn(Optional.empty());

        CorridorIntersectionCreateRequestVO firstCorridorIntersection = this.initCorridorIntersectionCreateRequestVO(
                FIRST_INTERSECTION_ID);
        CorridorIntersectionCreateRequestVO lastCorridorIntersection = this.initSecondCorridorIntersectionCreateRequestVO(
                SECOND_INTERSECTION_ID);

        CorridorUpdateRequestVO updateRequestVO = this.initCorridorUpdateRequestVO(
                List.of(firstCorridorIntersection, lastCorridorIntersection)
        );
        CorridorResultObject resultObject = corridorManagementStrategyBean.updateCorridor(CORRIDOR_ID, updateRequestVO);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(CorridorStatusCode.CORRIDOR_NOT_FOUND, resultObject.getStatusCode());
    }

    @Test
    void test_updateCorridor_corridorNameDuplicate() throws DataHubIntersectionsException {
        Mockito.when(corridorRepository.findById(Mockito.anyString()))
                .thenReturn(Optional.of(this.initCorridor()));

        Mockito.when(corridorRepository.existsByName(Mockito.anyString()))
                .thenReturn(true);

        CorridorIntersectionCreateRequestVO firstCorridorIntersection = this.initCorridorIntersectionCreateRequestVO(
                FIRST_INTERSECTION_ID);
        CorridorIntersectionCreateRequestVO lastCorridorIntersection = this.initSecondCorridorIntersectionCreateRequestVO(
                SECOND_INTERSECTION_ID);

        CorridorUpdateRequestVO updateRequestVO = this.initCorridorUpdateRequestVO(
                List.of(firstCorridorIntersection, lastCorridorIntersection)
        );
        CorridorResultObject resultObject = corridorManagementStrategyBean.updateCorridor(CORRIDOR_ID, updateRequestVO);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(CorridorStatusCode.CORRIDOR_NAME_DUPLICATED, resultObject.getStatusCode());
    }

    @Test
    void test_updateCorridor_intersectionNotFound() throws DataHubIntersectionsException {
        Mockito.when(corridorRepository.findById(Mockito.anyString()))
                .thenReturn(Optional.of(this.initCorridor()));

        Mockito.when(corridorRepository.existsByName(Mockito.anyString()))
                .thenReturn(false);

        Mockito.when(intersectionRepository.findAllByIdIn(Mockito.anyList()))
                .thenReturn(Collections.emptyList());

        CorridorIntersectionCreateRequestVO firstCorridorIntersection = this.initCorridorIntersectionCreateRequestVO(
                FIRST_INTERSECTION_ID);
        CorridorIntersectionCreateRequestVO lastCorridorIntersection = this.initSecondCorridorIntersectionCreateRequestVO(
                SECOND_INTERSECTION_ID);

        CorridorUpdateRequestVO updateRequestVO = this.initCorridorUpdateRequestVO(
                List.of(firstCorridorIntersection, lastCorridorIntersection)
        );
        CorridorResultObject resultObject = corridorManagementStrategyBean.updateCorridor(CORRIDOR_ID, updateRequestVO);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(CorridorStatusCode.INTERSECTION_NOT_FOUND, resultObject.getStatusCode());
    }

    @Test
    void test_updateCorridor_containsDisableIntersection() throws DataHubIntersectionsException {
        Mockito.when(corridorRepository.existsByName(Mockito.anyString()))
                .thenReturn(false);

        Mockito.when(intersectionRepository.findAllByIdIn(Mockito.anyList()))
                .thenReturn(
                        List.of(this.initIntersectionWithStatus(FIRST_INTERSECTION_ID,
                                        IntersectionStatus.AVAILABLE.name()),
                                this.initIntersectionWithStatus(SECOND_INTERSECTION_ID,
                                        IntersectionStatus.UNAVAILABLE.name()))
                );

        CorridorCreateRequestVO createRequestVO = this.initCorridorCreateRequestVO();
        CorridorResultObject resultObject = corridorManagementStrategyBean.createCorridor(createRequestVO);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(CorridorStatusCode.CORRIDOR_CONTAINS_DISABLED_INTERSECTIONS,
                resultObject.getStatusCode());
    }

    @Test
    void test_updateCorridor_nameNull() throws DataHubIntersectionsException {
        Mockito.when(corridorRepository.findById(Mockito.anyString()))
                .thenReturn(Optional.of(this.initCorridor()));

        CorridorIntersectionCreateRequestVO firstCorridorIntersection = this.initCorridorIntersectionCreateRequestVO(
                FIRST_INTERSECTION_ID);
        CorridorUpdateRequestVO updateRequestVO = this.initCorridorUpdateRequestVO(
                List.of(firstCorridorIntersection)
        );
        updateRequestVO.setName(null);
        CorridorResultObject resultObject = corridorManagementStrategyBean.updateCorridor(CORRIDOR_ID, updateRequestVO);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(CorridorStatusCode.NAME_NOT_NULL, resultObject.getStatusCode());
    }

    @Test
    void test_updateCorridor_speedNull() throws DataHubIntersectionsException {
        Mockito.when(corridorRepository.findById(Mockito.anyString()))
                .thenReturn(Optional.of(this.initCorridor()));

        CorridorIntersectionCreateRequestVO firstCorridorIntersection = this.initCorridorIntersectionCreateRequestVO(
                FIRST_INTERSECTION_ID);
        CorridorUpdateRequestVO updateRequestVO = this.initCorridorUpdateRequestVO(
                List.of(firstCorridorIntersection)
        );
        updateRequestVO.setSpeed(null);
        CorridorResultObject resultObject = corridorManagementStrategyBean.updateCorridor(CORRIDOR_ID, updateRequestVO);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(CorridorStatusCode.SPEED_NOT_NULL, resultObject.getStatusCode());
    }

    @Test
    void test_updateCorridor_globalUpstreamNull() throws DataHubIntersectionsException {
        Mockito.when(corridorRepository.findById(Mockito.anyString()))
                .thenReturn(Optional.of(this.initCorridor()));

        CorridorIntersectionCreateRequestVO firstCorridorIntersection = this.initCorridorIntersectionCreateRequestVO(
                FIRST_INTERSECTION_ID);
        CorridorUpdateRequestVO updateRequestVO = this.initCorridorUpdateRequestVO(
                List.of(firstCorridorIntersection)
        );
        updateRequestVO.setGlobalUpstreamPhase(null);
        CorridorResultObject resultObject = corridorManagementStrategyBean.updateCorridor(CORRIDOR_ID, updateRequestVO);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(CorridorStatusCode.GLOBAL_UPSTREAM_NOT_NULL, resultObject.getStatusCode());
    }

    @Test
    void test_updateCorridor_globalDownstreamNull() throws DataHubIntersectionsException {
        Mockito.when(corridorRepository.findById(Mockito.anyString()))
                .thenReturn(Optional.of(this.initCorridor()));

        CorridorIntersectionCreateRequestVO firstCorridorIntersection = this.initCorridorIntersectionCreateRequestVO(
                FIRST_INTERSECTION_ID);
        CorridorUpdateRequestVO updateRequestVO = this.initCorridorUpdateRequestVO(
                List.of(firstCorridorIntersection)
        );
        updateRequestVO.setGlobalDownstreamPhase(null);
        CorridorResultObject resultObject = corridorManagementStrategyBean.updateCorridor(CORRIDOR_ID, updateRequestVO);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(CorridorStatusCode.GLOBAL_DOWNSTREAM_NOT_NULL, resultObject.getStatusCode());
    }

    @Test
    void test_updateCorridor_globalDownstreamGreaterSixteen() throws DataHubIntersectionsException {
        Mockito.when(corridorRepository.findById(Mockito.anyString()))
                .thenReturn(Optional.of(this.initCorridor()));

        CorridorIntersectionCreateRequestVO firstCorridorIntersection = this.initCorridorIntersectionCreateRequestVO(
                FIRST_INTERSECTION_ID);
        CorridorUpdateRequestVO updateRequestVO = this.initCorridorUpdateRequestVO(
                List.of(firstCorridorIntersection)
        );
        updateRequestVO.setGlobalDownstreamPhase(200);
        CorridorResultObject resultObject = corridorManagementStrategyBean.updateCorridor(CORRIDOR_ID, updateRequestVO);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(CorridorStatusCode.INVALID_STREAM_PHASE_NUMBER, resultObject.getStatusCode());
    }

    @Test
    void test_getCorridorDetail_success() {
        Corridor corridor = this.initCorridor();
        Mockito.when(corridorRepository.findById(Mockito.anyString()))
                .thenReturn(Optional.of(corridor));

        CorridorResultObject resultObject = corridorManagementStrategyBean.getCorridorDetail(CORRIDOR_ID);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(CorridorStatusCode.SUCCESS, resultObject.getStatusCode());
    }

    @Test
    void test_getCorridorDetail_notFound() {
        Mockito.when(corridorRepository.findById(Mockito.anyString()))
                .thenReturn(Optional.empty());

        CorridorResultObject resultObject = corridorManagementStrategyBean.getCorridorDetail(CORRIDOR_ID);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(CorridorStatusCode.CORRIDOR_NOT_FOUND, resultObject.getStatusCode());
    }

    @Test
    void test_updateCorridorStatus_success() {
        Corridor activeCorridor = this.initCorridor();

        Mockito.when(corridorRepository.findAllById(Mockito.anyList()))
                .thenReturn(List.of(activeCorridor));

        CorridorStatusUpdateRequestVO requestVO = CorridorStatusUpdateRequestVO.builder()
                .status(CorridorStatus.INACTIVE.name())
                .ids(List.of(CORRIDOR_ID))
                .build();
        CorridorResultObject resultObject =
                corridorManagementStrategyBean.updateCorridorStatus(requestVO);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(CorridorStatusCode.NO_CONTENT, resultObject.getStatusCode());
    }

    @Test
    void test_updateCorridorStatus_corridorNotFound() {
        Mockito.when(corridorRepository.findAllById(Mockito.anyList()))
                .thenReturn(Collections.emptyList());

        CorridorStatusUpdateRequestVO requestVO = CorridorStatusUpdateRequestVO.builder()
                .status(CorridorStatus.INACTIVE.name())
                .ids(List.of(CORRIDOR_ID))
                .build();

        CorridorResultObject resultObject =
                corridorManagementStrategyBean.updateCorridorStatus(requestVO);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(CorridorStatusCode.CORRIDOR_NOT_FOUND, resultObject.getStatusCode());
    }

    @Test
    void test_updateCorridorStatus_corridorStatusInvalid() {
        CorridorStatusUpdateRequestVO requestVO = CorridorStatusUpdateRequestVO.builder()
                .status("INVALID_STATUS")
                .build();

        CorridorResultObject resultObject =
                corridorManagementStrategyBean.updateCorridorStatus(requestVO);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(CorridorStatusCode.CORRIDOR_STATUS_INVALID, resultObject.getStatusCode());
    }

    @Test
    void test_updateCorridorStatusToActive_CorridorContainsDisabledIntersection() {
        Corridor activeCorridor = this.initCorridor();
        var intersection = initIntersectionWithStatus(FIRST_INTERSECTION_ID, IntersectionStatus.UNAVAILABLE.name());
        var corridorIntersection = initCorridorIntersection();
        corridorIntersection.setIntersection(intersection);

        activeCorridor.setCorridorIntersections(List.of(corridorIntersection));

        Mockito.when(corridorRepository.findAllById(Mockito.anyList()))
                .thenReturn(List.of(activeCorridor));

        CorridorStatusUpdateRequestVO requestVO = CorridorStatusUpdateRequestVO.builder()
                .status(CorridorStatus.ACTIVE.name())
                .ids(List.of(CORRIDOR_ID))
                .build();
        CorridorResultObject resultObject =
                corridorManagementStrategyBean.updateCorridorStatus(requestVO);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(CorridorStatusCode.CORRIDOR_CONTAINS_DISABLED_INTERSECTIONS,
                resultObject.getStatusCode());
    }

    private Intersection initIntersection(String intersectionId) {
        return Intersection.builder()
                .id(intersectionId)
                .build();
    }

    private Intersection initIntersectionWithStatus(String intersectionId, String status) {
        return Intersection.builder()
                .id(intersectionId)
                .status(status)
                .build();
    }

    private CorridorIntersectionCreateRequestVO initCorridorIntersectionCreateRequestVO(String intersectionId) {
        return CorridorIntersectionCreateRequestVO.builder()
                .number(1)
                .distance(12.0)
                .intersectionId(intersectionId)
                .build();
    }

    private CorridorIntersectionCreateRequestVO initSecondCorridorIntersectionCreateRequestVO(String intersectionId) {
        return CorridorIntersectionCreateRequestVO.builder()
                .number(2)
                .distance(0.0)
                .intersectionId(intersectionId)
                .build();
    }

    private CorridorUpdateRequestVO initCorridorUpdateRequestVO(List<CorridorIntersectionCreateRequestVO> corridorIntersectionVOS) {
        return CorridorUpdateRequestVO.builder()
                .name(RandomStringUtils.randomAlphabetic(15))
                .speed(12.0)
                .globalDownstreamPhase(1)
                .globalUpstreamPhase(14)
                .corridorIntersections(corridorIntersectionVOS)
                .build();
    }

    private CorridorCreateRequestVO initCorridorCreateRequestVO() {
        CorridorIntersectionCreateRequestVO firstCorridorIntersectionVO =
                this.initSecondCorridorIntersectionCreateRequestVO(FIRST_INTERSECTION_ID);
        CorridorIntersectionCreateRequestVO lastCorridorIntersectionVO =
                this.initSecondCorridorIntersectionCreateRequestVO(SECOND_INTERSECTION_ID);

        return CorridorCreateRequestVO.builder()
                .name(RandomStringUtils.randomAlphabetic(15))
                .speed(12.0)
                .globalUpstreamPhase(6)
                .globalDownstreamPhase(2)
                .agencyId(AGENCY_ID)
                .corridorIntersections(
                        List.of(firstCorridorIntersectionVO,
                                lastCorridorIntersectionVO
                        )
                )
                .build();
    }

    private Corridor initCorridor() {
        return Corridor.builder()
                .id(CORRIDOR_ID)
                .name(RandomStringUtils.randomAlphabetic(15))
                .status(CorridorStatus.ACTIVE.name())
                .agencyId(AGENCY_ID)
                .speed(12.0)
                .globalUpstreamPhase(6)
                .globalDownstreamPhase(2)
                .build();
    }

    private CorridorIntersection initCorridorIntersection() {
        return CorridorIntersection.builder()
                .id(CORRIDOR_INTERSECTION_ID)
                .distance(12.0)
                .speed(12.0)
                .number(1)
                .intersection(this.initIntersection(FIRST_INTERSECTION_ID))
                .build();
    }

    private StudioAgencyDto mockStudioAgencyDto() {
        StudioAgencyDto studioAgencyDto = new StudioAgencyDto();
        studioAgencyDto.setAgencyNo(1);
        studioAgencyDto.setAgencyName("Test Agency");
        studioAgencyDto.setActivated(true);
        return studioAgencyDto;
    }

    // Add helper method to create DataHubIntersectionVO
    private DataHubIntersectionVO mockDataHubIntersectionVO(String id) {
        DataHubIntersectionVO vo = new DataHubIntersectionVO();
        vo.setId(id);
        vo.setName("6TH & Champion");
        return vo;
    }

}
