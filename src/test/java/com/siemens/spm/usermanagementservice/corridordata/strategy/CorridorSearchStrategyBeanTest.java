package com.siemens.spm.usermanagementservice.corridordata.strategy;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import com.siemens.spm.common.shared.domaintype.CorridorStatus;
import com.siemens.spm.common.util.BeanFinder;
import com.siemens.spm.spmstudiosdk.dto.StudioAgencyDto;
import com.siemens.spm.spmstudiosdk.dto.StudioUserDto;
import com.siemens.spm.spmstudiosdk.service.StudioAgencyService;
import com.siemens.spm.spmstudiosdk.service.StudioUserService;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionInternalListVO;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionInternalVO;
import com.siemens.spm.usermanagementservice.api.vo.request.CorridorListRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.IntersectionAvailableSearchRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.CorridorListResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.CorridorListResultObject.CorridorsStatusCode;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionInternalSearchResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionInternalSearchResultObject.StatusCode;
import com.siemens.spm.usermanagementservice.domain.Corridor;
import com.siemens.spm.usermanagementservice.domain.CorridorIntersection;
import com.siemens.spm.usermanagementservice.domain.Intersection;
import com.siemens.spm.usermanagementservice.intersectiondata.strategy.IntersectionSearchStrategyBean;
import com.siemens.spm.usermanagementservice.repository.CorridorRepository;
import com.siemens.spm.usermanagementservice.util.BeanFinderMocker;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;

class CorridorSearchStrategyBeanTest {

    private static final int AGENCY_ID = 1;

    @InjectMocks
    private CorridorSearchStrategyBean corridorSearchStrategyBean;

    @Mock
    private CorridorRepository corridorRepository;

    @Mock
    private StudioUserService studioUserService;

    @Mock
    private StudioAgencyService studioAgencyService;

    @Mock
    private IntersectionSearchStrategyBean intersectionStrategyBean;

    private static MockedStatic<BeanFinder> beanFinderMock;

    @BeforeEach
    void beforeEach() {
        MockitoAnnotations.openMocks(this);
    }

    @BeforeAll
    static void initClass() {
        beanFinderMock = Mockito.mockStatic(BeanFinder.class);
        beanFinderMock.when(BeanFinder::getDefaultObjectMapper).thenReturn(BeanFinderMocker.objectMapper());
    }

    @AfterAll
    static void afterClass() {
        beanFinderMock.close();
    }

    @Test
    void test_getCorridors_success() {
        Corridor corridor = this.initCorridor();
        Mockito.when(corridorRepository.findPageByFilter(any(), any(), anyInt(), anyInt()))
                .thenReturn(List.of(corridor));

        Mockito.when(corridorRepository.countTotalByFilter(any()))
                .thenReturn(1L);

        CorridorListRequestVO requestVO = CorridorListRequestVO.builder()
                .page(0)
                .size(10)
                .text(corridor.getName())
                .agencyId(corridor.getAgencyId())
                .orderByColumns(new String[] { "name:ascend", "last_modified_at:descend", "status:ascend" })
                .status(CorridorStatus.ACTIVE.name())
                .build();
        CorridorListResultObject resultObject = corridorSearchStrategyBean.getCorridors(requestVO);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(CorridorsStatusCode.SUCCESS, resultObject.getStatusCode());
        Assertions.assertEquals(1, resultObject.getData().getTotalCount());
    }

    @Test
    void test_getCorridors_invalidSortColumnName() {
        Corridor corridor = this.initCorridor();
        Mockito.when(corridorRepository.findPageByFilter(any(), any(), anyInt(), anyInt()))
                .thenReturn(List.of(corridor));

        Mockito.when(corridorRepository.countTotalByFilter(any()))
                .thenReturn(1L);

        CorridorListRequestVO requestVO = CorridorListRequestVO.builder()
                .page(0)
                .size(10)
                .orderByColumns(new String[] { "invalid_column:ascend" })
                .build();
        CorridorListResultObject resultObject = corridorSearchStrategyBean.getCorridors(requestVO);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(CorridorsStatusCode.INVALID_SORT_COLUMN, resultObject.getStatusCode());
    }

    @Test
    void test_getCorridors_invalidSortOrder() {
        Corridor corridor = this.initCorridor();
        Mockito.when(corridorRepository.findPageByFilter(any(), any(), anyInt(), anyInt()))
                .thenReturn(List.of(corridor));

        Mockito.when(corridorRepository.countTotalByFilter(any()))
                .thenReturn(1L);

        CorridorListRequestVO requestVO = CorridorListRequestVO.builder()
                .orderByColumns(new String[] { "name:invalid_sort_order" })
                .build();
        CorridorListResultObject resultObject = corridorSearchStrategyBean.getCorridors(requestVO);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(CorridorsStatusCode.INVALID_SORT_ORDER, resultObject.getStatusCode());
    }

    @Test
    void test_searchAvailableIntersections_success() {
        Corridor corridor = this.initCorridor();
        corridor.setCorridorIntersections(
                List.of(CorridorIntersection.builder()
                        .id(UUID.randomUUID().toString())
                        .number(1)
                        .intersection(Intersection.builder()
                                .id(UUID.randomUUID().toString())
                                .build()
                        )
                        .build()
                )
        );

        Mockito.when(corridorRepository.findById(Mockito.anyString()))
                .thenReturn(Optional.of(corridor));

        IntersectionInternalListVO intersectionInternalListVO = this.initIntersectionInternalListVO();
        IntersectionInternalSearchResultObject mockResponse = new IntersectionInternalSearchResultObject(
                intersectionInternalListVO,
                StatusCode.SUCCESS);
        Mockito.when(intersectionStrategyBean.searchIntersectionsInternal(any()))
                .thenReturn(mockResponse);

        IntersectionAvailableSearchRequestVO requestVO = IntersectionAvailableSearchRequestVO.builder()
                .agencyId(AGENCY_ID)
                .corridorId(corridor.getId())
                .text(RandomStringUtils.randomAlphabetic(15))
                .page(0)
                .size(10)
                .build();

        IntersectionInternalSearchResultObject resultObject = corridorSearchStrategyBean.searchAvailableIntersections(
                requestVO);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(StatusCode.SUCCESS, resultObject.getStatusCode());
    }

    @Test
    void test_searchAvailableIntersections_successInCaseCorridorHasNoIntersection() {
        Corridor corridor = this.initCorridor();

        Mockito.when(corridorRepository.findById(Mockito.anyString()))
                .thenReturn(Optional.of(corridor));

        IntersectionInternalListVO intersectionInternalListVO = this.initIntersectionInternalListVO();
        IntersectionInternalSearchResultObject mockResponse = new IntersectionInternalSearchResultObject(
                intersectionInternalListVO,
                StatusCode.SUCCESS);
        Mockito.when(intersectionStrategyBean.searchIntersectionsInternal(any()))
                .thenReturn(mockResponse);

        IntersectionAvailableSearchRequestVO requestVO = IntersectionAvailableSearchRequestVO.builder()
                .agencyId(AGENCY_ID)
                .corridorId(corridor.getId())
                .text(RandomStringUtils.randomAlphabetic(15))
                .page(0)
                .size(10)
                .build();

        IntersectionInternalSearchResultObject resultObject = corridorSearchStrategyBean.searchAvailableIntersections(
                requestVO);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(StatusCode.SUCCESS, resultObject.getStatusCode());
    }

//    @Test
//    void test_searchAvailableIntersections_agencyNotFound() {
//        Corridor corridor = this.initCorridor();
//        IntersectionAvailableSearchRequestVO requestVO = IntersectionAvailableSearchRequestVO.builder()
//                .agencyId(AGENCY_ID)
//                .corridorId(corridor.getId())
//                .text(RandomStringUtils.randomAlphabetic(15))
//                .page(0)
//                .size(10)
//                .build();
//
//        IntersectionInternalSearchResultObject resultObject = corridorSearchStrategyBean.searchAvailableIntersections(
//                requestVO);
//
//        Assertions.assertNotNull(resultObject);
//        Assertions.assertEquals(StatusCode.AGENCY_NOT_FOUND, resultObject.getStatusCode());
//    }

    @Test
    void test_searchAvailableIntersections_corridorNotFound() {
        Corridor corridor = this.initCorridor();
        Mockito.when(corridorRepository.findById(Mockito.anyString()))
                .thenReturn(Optional.empty());

        IntersectionAvailableSearchRequestVO requestVO = IntersectionAvailableSearchRequestVO.builder()
                .agencyId(AGENCY_ID)
                .corridorId(corridor.getId())
                .page(0)
                .size(10)
                .build();

        IntersectionInternalSearchResultObject resultObject = corridorSearchStrategyBean.searchAvailableIntersections(
                requestVO);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(StatusCode.CORRIDOR_NOT_FOUND, resultObject.getStatusCode());
    }

    @Test
    void test_searchAvailableIntersections_corridorNotBelongToAgency() {
        Corridor corridor = this.initCorridor();
        corridor.setAgencyId(2);
        Mockito.when(corridorRepository.findById(Mockito.anyString()))
                .thenReturn(Optional.of(corridor));
        IntersectionAvailableSearchRequestVO requestVO = IntersectionAvailableSearchRequestVO.builder()
                .agencyId(AGENCY_ID)
                .corridorId(corridor.getId())
                .text(RandomStringUtils.randomAlphabetic(15))
                .page(0)
                .size(10)
                .build();

        IntersectionInternalSearchResultObject resultObject = corridorSearchStrategyBean.searchAvailableIntersections(
                requestVO);

        Assertions.assertNotNull(resultObject);
        Assertions.assertEquals(StatusCode.CORRIDOR_NOT_BELONG_TO_AGENCY, resultObject.getStatusCode());
    }

    private IntersectionInternalListVO initIntersectionInternalListVO() {
        return IntersectionInternalListVO.builder()
                .totalCount(1L)
                .intersections(
                        List.of(
                                IntersectionInternalVO.builder()
                                        .id(UUID.randomUUID().toString())
                                        .name(RandomStringUtils.randomAlphabetic(15))
                                        .build()
                        )
                )
                .build();
    }

    private Corridor initCorridor() {
        Corridor corridor = Corridor.builder()
                .id(UUID.randomUUID().toString())
                .name(RandomStringUtils.randomAlphabetic(15))
                .status(CorridorStatus.ACTIVE.name())
                .agencyId(AGENCY_ID)
                .speed(12.0)
                .globalUpstreamPhase(6)
                .globalDownstreamPhase(2)
                .build();
        corridor.setCreatedBy("<EMAIL>");
        corridor.setLastModifiedBy("<EMAIL>");
        return corridor;
    }

    private StudioAgencyDto mockStudioAgencyDto() {
        StudioAgencyDto studioAgencyDto = new StudioAgencyDto();
        studioAgencyDto.setAgencyNo(1);
        studioAgencyDto.setAgencyName("Test Agency");
        studioAgencyDto.setActivated(true);
        return studioAgencyDto;
    }

    private StudioUserDto mockStudioUserDto() {
        StudioUserDto studioUserDto = new StudioUserDto();
        studioUserDto.setIsEnabled(true);
        studioUserDto.setId(1);
        studioUserDto.setEmail("<EMAIL>");
        return studioUserDto;
    }

}
