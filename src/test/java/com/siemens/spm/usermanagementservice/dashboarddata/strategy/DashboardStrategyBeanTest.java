//package com.siemens.spm.usermanagementservice.dashboarddata.strategy;
//
//import com.siemens.spm.analysis.api.controller.InternalAnalysisController;
//import com.siemens.spm.analysis.api.vo.AnalysisStatisticsVO;
//import com.siemens.spm.analysis.api.vo.IntersectionStatisticsVO;
//import com.siemens.spm.analysis.api.vo.response.AnalysisStatisticsResultObject;
//import com.siemens.spm.common.constant.IntersectionOption;
//import com.siemens.spm.common.constant.NotificationConstants;
//import com.siemens.spm.common.message.MessageService;
//import com.siemens.spm.common.util.BeanFinder;
//import com.siemens.spm.datahub.api.boundary.DataIntegrationService;
//import com.siemens.spm.datahub.api.vo.DataHubIntersectionSearchRequestVO;
//import com.siemens.spm.datahub.api.vo.DataHubIntersectionVO;
//import com.siemens.spm.datahub.api.vo.response.DataHubIntersectionResponseVO;
//import com.siemens.spm.datahub.exception.DataHubException;
//import com.siemens.spm.perflogcrawler.api.intercom.PerfLogController;
//import com.siemens.spm.perflogcrawler.api.vo.IntersectionListVolumeCountVO;
//import com.siemens.spm.perflogcrawler.api.vo.IntersectionVolumeCountVO;
//import com.siemens.spm.perflogcrawler.api.vo.response.IntersectionVolumeCountResultObject;
//import com.siemens.spm.rule.api.intercom.AlarmRecordInterComController;
//import com.siemens.spm.rule.api.vo.response.AlarmRecordCountForEachIntersectionResponseVO;
//import com.siemens.spm.spmstudiosdk.dto.StudioAgencyDto;
//import com.siemens.spm.spmstudiosdk.dto.StudioUserDto;
//import com.siemens.spm.spmstudiosdk.exception.StudioException;
//import com.siemens.spm.spmstudiosdk.service.StudioAgencyService;
//import com.siemens.spm.spmstudiosdk.service.StudioUserService;
//import com.siemens.spm.usermanagementservice.api.vo.request.IntersectionForDashboardMapSearchRequestVO;
//import com.siemens.spm.usermanagementservice.api.vo.response.ElementListForDashboardChartResultObject;
//import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionListForDashboardMapResultObject;
//import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionListForDashboardTopRankResultObject;
//import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionListVolumeCountResultObject;
//import com.siemens.spm.usermanagementservice.api.vo.response.NotificationListForUserNotificationObject;
//import com.siemens.spm.usermanagementservice.config.SpmConfig;
//import com.siemens.spm.usermanagementservice.domain.Intersection;
//import com.siemens.spm.usermanagementservice.domain.Notification;
//import com.siemens.spm.usermanagementservice.repository.AgencySettingsRepository;
//import com.siemens.spm.usermanagementservice.repository.IntersectionRepository;
//import com.siemens.spm.usermanagementservice.repository.NotificationRepository;
//import com.siemens.spm.usermanagementservice.repository.filterdata.NotificationFilterDataVO;
//import com.siemens.spm.usermanagementservice.util.BeanFinderMocker;
//import org.junit.jupiter.api.AfterAll;
//import org.junit.jupiter.api.Assertions;
//import org.junit.jupiter.api.BeforeAll;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.MockedStatic;
//import org.mockito.Mockito;
//import org.mockito.MockitoAnnotations;
//import org.springframework.http.ResponseEntity;
//
//import java.sql.Timestamp;
//import java.time.LocalDateTime;
//import java.time.OffsetDateTime;
//import java.time.ZoneOffset;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//import java.util.Optional;
//import java.util.UUID;
//
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.ArgumentMatchers.anyInt;
//import static org.mockito.ArgumentMatchers.anyString;
//import static org.mockito.ArgumentMatchers.eq;
//
//class DashboardStrategyBeanTest {
//
//    private static final String AVAILABLE_INTERSECTION_ID = UUID.randomUUID().toString();
//    private static final String UNAVAILABLE_INTERSECTION_ID = UUID.randomUUID().toString();
//
//    @InjectMocks
//    private DashboardStrategyBean dashboardStrategyBean;
//
//    @Mock
//    private SpmConfig spmConfig;
//
//    @Mock
//    private IntersectionRepository intersectionRepository;
//
//    @Mock
//    private NotificationRepository notificationRepository;
//
//    @Mock
//    private StudioUserService studioUserService;
//
//    @Mock
//    private StudioAgencyService studioAgencyService;
//
//    @Mock
//    private AgencySettingsRepository agencySettingsRepository;
//
//    @Mock
//    private MessageService translator;
//
//    @Mock
//    private DataIntegrationService dataIntegrationService;
//
//    private static MockedStatic<BeanFinder> beanFinderMockedStatic;
//
//    private static final int AGENCY_ID = 1;
//    private static final String INT_UUID = AVAILABLE_INTERSECTION_ID;
//
//    private static final String PERLOG_SERVICE_ENDPOINT = "http://localhost:8004";
//
//    @BeforeAll
//    static void beforeClass() {
//        beanFinderMockedStatic = Mockito.mockStatic(BeanFinder.class);
//        beanFinderMockedStatic.when(BeanFinder::getDefaultMessageService).thenReturn(BeanFinderMocker.messageService());
//        beanFinderMockedStatic.when(BeanFinder::getDefaultObjectMapper).thenReturn(BeanFinderMocker.objectMapper());
//    }
//
//    @AfterAll
//    static void afterClass() {
//        beanFinderMockedStatic.close();
//    }
//
//    @BeforeEach
//    void beforeEach() {
//        MockitoAnnotations.openMocks(this);
//    }
//
//    @Test
//    void test_getIntersectionsForMap_success() throws DataHubException {
//        IntersectionForDashboardMapSearchRequestVO requestVO = mockSearchIntersectionRequest();
//
//        DataHubIntersectionResponseVO responseVO = new DataHubIntersectionResponseVO();
//        responseVO.setItems(List.of(mockDataHubIntersectionVO(INT_UUID)));
//        responseVO.setTotalItems(1L);
//
//        Mockito.when(dataIntegrationService.getIntersectionsByFilter(any()))
//                .thenReturn(responseVO);
//
//        Mockito.when(spmConfig.getRuleServiceEndpoint())
//                .thenReturn("rule-endpoint");
//        Mockito.when(spmConfig.getAnalysisServiceEndpoint())
//                .thenReturn("analysis-endpoint");
//
//        try (MockedStatic<InternalAnalysisController> internalAnalysisControllerMocked =
//                Mockito.mockStatic(InternalAnalysisController.class);
//                MockedStatic<AlarmRecordInterComController> alarmRecordInterComControllerMocked =
//                        Mockito.mockStatic(AlarmRecordInterComController.class)) {
//            alarmRecordInterComControllerMocked
//                    .when(() -> AlarmRecordInterComController.invokeCountRecordsForEachIntersection(anyString(), any()))
//                    .thenReturn(ResponseEntity.ok(alarmRecordCountForEachIntersectionResponseVO()));
//
//            internalAnalysisControllerMocked
//                    .when(() -> InternalAnalysisController
//                            .invokeGetAnalysisStatistics(anyString(), anyInt(), any()))
//                    .thenReturn(ResponseEntity.ok(analysisStatisticsResultObject()));
//
//            IntersectionListForDashboardMapResultObject resultObject = dashboardStrategyBean
//                    .getIntersectionsForMap(requestVO, new String[0], 1, 10);
//
//            Assertions.assertNotNull(resultObject);
//            Assertions.assertEquals(IntersectionListForDashboardMapResultObject.StatusCode.SUCCESS,
//                    resultObject.getStatusCode());
//        }
//    }
//
//    @Test
//    void test_getIntersectionsForTopRank() throws StudioException, DataHubException {
//        Mockito.when(notificationRepository
//                        .findTopIntersectionsByUserIdAndAgencyIdAndTypeIdAndUnreadCount(any(NotificationFilterDataVO.class),
//                                anyInt()))
//                .thenReturn(Map.of(INT_UUID, 1L));
//
//        DataHubIntersectionResponseVO responseVO = new DataHubIntersectionResponseVO();
//        responseVO.setItems(List.of(mockDataHubIntersectionVO(INT_UUID)));
//        responseVO.setTotalItems(1L);
//
//        Mockito.when(dataIntegrationService.getIntersectionsByFilter(any()))
//                .thenReturn(responseVO);
//
//        Mockito.when(studioUserService.findByEmail(any())).thenReturn(Optional.of(mockStudioUserDto()));
//
//        IntersectionListForDashboardTopRankResultObject resultObject =
//                dashboardStrategyBean.getIntersectionsForTopRank(
//                        AGENCY_ID,
//                        new ArrayList<>(List.of("123")),
//                        new ArrayList<>(List.of("234")),
//                        9);
//
//        Assertions.assertNotNull(resultObject);
//        Assertions.assertEquals(IntersectionListForDashboardTopRankResultObject.StatusCode.SUCCESS,
//                resultObject.getStatusCode());
//    }
//
//    @Test
//    void test_getAllElementsForChart() throws StudioException, DataHubException {
//        Mockito.when(notificationRepository.findAllByFilter(any(NotificationFilterDataVO.class)))
//                .thenReturn(List.of(mockNotification("foo"), mockNotification("foo"),
//                        mockNotification("bar")));
//        Mockito.when(studioUserService.findByEmail(any())).thenReturn(Optional.of(mockStudioUserDto()));
//
//        DataHubIntersectionResponseVO responseVO = new DataHubIntersectionResponseVO();
//        responseVO.setItems(List.of(mockDataHubIntersectionVO(UNAVAILABLE_INTERSECTION_ID)));
//
//        Mockito.when(dataIntegrationService.getIntersectionsByFilter(any()))
//                .thenReturn(responseVO);
//
//        ElementListForDashboardChartResultObject resultObject = dashboardStrategyBean.getAllElementsForChart(
//                AGENCY_ID,
//                new ArrayList<>(List.of("123")),
//                new ArrayList<>(List.of("234")));
//
//        Assertions.assertNotNull(resultObject);
//        Assertions.assertEquals(ElementListForDashboardChartResultObject.StatusCode.SUCCESS,
//                resultObject.getStatusCode());
//    }
//
//    @Test
//    void test_getElementForUserNotification() throws StudioException, DataHubException {
//        Mockito.when(studioUserService.findByEmail(any())).thenReturn(Optional.of(mockStudioUserDto()));
//        Mockito.when(notificationRepository.findAllByFilter(any(NotificationFilterDataVO.class)))
//                .thenReturn(
//                        List.of(mockNotification("foo", 12L,
//                                        NotificationConstants.ReadStatus.READ.getValue(),
//                                        NotificationConstants.FlagStatus.FLAG.getValue()),
//                                mockNotification("foo", 34L,
//                                        NotificationConstants.ReadStatus.UNREAD.getValue(),
//                                        NotificationConstants.FlagStatus.UNFLAG.getValue()),
//                                mockNotification("bar")));
//
//        DataHubIntersectionResponseVO responseVO = new DataHubIntersectionResponseVO();
//        responseVO.setItems(List.of(mockDataHubIntersectionVO(UNAVAILABLE_INTERSECTION_ID)));
//
//        Mockito.when(dataIntegrationService.getIntersectionsByFilter(any()))
//                .thenReturn(responseVO);
//
//        NotificationListForUserNotificationObject resultObject = dashboardStrategyBean.getElementsForUserNotification(
//                AGENCY_ID,
//                new ArrayList<>(List.of(2L)),
//                Timestamp.valueOf(LocalDateTime.now()),
//                Timestamp.valueOf(LocalDateTime.now().plusHours(12)));
//
//        Assertions.assertNotNull(resultObject);
//        Assertions.assertEquals(NotificationListForUserNotificationObject.StatusCode.SUCCESS,
//                resultObject.getStatusCode());
//    }
//
//    @Test
//    void test_getIntersectionVolumeCount_success() throws DataHubException {
//        LocalDateTime toTime = OffsetDateTime.now(ZoneOffset.UTC).toLocalDateTime();
//        LocalDateTime fromTime = toTime.minusHours(10);
//
//        IntersectionVolumeCountResultObject mockResponse = this.mockIntersectionVolumeCountResultObject();
//
//        Mockito.when(spmConfig.getPerflogServiceEndpoint())
//                .thenReturn(PERLOG_SERVICE_ENDPOINT);
//
//        DataHubIntersectionResponseVO responseVO = new DataHubIntersectionResponseVO();
//        responseVO.setItems(List.of(mockDataHubIntersectionVO(INT_UUID)));
//
//        Mockito.when(dataIntegrationService.getIntersectionsByFilter(any()))
//                .thenReturn(responseVO);
//
//        try (MockedStatic<PerfLogController> perfLogControllerMockedStatic = Mockito.mockStatic(
//                PerfLogController.class)) {
//            perfLogControllerMockedStatic
//                    .when(() -> PerfLogController.invokeGetIntersectionVolumeCount(
//                            Mockito.anyString(),
//                            Mockito.any(),
//                            Mockito.any(),
//                            Mockito.anyInt(),
//                            Mockito.any(),
//                            Mockito.anyList())
//                    )
//                    .thenReturn(ResponseEntity.ok(mockResponse));
//
//            IntersectionListVolumeCountResultObject resultObject = dashboardStrategyBean
//                    .getIntersectionVolumeCount(fromTime, toTime, AGENCY_ID, IntersectionOption.INCLUDE_ALL,
//                            new ArrayList<>());
//
//            Assertions.assertNotNull(resultObject);
//            Assertions.assertEquals(IntersectionListVolumeCountResultObject.StatusCode.SUCCESS,
//                    resultObject.getStatusCode());
//        }
//    }
//
//    @Test
//    void test_getIntersectionVolumeCount_inIncludeAllOption() throws DataHubException {
//        LocalDateTime toTime = OffsetDateTime.now(ZoneOffset.UTC).toLocalDateTime();
//        LocalDateTime fromTime = toTime.minusHours(10);
//
//        IntersectionVolumeCountResultObject mockResponse = this.mockIntersectionVolumeCountResultObject();
//
//        Mockito.when(spmConfig.getPerflogServiceEndpoint())
//                .thenReturn(PERLOG_SERVICE_ENDPOINT);
//
//        // Create a default response for any request to getIntersectionsByFilter
//        DataHubIntersectionResponseVO responseVO = new DataHubIntersectionResponseVO();
//        responseVO.setItems(List.of(mockDataHubIntersectionVO(UNAVAILABLE_INTERSECTION_ID)));
//        responseVO.setTotalItems(1L);
//
//        // CRITICAL: Use a lenient stub to ensure we ALWAYS return this response regardless of arguments
//        Mockito.lenient()
//                .when(dataIntegrationService.getIntersectionsByFilter(any(DataHubIntersectionSearchRequestVO.class)))
//                .thenReturn(responseVO);
//
//        try (MockedStatic<PerfLogController> perfLogControllerMockedStatic = Mockito.mockStatic(
//                PerfLogController.class)) {
//            perfLogControllerMockedStatic
//                    .when(() -> PerfLogController.invokeGetIntersectionVolumeCount(
//                            Mockito.anyString(),
//                            Mockito.any(),
//                            Mockito.any(),
//                            Mockito.anyInt(),
//                            Mockito.any(),
//                            Mockito.anyList())
//                    )
//                    .thenReturn(ResponseEntity.ok(mockResponse));
//
//            IntersectionListVolumeCountResultObject resultObject = dashboardStrategyBean
//                    .getIntersectionVolumeCount(fromTime, toTime, AGENCY_ID, IntersectionOption.INCLUDE_ALL,
//                            new ArrayList<>());
//
//            Assertions.assertNotNull(resultObject);
//            Assertions.assertEquals(IntersectionListVolumeCountResultObject.StatusCode.SUCCESS,
//                    resultObject.getStatusCode());
//
//            // Just verify that PerfLogController was called with the right option
//            perfLogControllerMockedStatic.verify(
//                    () -> PerfLogController.invokeGetIntersectionVolumeCount(
//                            Mockito.anyString(),
//                            Mockito.any(),
//                            Mockito.any(),
//                            Mockito.anyInt(),
//                            eq(IntersectionOption.EXCLUDE_SPECIFIC),
//                            Mockito.anyList())
//            );
//        }
//    }
//
//    @Test
//    void test_getIntersectionVolumeCount_inIncludeSpecificOption() throws DataHubException {
//        LocalDateTime toTime = OffsetDateTime.now(ZoneOffset.UTC).toLocalDateTime();
//        LocalDateTime fromTime = toTime.minusHours(10);
//
//        IntersectionVolumeCountResultObject mockResponse = this.mockIntersectionVolumeCountResultObject();
//
//        Mockito.when(spmConfig.getPerflogServiceEndpoint())
//                .thenReturn(PERLOG_SERVICE_ENDPOINT);
//
//        DataHubIntersectionResponseVO responseVO = new DataHubIntersectionResponseVO();
//        responseVO.setItems(List.of(mockDataHubIntersectionVO(INT_UUID)));
//        responseVO.setTotalItems(1L);
//
//        // CRITICAL: Use a lenient stub to ensure we ALWAYS return this response regardless of arguments
//        Mockito.lenient()
//                .when(dataIntegrationService.getIntersectionsByFilter(any(DataHubIntersectionSearchRequestVO.class)))
//                .thenReturn(responseVO);
//
//        try (MockedStatic<PerfLogController> perfLogControllerMockedStatic = Mockito.mockStatic(
//                PerfLogController.class)) {
//            perfLogControllerMockedStatic
//                    .when(() -> PerfLogController.invokeGetIntersectionVolumeCount(
//                            Mockito.anyString(),
//                            Mockito.any(),
//                            Mockito.any(),
//                            Mockito.anyInt(),
//                            Mockito.any(),
//                            Mockito.anyList())
//                    )
//                    .thenReturn(ResponseEntity.ok(mockResponse));
//
//            IntersectionListVolumeCountResultObject resultObject = dashboardStrategyBean
//                    .getIntersectionVolumeCount(fromTime, toTime, AGENCY_ID, IntersectionOption.INCLUDE_SPECIFIC,
//                            new ArrayList<>());
//
//            Assertions.assertNotNull(resultObject);
//            Assertions.assertEquals(IntersectionListVolumeCountResultObject.StatusCode.SUCCESS,
//                    resultObject.getStatusCode());
//
//            // Just verify that PerfLogController was called with the right option
//            perfLogControllerMockedStatic.verify(
//                    () -> PerfLogController.invokeGetIntersectionVolumeCount(
//                            Mockito.anyString(),
//                            Mockito.any(),
//                            Mockito.any(),
//                            Mockito.anyInt(),
//                            eq(IntersectionOption.INCLUDE_SPECIFIC),
//                            Mockito.anyList())
//            );
//        }
//    }
//
//    @Test
//    void test_getIntersectionVolumeCount_inExcludeSpecificOption() throws DataHubException {
//        LocalDateTime toTime = OffsetDateTime.now(ZoneOffset.UTC).toLocalDateTime();
//        LocalDateTime fromTime = toTime.minusHours(10);
//
//        IntersectionVolumeCountResultObject mockResponse = this.mockIntersectionVolumeCountResultObject();
//
//        Mockito.when(spmConfig.getPerflogServiceEndpoint())
//                .thenReturn(PERLOG_SERVICE_ENDPOINT);
//
//        DataHubIntersectionResponseVO responseVO = new DataHubIntersectionResponseVO();
//        responseVO.setItems(List.of(mockDataHubIntersectionVO(UNAVAILABLE_INTERSECTION_ID)));
//        responseVO.setTotalItems(1L);
//
//        // CRITICAL: Use a lenient stub to ensure we ALWAYS return this response regardless of arguments
//        Mockito.lenient()
//                .when(dataIntegrationService.getIntersectionsByFilter(any(DataHubIntersectionSearchRequestVO.class)))
//                .thenReturn(responseVO);
//
//        try (MockedStatic<PerfLogController> perfLogControllerMockedStatic = Mockito.mockStatic(
//                PerfLogController.class)) {
//            perfLogControllerMockedStatic
//                    .when(() -> PerfLogController.invokeGetIntersectionVolumeCount(
//                            Mockito.anyString(),
//                            Mockito.any(),
//                            Mockito.any(),
//                            Mockito.anyInt(),
//                            Mockito.any(),
//                            Mockito.anyList())
//                    )
//                    .thenReturn(ResponseEntity.ok(mockResponse));
//
//            IntersectionListVolumeCountResultObject resultObject = dashboardStrategyBean
//                    .getIntersectionVolumeCount(fromTime, toTime, AGENCY_ID, IntersectionOption.EXCLUDE_SPECIFIC,
//                            new ArrayList<>());
//
//            Assertions.assertNotNull(resultObject);
//            Assertions.assertEquals(IntersectionListVolumeCountResultObject.StatusCode.SUCCESS,
//                    resultObject.getStatusCode());
//
//            // Just verify that PerfLogController was called with the right option
//            perfLogControllerMockedStatic.verify(
//                    () -> PerfLogController.invokeGetIntersectionVolumeCount(
//                            Mockito.anyString(),
//                            Mockito.any(),
//                            Mockito.any(),
//                            Mockito.anyInt(),
//                            eq(IntersectionOption.EXCLUDE_SPECIFIC),
//                            Mockito.anyList())
//            );
//        }
//    }
//
//    // Add helper method to create DataHubIntersectionVO
//    private DataHubIntersectionVO mockDataHubIntersectionVO(String id) {
//        DataHubIntersectionVO vo = new DataHubIntersectionVO();
//        vo.setId(id);
//        vo.setName("6TH & Champion");
//        return vo;
//    }
//
//    private IntersectionVolumeCountResultObject mockIntersectionVolumeCountResultObject() {
//        return new IntersectionVolumeCountResultObject(
//                IntersectionListVolumeCountVO.builder()
//                        .intersectionVolumeCountVOS(
//                                mockIntersectionVolumeCountVO()
//                        )
//                        .build(),
//                IntersectionVolumeCountResultObject.StatusCode.SUCCESS
//        );
//    }
//
//    private List<IntersectionVolumeCountVO> mockIntersectionVolumeCountVO() {
//        List<IntersectionVolumeCountVO> result = new ArrayList<>();
//        result.add(IntersectionVolumeCountVO.builder()
//                .intersectionId(INT_UUID)
//                .totalCount(15)
//                .build());
//        return result;
//    }
//
//    private Intersection mockIntersection(String iid) {
//        return Intersection.builder()
//                .id(iid)
//                .name("6TH & Champion")
//                .build();
//    }
//
//    private AlarmRecordCountForEachIntersectionResponseVO alarmRecordCountForEachIntersectionResponseVO() {
//        AlarmRecordCountForEachIntersectionResponseVO.ResponseData responseData =
//                AlarmRecordCountForEachIntersectionResponseVO.ResponseData.builder()
//                        .intersectionIdToAlarmRecordCountMap(Map.of(INT_UUID, 10L))
//                        .build();
//
//        return new AlarmRecordCountForEachIntersectionResponseVO(responseData);
//    }
//
//    private AnalysisStatisticsResultObject analysisStatisticsResultObject() {
//        Timestamp updatedTime = Timestamp.valueOf(LocalDateTime.now());
//        AnalysisStatisticsVO statisticsVO = AnalysisStatisticsVO.builder()
//                .intStatisticsVOList(List.of(new IntersectionStatisticsVO(INT_UUID, updatedTime)))
//                .updatedTime(updatedTime)
//                .build();
//        return new AnalysisStatisticsResultObject(statisticsVO);
//    }
//
//    private Notification mockNotification(String categoryName) {
//        return Notification.builder()
//                .alarmCategoryName(categoryName)
//                .build();
//    }
//
//    private IntersectionForDashboardMapSearchRequestVO mockSearchIntersectionRequest() {
//        LocalDateTime now = LocalDateTime.now();
//        return IntersectionForDashboardMapSearchRequestVO.builder()
//                .agencyId(AGENCY_ID)
//                .fromTime(Timestamp.valueOf(now))
//                .toTime(Timestamp.valueOf(now.plusHours(2)))
//                .build();
//    }
//
//    private Notification mockNotification(String categoryName, Long typeId, String readStatus, String flagStatus) {
//        return Notification.builder()
//                .alarmCategoryName(categoryName)
//                .typeId(typeId)
//                .readStatus(readStatus)
//                .flagStatus(flagStatus)
//                .build();
//    }
//
//    private StudioAgencyDto mockStudioAgencyDto() {
//        StudioAgencyDto studioAgencyDto = new StudioAgencyDto();
//        studioAgencyDto.setAgencyNo(1);
//        studioAgencyDto.setAgencyName("Test Agency");
//        studioAgencyDto.setActivated(true);
//        return studioAgencyDto;
//    }
//
//    private StudioUserDto mockStudioUserDto() {
//        StudioUserDto studioUserDto = new StudioUserDto();
//        studioUserDto.setIsEnabled(true);
//        studioUserDto.setId(1);
//        studioUserDto.setEmail("<EMAIL>");
//        return studioUserDto;
//    }
//
//}
