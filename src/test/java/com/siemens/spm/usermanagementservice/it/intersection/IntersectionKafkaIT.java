/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionKafkaIntegrestionTest.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.it.intersection;

import com.siemens.spm.usermanagementservice.domain.Intersection;
import com.siemens.spm.usermanagementservice.it.BaseIntegrationTest;
import com.siemens.spm.usermanagementservice.repository.CorridorIntersectionRepository;
import com.siemens.spm.usermanagementservice.repository.IntersectionRepository;
import org.json.JSONArray;
import org.json.JSONException;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.jdbc.JdbcTestUtils;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.Duration;
import java.util.Optional;

import static java.util.concurrent.TimeUnit.SECONDS;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.testcontainers.shaded.org.awaitility.Awaitility.await;

class IntersectionKafkaIT extends BaseIntegrationTest {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private KafkaTemplate<String, Object> kafkaTemplate;

    @Autowired
    private IntersectionRepository intersectionRepository;

    @Autowired
    private CorridorIntersectionRepository corridorIntersectionRepository;

    @AfterEach
    void clearRecords() {
        JdbcTestUtils.deleteFromTables(jdbcTemplate, "intersection", "agency_settings", "agency_intersection");
    }

    @Test
    @Sql(value = "/it/create_intersection_metadata.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    void handleIntersectionKafkaCreateEvent() throws IOException, JSONException {

        String jsonArray = new String(Files.readAllBytes(Paths.get("src/test/resources/it/create_intersection.json")));

        JSONArray kafkaMessages = new JSONArray(jsonArray);

        for (int i = 0; i < kafkaMessages.length(); i++) {
            kafkaTemplate.send("studio-signal-changed", kafkaMessages.get(i).toString());
        }

        await().pollInterval(Duration.ofSeconds(3)).atMost(10, SECONDS).untilAsserted(() -> {
            //assert that first message is not saved
            assertFalse(intersectionRepository.existsById("c5ba698d-b53e-4b02-a5be-a1202385c47c"));

            //assert that second message is not saved
            Optional<Intersection> intersectionOpt1 = intersectionRepository.findById(
                    "ae9efee2-1288-464d-8ac1-7816963b31c6");
            assertTrue(intersectionOpt1.isPresent());
            Intersection intersection1 = intersectionOpt1.get();
            assertNotEquals("This will fail due to intersection id already existed", intersection1.getName());

            //assert message 3 is processed and related data is saved
            Optional<Intersection> intersectionOpt2 = intersectionRepository.findById(
                    "ff44662a-558c-4803-95e1-1879d40408a7");
            assertTrue(intersectionOpt2.isPresent());

            Intersection intersection2 = intersectionOpt2.get();

            assertEquals("Create success", intersection2.getName());
            assertEquals("SEPAC", intersection2.getModel());
            assertEquals("5.5.2", intersection2.getVersion());
            assertEquals(25.729654, intersection2.getLatitude());
            assertEquals(-80.39962, intersection2.getLongitude());

        });
    }

    @Test
    @Sql(value = "/it/update_intersection_metadata.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    void handleIntersectionKafkaUpdateEvent() throws IOException, JSONException {
        String jsonArray = new String(Files.readAllBytes(Paths.get("src/test/resources/it/update_intersection.json")));

        JSONArray kafkaMessages = new JSONArray(jsonArray);

        //send first messages. This will fail
        kafkaTemplate.send("studio-signal-changed", kafkaMessages.get(0).toString());
        await().pollDelay(2, SECONDS).pollInterval(Duration.ofSeconds(3)).atMost(10, SECONDS).untilAsserted(() -> {
            //assert that first message is not saved
            assertFalse(intersectionRepository.existsById("c5ba698d-b53e-4b02-a5be-a1202385c47c"));
        });

        //send next 2 messages
        for (int i = 1; i < kafkaMessages.length(); i++) {
            kafkaTemplate.send("studio-signal-changed", kafkaMessages.get(i).toString());
        }

        await().pollDelay(2, SECONDS).pollInterval(Duration.ofSeconds(3)).atMost(10, SECONDS).untilAsserted(() -> {
            //assert that second message is saved
            Optional<Intersection> intersectionOpt1 = intersectionRepository.findById(
                    "c5ba698d-b53e-4b02-a5be-a1202385c47c");
            assertTrue(intersectionOpt1.isPresent());
            Intersection intersection1 = intersectionOpt1.get();
            assertEquals("This will create new intersection", intersection1.getName());

            //assert message 3 is processed and info is updated
            Optional<Intersection> intersectionOpt2 = intersectionRepository.findById(
                    "ae9efee2-1288-464d-8ac1-7816963b31c6");
            assertTrue(intersectionOpt2.isPresent());

            Intersection intersection2 = intersectionOpt2.get();

            assertEquals("This will update existed intersection", intersection2.getName());
            assertEquals("SEPAC", intersection2.getModel());
            assertEquals("5.5.2", intersection2.getVersion());
            assertEquals(25.729654, intersection2.getLatitude());
            assertEquals(-80.39962, intersection2.getLongitude());
        });
    }

    @Test
    @Sql(value = "/it/delete_intersection_metadata.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    void handleDeleteIntersectionKafkaDeleteEvent() throws IOException, JSONException {
        String jsonArray = new String(Files.readAllBytes(Paths.get("src/test/resources/it/delete_intersection.json")));

        JSONArray kafkaMessages = new JSONArray(jsonArray);

        for (int i = 0; i < kafkaMessages.length(); i++) {
            kafkaTemplate.send("studio-signal-changed", kafkaMessages.get(i).toString());
        }

        await().pollDelay(2, SECONDS).pollInterval(Duration.ofSeconds(3)).atMost(10, SECONDS).untilAsserted(() -> {
            //assert that first message is processed
            assertFalse(intersectionRepository.existsById("c5ba698d-b53e-4b02-a5be-a1202385c47c"));

            //assert that second message is processed
            assertFalse(intersectionRepository.existsById("c5ba698d-b53e-4b02-a5be-a1202385c47c"));
            assertFalse(corridorIntersectionRepository.existsById("ada5d133-e869-4890-b007-13d1aa82a1e5"));
        });
    }

}
