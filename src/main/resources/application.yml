management:
  server:
    # do not change port
    port: 9193

  endpoints:
    exposure:
      include: health

  endpoint:
    health:
      probes:
        enabled: true

logging:
  level:
    root: INFO
    org.apache.kafka: OFF

# Port configuration
server:
  port: 9100
  servlet:
    context-path: /user-service
  max-http-request-header-size: 100KB

data-retention:
  retention-days:
    enabled-data: 180
    disabled-data: 60
  schedulers:
    clean-up-notification-data:
      enabled: true
      cron: 0 0 1 * * ?

spring:
  profiles:
    active: native

  # DB configuration
  datasource:
    master:
      hikari:
        driver-class-name: org.postgresql.Driver
        jdbc-url: ************************************************************************************************
        username: "insights.postgres"
        password: dlhVW5Ekuj5JztyO1fK4GdIV6Gz2579spPpxTtXc1FaCI0WIaumjeAjbdWeTfBSz
        enabled: true
        maximum-pool-size: 10
        minimum-idle: 5
        idle-timeout: 10000 #10 seconds
        connection-timeout: 30000 #30 seconds
        pool-name: master-pool
    #Agency schema
    agency:
      hikari:
        driver-class-name: org.postgresql.Driver
        jdbc-url: ************************************************************************************************
        username: "insights.postgres"
        password: dlhVW5Ekuj5JztyO1fK4GdIV6Gz2579spPpxTtXc1FaCI0WIaumjeAjbdWeTfBSz
        enabled: true
        maximum-pool-size: 30
        minimum-idle: 5
        idle-timeout: 10000 #10 seconds
        connection-timeout: 30000 #30 seconds
        pool-name: agency-pool

  sql:
    init:
      mode: never
      continue-on-error: true

  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: none
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        use_sql_comments: true
        format_sql: true
        type: trace
        jdbc:
          batch_size: 50
        order_inserts: true
    open-in-view: false
    defer-datasource-initialization: false

  messages:
    basename: i18n/messages

  servlet:
    multipart:
      max-file-size: 25MB
      max-request-size: 25MB

  # Email configuration. Just for testing
  mail:
    host: smtp.gmail.com
    port: 587
    username: <EMAIL>
    password: nrurpfvycdygfqax
    protocol: smtp
    tls: true
    properties.mail.smtp:
      auth: true
      starttls.enable: true
      ssl.trust: smtp.gmail.com
    faviconUrl: http://d2miadwavvabr5.cloudfront.net
    passwordResetPageUrl: http://password-reset-page

  data:
    # Redis configuration
    redis:
      host: *************
      port: 6379

  scheduling:
    core_pool_size: 10
    max_pool_size: 30
    queue_size: 100

  # App configuration
  app:
    time-zone: UTC

  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: https://iam.us.dev.yunextraffic.cloud/auth/realms/mos
      client:
        registration:
          idp:
            client-id: ####
            client-secret: ####
            authorization-grant-type: client_credentials
        provider:
          idp:
            token-uri: https://iam.us.dev.yunextraffic.cloud/auth/realms/mos/protocol/openid-connect/token

  kafka:
    enable: true
    bootstrap-servers: localhost:9094
    topic:
      agency:
        group-id: insights-user-service-agency
        name: datahub-delegate-agency-changed
        concurrency: 3
      intersection:
        name: studio-signal-changed
        concurrency: 3
      perflog-uploaded-changed:
        name: datahub-data-perflog-uploaded-changed
        concurrency: 3
    consumer:
      group-id: insights-user-management   #this is default consumer group

# SPM Platform configuration
spm:
  web-ui:
    endpoint: http://localhost:3000

  analysis:
    endpoint: http://localhost:8001/analysis-service

  rule-service:
    endpoint: http://localhost:7001/alarm-rule-service

  perflog-crawler-service:
    endpoint: http://localhost:8004/perflog-crawler-service

  user-mgmt-service:
    endpoint: http://localhost:9100/user-service

  tactic:
    analysis-page: /analysis/perfomance-metric

  agency:
    synchronization:
      initial-delay-ms: 60000 # 3 minutes in milliseconds
      fixed-rate-ms: 1800000  # 30 * 60 * 1000 = 30 minutes in milliseconds

user:
  user-key:
    lifetime-in-day: 90
    expiration:
      notification-before-days: 10

studio:
  user-service:
    endpoint: http://studio--user-service.studio:8080
  agency-service:
    endpoint: http://studio--agency-service.studio:8080

data-hub:
  integration-service:
    endpoint: http://localhost:9000/integration

cors:
  allowed-origins: https://*.yunextraffic.cloud

cache:
  config:
    cache-prefix: "[INSIGHTS-USERS]"
    default-ttl-minutes: 60
    agency:
      ttl-in-minutes: 10
    timezone:
      ttl-in-days: 30

multi-agency:
  provision:
    agency:
      liquibase:
        enabled: false
        secure-parsing: false
        change-log: classpath:db/changelog/agency/db.master.changelog.xml
        fail-on-error: true
      batch-config:
        batch-size: 5

    master:
      liquibase:
        enabled: false
        secure-parsing: false
        change-log: classpath:db/changelog/master/db.master.changelog.xml
        fail-on-error: true
      batch-config:
        batch-size: 1

app:
  agency:
    security:
      allowed-agency-filters:
        - /actuator/**
        - /configuration/**
        - /v2/api-docs
        - /v3/api-docs/**
        - /swagger-ui/**
        - /swagger*/**
        - /v3/api-docs.yaml
        - /internal-api/v1/intersections/sync-data
        - /internal-api/v1/users/expiration-data
