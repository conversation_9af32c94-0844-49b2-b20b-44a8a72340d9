# General texts
alarm_notifications=Alarm notifications
alarm_records=Alarm records
aor_analysis=AoR analysis
aog_analysis=AoG analysis
coord_analysis=Coordination analysis
avg_ped_delay=Avg. Ped Delay
avg_app_delay=Avg. App Delay
rlv_analysis=RLV analysis
preemption_analysis=Preemption/Priority analysis
queue_length_analysis=Queue Length analysis

## avatar
avatar_can_not_crop=Cannot crop Avatar
avatar_not_allow_file_type=Please use a png or jpg file for your Avatar
avatar_not_allow_size=Please upload a file that is not larger than 4MB
avatar_not_allow_dimension=Please upload a file that has height and width between 160px and 1000px

not_support_language=This language is not supported
user_not_found=No user matches the provided search criteria
agency_not_found=No agency matches the provided search criteria
agency_not_deleted=Agency is not deleted
your_agency_is_not_active=Your Agency is not active
role_not_found=No role matches the provided search criteria
intersection_not_found=No intersection matches the provided search criteria
id_can_not_generated=Unable to generate ID
name_can_not_updated=Unable to update name
name_already_used=Please enter a different name, this one is already in use
email_already_used=Please enter a different email, this email is already in use
organization_can_not_updated=Unable to update Organization
user_existed_in_agency=User already exists in this agency
invalid_email=Email is invalid
email_duplicate=Email is duplicated
invalid_name=Name is invalid
invalid_role_name=Role name is invalid
invalid_sort_column=Sorting columns are invalid
invalid_sort_order=Sorting orders are invalid
invalid_current_password=Current password is invalid
invalid_new_password=New password is invalid
invalid_password=Password is invalid
invalid_default_agency=Default agency is invalid
invalid_role=Role is invalid
invalid_agency=Agency is invalid
invalid_refresh_token=Refresh token are invalid
current_new_password_same=New password cannot be the same as current password
name_required=Please enter your Name
phone_required=Please enter your Phone number
email_required=Please input your email
role_required=Please enter a Role
disabled_account=Account was disabled
bad_credentials=Credentials are invalid
otp_sending_failed=Can not send OTP token!
otp_is_incorrect_or_expired=The OTP token is invalid!
invalid_password_reset_key=Password reset key is invalid or no longer exists
incorrect_current_password=Current password is incorrect
user_key_not_exist=User key not exist
user_key_expired=User key expired
reset_time_expired=Reset time was expired
data_integrity_violation=Data integrity violation
user_not_under_agency_management=User does not belong to the agency
intersection_not_under_agency_management=This intersection does not belong the agency
agency_id_is_missing=agency_id is missing
agency_mismatched=Agency mismatches
ids_not_empty=ids can not be empty
user_ids_is_null=user_ids can not be null
agency_not_null=agency can not be null
agency_id_not_null=agency_id can not be null
user_id_not_null=user_id can not be null
intersection_title=Intersections
analysis_title=Analysis
invalid_account=This account is invalid
user_not_belong_any_agency=This account does not belong to any agency
agency_not_found_check_again=Agency not found, please check again
user_did_not_have_access_to_the_given_agency=User has not permission to access this agency
invalid_api_key=Api key does not exist!
error_when_communicating_with_datahub=Error when communicating with Data Hub! Please try again later!
deleted_by=Deleted by

## notification
notification.invalid_read_status=read_status is invalid
notification.invalid_flag_status=flag_status is invalid
notification.noti_ids_not_empty=noti_ids can not be empty
notification.notification_not_found=Notification not found
notification.user_does_not_have_permission_to_update_notification=User does not have permission to update notification
notification.notication_id_not_empty=notification_id can not be empty
notification.invalid_notification_content=The format of content of the notification is invalid
notification.intersection_status_changed_name=Intersection Status Update
notification.intersection_status_changed_content_header=Set of intersections has had a change in their status
notification.intersection_status_changed_content_description=\nIntersection {0}: {1} at {2}

## rule
alarm.rule.name=Rule name
alarm.rule.description=Rule description

## alarm record
alarm.record.analysis_type=Analysis type
alarm.record.alarm_category=Alarm category
alarm.record.intersection_name=Intersection
alarm.record.alarm_condition=Alarm condition
alarm.record.alarm_content=Alarm content
alarm.record.alarm_time=Alarm time

#Password Expiration Notification
pwd-expiration.title=YUTRAFFIC Insights: Password expiration notification
pwd-expiration.notification=Your YUTRAFFIC password will expire in {0} days
pwd-expiration.announcement=To change your password, please log in to YUTRAFFIC Insights and go to your account settings. Click on the link below for quick access:

#User key expiration notification
user-key-expiration.title=YUTRAFFIC Insights: User key expiration
user-key-expiration.notification=Your user key will expire in {0} days
user-key-expiration.announcement=To generate a new user key, please log in to YUTRAFFIC Insights and go to your account settings. Click on the link below for quick access:

#User agency deletion
user-deletion.title=User Deleted
agency-deletion.title=Agency Deleted
user-agency-deletion.notification=- List of user is deleted: {0} \n - List of agency is deleted: {1}
user-agency-deletion.notification.missing_user=- List of agency is deleted: {0}
user-agency-deletion.notification.missing_agency=- List of user is deleted: {0}

## Agency settings
agency.settings.display_is_invalid=Agency display settings are invalid
agency.settings.timezone_is_invalid=Agency time zone settings are invalid
agency.settings.max_crawl_fails=Agency max crawl fails are invalid
agency.settings.alarm_categories_invalid=Agency alarm categories are invalid

## Report template
summary-report.templates.name=Report template name
summary-report.templates.description=Report template description

summary-report.result.name=Report name
summary-report.result.from-time=From time
summary-report.result.to-time=To time
summary-report.result.from-date=From date
summary-report.result.to-date=To date
summary-report.result.timezone=Timezone

## Performance metric template
performance-metric.templates.metric-type=Performance metric type
performance-metric.templates.description=Performance metric template description

# Performance metric result
performance_metric.results.metric_type=Metric type
performance_metric.results.bin_size=Bin size
performance_metric.results.from_time=From time
performance_metric.results.to_time=To time
performance_metric.results.from_date=From date
performance_metric.results.to_date=To date
performance_metric.results.week_days=Days of week
performance_metric.results.timezone=Timezone
performance_metric.results.result_list=List of results
performance_metric.results.empty_list=Template run have no result created

detector-report.templates.name=Detector report name
detector-report.templates.description=Detector report description

# Detector metric result
detector_report.results.from_time=From time
detector_report.results.to_time=To time
detector_report.results.from_date=From date
detector_report.results.to_date=To date
detector_report.results.week_days=Days of week
detector_report.results.timezone=Timezone
detector_report.results.result_list=List of results
detector_report.results.empty_list=Template run have no result created

# User setting
## Dashboard
user.setting.dashboard.mapview.invalid=Invalid map view widget setting
user.setting.dashboard.open_alarm.invalid=Invalid open alarm widget setting
user.setting.dashboard.top_int_open_alarm.invalid=Invalid top intersection open alarm widget setting
user.setting.dashboard.summary_report.invalid=Invalid summary report widget setting
user.setting.dashboard.volume_report.invalid=Invalid volume report widget setting
user.setting.dashboard.notification.invalid=Invalid notification widget setting

## Corridor
corridor_id_not_null=Corridor id can not be null
corridor_id_invalid=Corridor id invalid
corridor_not_found=Corridor not found
corridor_name_duplicated=Corridor name is already in use
intersection_not_belong_to_agency=Intersection is not belong to the agency
global_upstream_phase_min_is_1=Min of global upstream phase is 1
global_upstream_phase_max_is_16=Max of global upstream phase is 16
global_downstream_phase_min_is_1=Min of global downstream phase is 1
global_downstream_phase_max_is_16=Max of global downstream phase is 16
speed_required=Please enter speed
corridor_intersections_duplicated=The list corridor intersection has duplicated data, please check again
status_not_null=Status can not be null
invalid_corridor_status=Corridor status is invalid
corridor_not_belong_to_agency=Corridor not belong to agency
invalid_corridor_intersections_size=Minimum size of corridor intersections is 2
invalid_last_corridor_intersection_distance=Distance of last intersection in the list must be 0
corridor_intersection_speed_required=Please enter speed of each corridor intersection
corridor_intersection_upstream_required=Please enter upstream of each corridor intersection
corridor_intersection_downstream_required=Please enter downstream of each corridor intersection
global_upstream_phase_required=Please enter global upstream phase
global_downstream_phase_required=Please enter global downstream phase
invalid_stream_phase_number=Stream phase number must be between 1 and 16
number_required=Please enter number
agency_id_required=Please enter agency id
agency_id_not_found=Agency not found
intersection_ids_not_found=Intersection(s) not found
corridor_contains_disabled_intersection=Corridor contains disabled intersection

invalid_time_range=Max range time between from time and to time is 24h
invalid_input_time=from time and to time is require
input_time_must_less_than_now=Input time can not greater than now
from_time_must_less_than_to_time=from time can not greater than to time
can_not_get_data_from_perflog_service=Can not get data from perflog service
