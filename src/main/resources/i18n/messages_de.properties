# General texts
alarm_notifications=Alarmbenachrichtigungen
alarm_records=Alarm-Aufzeichnungen
aor_analysis=AoR analysis
aog_analysis=AoG analysis
coord_analysis=Coordination analysis
avg_ped_delay=Avg. Ped Delay
avg_app_delay=Avg. App Delay
preemption_analysis=Preemption/Priority analysis
queue_length_analysis=Queue Length analysis

## avatar
avatar_can_not_crop=Avatar kann nicht schneiden
avatar_not_allow_file_type=Bitte verwenden Sie eine PNG- oder JPG-Datei f?r Ihren Avatar
avatar_not_allow_size=Bitte laden Sie eine Datei hoch, die nicht gr??er als 4MB ist
avatar_not_allow_dimension=Bitte laden Sie eine Datei mit einer H?he und Breite zwischen 160px und 1000px hoch

not_support_language=Diese Sprache wird nicht unterst?tzt
user_not_found=Kein Benutzer entspricht den angegebenen Suchkriterien
agency_not_found=Keine Agentur entspricht den angegebenen Suchkriterien
agency_not_deleted=Agency is not deleted
your_agency_is_not_active=Ihre Agentur ist nicht aktiv
role_not_found=Keine Rolle entspricht den angegebenen Suchkriterien
intersection_not_found=Keine Schnittmenge entspricht den angegebenen Suchkriterien
id_can_not_generated=Id kann nicht generiert werden
name_can_not_updated=Name kann nicht aktualisiert werden
name_already_used=Bitte geben Sie einen anderen Namen ein, dieser wird bereits verwendet
email_already_used=Bitte geben Sie eine andere E-Mail ein, diese E-Mail ist bereits in Gebrauch
organization_can_not_updated=Organisation kann nicht aktualisiert werden
user_existed_in_agency=Benutzer existiert bereits in dieser Agentur
invalid_email=E-Mail ist ung?ltig
email_duplicate=Email is duplicated
invalid_name=Name ist ung?ltig
invalid_role_name=Rollenname ist ung?ltig
invalid_sort_column=Sortierspalten sind ung?ltig
invalid_sort_order=Sortierauftr?ge sind ung?ltig
invalid_current_password=Aktuelles Passwort ist ung?ltig
invalid_new_password=Neues Passwort ist ung?ltig
invalid_password=Passwort ist ung?ltig
invalid_default_agency=Standard-Agentur ist ung?ltig
invalid_role=Rolle ist ung?ltig
invalid_agency=Agentur ist ung?ltig
invalid_refresh_token=Refresh-Token sind ung?ltig
current_new_password_same=Neues Passwort und aktuelles Passwort sind gleich
name_required=Name ist erforderlich
phone_required=Telefon ist erforderlich
email_required=E-Mail ist erforderlich
role_required=Rolle ist erforderlich
disabled_account=Konto wurde deaktiviert
bad_credentials=Berechtigungsnachweise sind ung?ltig
otp_sending_failed=Can not send OTP token!
otp_is_incorrect_or_expired=The OTP token is invalid!
invalid_password_reset_key=Passwort-R?cksetzschl?ssel ist ung?ltig oder existiert nicht mehr
incorrect_current_password=Aktuelles Passwort ist falsch
user_key_not_exist=Benutzerschl?ssel nicht vorhanden
user_key_expired=Benutzerschl?ssel abgelaufen
reset_time_expired=Reset-Zeit war abgelaufen
data_integrity_violation=Verletzung der Datenintegrit?t
user_not_under_agency_management=Benutzer geh?rt nicht zur Agentur
intersection_not_under_agency_management=Diese Schnittmenge geh?rt nicht der Beh?rde
agency_id_is_missing=agency_id fehlt
agency_mismatched=Agentur-Unstimmigkeiten
ids_not_empty=ids k?nnen nicht leer sein
user_ids_is_null=user_ids kann nicht null sein
agency_not_null=agency kann nicht null und nichtig sein
agency_id_not_null=agency_id kann nicht null sein
user_id_not_null=user_id kann nicht null sein
intersection_title=Schnittpunkte
analyse_title=Analyse
invalid_account=Dieses Konto ist ung?ltig
user_not_belong_any_agency=Dieses Konto geh?rt keiner Agentur
agency_not_found_check_again=Agentur nicht gefunden, bitte noch einmal nachsehen
user_did_not_have_access_to_the_given_agency=User has not permission to access this agency
invalid_api_key=Api key does not exist!
error_when_communicating_with_datahub=Fehler bei der Kommunikation mit Data Hub! Bitte versuchen Sie es sp?ter noch einmal!
deleted_by=Deleted by

## notification
notification.invalid_read_status=read_status ist ung?ltig
notification.invalid_flag_status=flag_status ist ung?ltig
notification.noti_ids_not_empty=noti_ids kann nicht leer sein
notification.notification_not_found=Benachrichtigung nicht gefunden
notification.user_does_not_have_permission_to_update_notification=Benutzer hat nicht die Erlaubnis, die Benachrichtigung zu aktualisieren
notification.invalid_notification_content=Das Format des Inhalts der Meldung ist ung?ltig
notification.intersection_status_changed_name=Intersection Status Update
notification.intersection_status_changed_content_header=Set of intersections has had a change in their status
notification.intersection_status_changed_content_description=\nIntersection {0}: {1} at {2}

## alarm rule
alarm.rule.name=Name der Regel
alarm.rule.description=Beschreibung der Regel

## alarm record
alarm.record.analysis_type=Analyse-Typ
alarm.record.alarm_category=Alarm-Kategorie
alarm.record.intersection_name=Schnittpunkt
alarm.record.alarm_condition=Alarmzustand
alarm.record.alarm_content=Alarm-Inhalt
alarm.record.alarm_time=Alarmzeit

#Password Expiration Notification
pwd-expiration.title=YUTRAFFIC Insights: Ablauf des Passworts
pwd-expiration.notification=Ihr YUTRAFFIC-Passwort lauft in {0} Tagen ab
pwd-expiration.announcement=Um Ihr Passwort zu andern, melden Sie sich bitte bei YUTRAFFIC Insights an und gehen Sie zu Ihrer Kontoeinstellung

#User key expiration notification
user-key-expiration.title=YUTRAFFIC Insights: Ablauf des Benutzerschlussels
user-key-expiration.notification=Ihr Benutzerschlussel lauft in {0} Tagen ab
user-key-expiration.announcement=Um einen neuen Benutzerschlussel zu generieren, melden Sie sich bitte bei YUTRAFFIC Insights an und gehen Sie zu Ihrer Kontoeinstellung

#User agency deletion
user-deletion.title=User Deleted
agency-deletion.title=Agency Deleted
user-agency-deletion.notification=- List of user is deleted: {0} \n - List of agency is deleted: {1}
user-agency-deletion.notification.missing_user=- List of agency is deleted: {0}
user-agency-deletion.notification.missing_agency=- List of user is deleted: {0}

## Agency settings
agency.settings.display_is_invalid=Die Anzeigeeinstellungen der Agentur sind ung?ltig
agency.settings.timezone_is_invalid=Zeitzoneneinstellungen der Agentur sind ung?ltig
agency.settings.max_crawl_fails=Agency max crawl fails are invalid
agency.settings.alarm_categories_invalid=Agency alarm categories are invalid

## Summary Report template
summary-report.templates.name=Report template name
summary-report.templates.description=Report template description

summary-report.result.name=Report name
summary-report.result.from-time=From time
summary-report.result.to-time=To time
summary-report.result.from-date=From date
summary-report.result.to-date=To date
summary-report.result.timezone=Timezone

## Performance metric template
performance-metric.templates.metric-type=Performance metric type
performance-metric.templates.description=Performance metric template description

# Performance metric result
performance_metric.results.metric_type=Metric type
performance_metric.results.bin_size=Bin size
performance_metric.results.from_time=From time
performance_metric.results.to_time=To time
performance_metric.results.from_date=From date
performance_metric.results.to_date=To date
performance_metric.results.week_days=Days of week
performance_metric.results.timezone=Timezone
performance_metric.results.result_list=List of results
performance_metric.results.empty_list=Template run have no result created

detector-report.templates.name=Detector report name
detector-report.templates.description=Detector report description

# Detector metric result
detector_report.results.from_time=From time
detector_report.results.to_time=To time
detector_report.results.from_date=From date
detector_report.results.to_date=To date
detector_report.results.week_days=Days of week
detector_report.results.timezone=Timezone
detector_report.results.result_list=List of results
detector_report.results.empty_list=Template run have no result created
# User setting
## Dashboard
user.setting.dashboard.mapview.invalid=Invalid map view widget setting
user.setting.dashboard.open_alarm.invalid=Invalid open alarm widget setting
user.setting.dashboard.top_int_open_alarm.invalid=Invalid top intersection open alarm widget setting
user.setting.dashboard.summary_report.invalid=Invalid summary report widget setting
user.setting.dashboard.volume_report.invalid=Invalid volume report widget setting
user.setting.dashboard.notification.invalid=Invalid notification widget setting

## Corridor
corridor_id_not_null=Korridor-ID darf nicht null sein
corridor_id_invalid=Korridor-ID ung?ltig
corridor_not_found=Korridor nicht gefunden
corridor_name_duplicated=Korridorname wird bereits verwendet
intersection_not_belong_to_agency=Kreuzung geh?rt nicht der Agentur
global_upstream_phase_min_is_1=Min. der globalen Upstream-Phase ist 1
global_upstream_phase_max_is_16=Das Maximum der globalen Upstream-Phase betr?gt 16
global_downstream_phase_min_is_1=Min. der globalen Downstream-Phase ist 1
global_downstream_phase_max_is_16=Das Maximum der globalen Downstream-Phase betr?gt 16
speed_required=Bitte Geschwindigkeit eingeben
corridor_intersections_duplicated=Die Listenkorridorkreuzung hat doppelte Daten, bitte ?berpr?fen Sie es erneut
status_not_null=Der Status darf nicht null sein
invalid_corridor_status=Der Korridorstatus ist ung?ltig
corridor_not_belong_to_agency=Korridor geh?rt nicht der Agentur
invalid_corridor_intersections_size=Minimum size of corridor intersections is 2
invalid_last_corridor_intersection_distance=Distance of last intersection in the list must be 0
corridor_intersection_speed_required=Please enter speed of each corridor intersection
corridor_intersection_upstream_required=Please enter upstream of each corridor intersection
corridor_intersection_downstream_required=Please enter downstream of each corridor intersection
global_upstream_phase_required=Please enter global upstream phase
global_downstream_phase_required=Please enter global downstream phase
invalid_stream_phase_number=Stream phase number must be between 1 and 16
number_required=Please enter number
agency_id_required=Please enter agency id
agency_id_not_found=Agency not found
intersection_ids_not_found= Intersection(s) not found

invalid_time_range=Max range time between from time and to time is 24h
invalid_input_time=from time and to time is require
input_time_must_less_than_now=Input time can not greater than now
from_time_must_less_than_to_time=from time can not greater than to time
can_not_get_data_from_perflog_service=Can not get data from perflog service
