<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <title th:text="#{user-key-expiration.title}">User key expiration</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <link rel="shortcut icon" th:href="@{|${faviconUrl}/favicon.ico|}"/>
</head>
<body>
    <p th:text="#{email.common.greeting(${user.name != null} ? ${user.name} : 'YUTRAFFIC User')}">
        Dear
    </p>
    <p>
        <span th:text="#{user-key-expiration.notification(${remainingDays != null} ? ${remainingDays} : ' ')}">
            Your user key will expire in ... days
        </span>
        <br>
        <span th:text="#{user-key-expiration.announcement}">
            To generate a new user key, please log in to YUTRAFFIC Insights and go to your account settings.
            Click on the link below for quick access:
        </span>
    </p>
    <p><a th:text="${webUiEndpoint}" th:href="${webUiEndpoint}">The link</a></p>
    <p>
        <span th:text="#{email.common.regards}">Best regards,</span>
        <br/>
        <span th:text="#{email.common.signature}">YUTRAFFIC Insights Team</span>
    </p>
</body>
</html>
