-- liquibase formatted sql

-- changeset toanpham:1735117729688-1
CREATE TABLE IF NOT EXISTS ${schema}."intersection"
(
    "id"                  VARCHAR(50) NOT NULL,
    "created_at"          TIMESTAMP WITHOUT TIME ZONE,
    "created_by"          VARCHAR(255),
    "last_modified_at"    TIMESTAMP WITHOUT TIME ZONE,
    "last_modified_by"    VARCHAR(255),
    "address"             VARCHAR(255),
    "latitude"            FLOAT8,
    "longitude"           FLOAT8,
    "model"               VARCHAR(255),
    "name"                VARCHAR(255),
    "number"              VARCHAR(255),
    "perflog_recent_time" TIMESTAMP WITHOUT TIME ZONE,
    "status"              VARCHAR(30),
    "timezone"            VARCHAR(255),
    "version"             VARCHAR(255),
    CONSTRAINT "intersection_pkey" PRIMARY KEY ("id")
);

CREATE TABLE IF NOT EXISTS ${schema}."notification"
(
    "id"                  BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    "created_at"          TIMESTAMP WITHOUT TIME ZONE,
    "created_by"          VARCHAR(255),
    "last_modified_at"    TIMESTAMP WITHOUT TIME ZONE,
    "last_modified_by"    VARCHAR(255),
    "action"              TEXT,
    "agency_id"           INTEGER,
    "alarm_category_id"   BIGINT,
    "alarm_category_name" VARCHAR(255),
    "alarm_time_utc"      TIMESTAMP WITHOUT TIME ZONE,
    "content"             TEXT,
    "flag_status"         VARCHAR(30),
    "intersection_id"     VARCHAR(50),
    "intersection_name"   VARCHAR(255),
    "read_status"         VARCHAR(30),
    "read_time"           TIMESTAMP WITHOUT TIME ZONE,
    "send_status"         VARCHAR(255),
    "type_id"             BIGINT                                  NOT NULL,
    "user_id"             BIGINT,
    CONSTRAINT "notification_pkey" PRIMARY KEY ("id")
);

CREATE TABLE IF NOT EXISTS ${schema}."intersection_status_history"
(
    "id"               VARCHAR(50)                     NOT NULL,
    "created_at"       TIMESTAMP WITHOUT TIME ZONE,
    "created_by"       VARCHAR(255),
    "last_modified_at" TIMESTAMP WITHOUT TIME ZONE,
    "last_modified_by" VARCHAR(255),
    "time_from"        TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    "intersection_id"  VARCHAR(255),
    "status"           VARCHAR(255),
    "time_to"          TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT "intersection_status_history_pkey" PRIMARY KEY ("id")
);

CREATE TABLE IF NOT EXISTS ${schema}."corridor"
(
    "id"               VARCHAR(50) NOT NULL,
    "created_at"       TIMESTAMP WITHOUT TIME ZONE,
    "created_by"       VARCHAR(255),
    "last_modified_at" TIMESTAMP WITHOUT TIME ZONE,
    "last_modified_by" VARCHAR(255),
    "agency_id"        INTEGER     NOT NULL,
    "downstream"       INTEGER,
    "upstream"         INTEGER,
    "name"             VARCHAR(255),
    "speed"            FLOAT8,
    "status"           VARCHAR(30),
    CONSTRAINT "corridor_pkey" PRIMARY KEY ("id")
);

CREATE TABLE IF NOT EXISTS ${schema}."corridor_intersection"
(
    "id"               VARCHAR(50) NOT NULL,
    "created_at"       TIMESTAMP WITHOUT TIME ZONE,
    "created_by"       VARCHAR(255),
    "last_modified_at" TIMESTAMP WITHOUT TIME ZONE,
    "last_modified_by" VARCHAR(255),
    "distance"         FLOAT8,
    "downstream"       INTEGER,
    "number"           INTEGER,
    "speed"            FLOAT8,
    "upstream"         INTEGER,
    "corridor_id"      VARCHAR(50),
    "intersection_id"  VARCHAR(50),
    CONSTRAINT "corridor_intersection_pkey" PRIMARY KEY ("id")
);

CREATE TABLE IF NOT EXISTS ${schema}."users"
(
    "id"                  BIGINT       NOT NULL,
    "created_at"          TIMESTAMP WITHOUT TIME ZONE,
    "created_by"          VARCHAR(255),
    "last_modified_at"    TIMESTAMP WITHOUT TIME ZONE,
    "last_modified_by"    VARCHAR(255),
    "email"               VARCHAR(255) NOT NULL,
    "language"            VARCHAR(10),
    "user_key"            VARCHAR(255),
    "user_key_expiration" date,
    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

CREATE INDEX IF NOT EXISTS "idx_intersection" ON ${schema}."intersection" ("status", "longitude", "latitude");

CREATE INDEX IF NOT EXISTS "idx_notification" ON ${schema}."notification" ("user_id", "agency_id", "type_id", "read_status");

CREATE INDEX IF NOT EXISTS "intersection_status_hist_iid_idx" ON ${schema}."intersection_status_history" ("intersection_id");

ALTER TABLE ${schema}."users"
    ADD CONSTRAINT "idx_email" UNIQUE ("email");

ALTER TABLE ${schema}."users"
    ADD CONSTRAINT "ukgwq1uxh9fwn7jl9ij9oo1tlij" UNIQUE ("user_key");

CREATE SEQUENCE IF NOT EXISTS ${schema}."intersection_status_history_seq" AS bigint START WITH 1 INCREMENT BY 50 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE ${schema}."agency_intersection"
(
    "id"              BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    "agency_id"       INTEGER                                 NOT NULL,
    "intersection_id" VARCHAR(255)                            NOT NULL,
    CONSTRAINT "agency_intersection_pkey" PRIMARY KEY ("id")
);

CREATE TABLE ${schema}."agency_license"
(
    "agency_id"        INTEGER      NOT NULL,
    "created_at"       TIMESTAMP WITHOUT TIME ZONE,
    "created_by"       VARCHAR(255),
    "last_modified_at" TIMESTAMP WITHOUT TIME ZONE,
    "last_modified_by" VARCHAR(255),
    "license_ids"      VARCHAR(255) NOT NULL,
    CONSTRAINT "agency_license_pkey" PRIMARY KEY ("agency_id")
);

CREATE TABLE ${schema}."agency_settings"
(
    "agency_id"                      INTEGER NOT NULL,
    "created_at"                     TIMESTAMP WITHOUT TIME ZONE,
    "created_by"                     VARCHAR(255),
    "last_modified_at"               TIMESTAMP WITHOUT TIME ZONE,
    "last_modified_by"               VARCHAR(255),
    "name"                           VARCHAR(255),
    "status"                         BOOLEAN,
    "central_intersection_latitude"  FLOAT8,
    "central_intersection_longitude" FLOAT8,
    "display_settings_lob"           TEXT,
    "saturation_lob"                 TEXT,
    "show_unavailable_map_data"      BOOLEAN,
    "top_left_lat"                   FLOAT8,
    "top_left_long"                  FLOAT8,
    "zone_offset"                    VARCHAR(9),
    CONSTRAINT "agency_settings_pkey" PRIMARY KEY ("agency_id")
);

CREATE TABLE ${schema}."agency_user_setting"
(
    "id"        BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    "agency_id" INTEGER                                 NOT NULL,
    "user_id"   BIGINT                                  NOT NULL,
    CONSTRAINT "agency_user_setting_pkey" PRIMARY KEY ("id")
);

CREATE TABLE ${schema}."dashboard_setting"
(
    "id"                        BIGINT NOT NULL,
    "map_view_widget"           TEXT,
    "notification_widget"       TEXT,
    "open_alarm_widget"         TEXT,
    "summary_report_widget"     TEXT,
    "top_int_open_alarm_widget" TEXT,
    "volume_report_widget"      TEXT,
    CONSTRAINT "dashboard_setting_pkey" PRIMARY KEY ("id")
);

CREATE TABLE ${schema}."noti_setting"
(
    "id"           BIGINT NOT NULL,
    "as_enabled"   BOOLEAN,
    "as_frequency" VARCHAR(255),
    CONSTRAINT "noti_setting_pkey" PRIMARY KEY ("id")
);

CREATE TABLE ${schema}."notification_message"
(
    "language"        VARCHAR(255) NOT NULL,
    "notification_id" BIGINT       NOT NULL,
    "description"     TEXT,
    "name"            TEXT,
    CONSTRAINT "notification_message_pkey" PRIMARY KEY ("language", "notification_id")
);

ALTER TABLE ${schema}."corridor_intersection"
    ADD CONSTRAINT "fk4qku2gdqaifsqbmlyfciq1dac" FOREIGN KEY ("corridor_id") REFERENCES ${schema}."corridor" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION;

ALTER TABLE ${schema}."dashboard_setting"
    ADD CONSTRAINT "fk9w7yryn1nuxa9kx31e7lvtfr4" FOREIGN KEY ("id") REFERENCES ${schema}."agency_user_setting" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION;

ALTER TABLE ${schema}."corridor_intersection"
    ADD CONSTRAINT "fkban0qjnsdnkng12cgdqhyut2m" FOREIGN KEY ("intersection_id") REFERENCES ${schema}."intersection" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION;

ALTER TABLE ${schema}."notification_message"
    ADD CONSTRAINT "fkkj2b7yqd7rmgfcvrnbf11f27f" FOREIGN KEY ("notification_id") REFERENCES ${schema}."notification" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION;

ALTER TABLE ${schema}."noti_setting"
    ADD CONSTRAINT "fknkfkd6wms9cxu2n0u4j38btr3" FOREIGN KEY ("id") REFERENCES ${schema}."agency_user_setting" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION;

