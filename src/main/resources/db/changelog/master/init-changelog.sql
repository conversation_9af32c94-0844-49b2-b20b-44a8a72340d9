-- liquibase formatted sql

-- changeset toanpham:1735272047962-1
CREATE TABLE IF NOT EXISTS "public"."agency_schema"
(
    "id"               BIGINT       NOT NULL,
    "activated"        BOOLEAN      NOT NULL,
    "agency_name"      VARCHAR(255),
    "created_at"       TIMES<PERSON>MP WITHOUT TIME ZONE,
    "last_modified_at" TIMESTAMP WITHOUT TIME ZONE,
    "schema"           VARCHAR(255) NOT NULL,
    CONSTRAINT "agency_schemaPK" PRIMARY KEY ("id")
);

ALTER TABLE "public"."agency_schema"
    ADD CONSTRAINT "uc_agency_schemaschema_col" UNIQUE ("schema");

