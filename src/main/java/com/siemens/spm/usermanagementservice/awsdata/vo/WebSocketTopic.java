package com.siemens.spm.usermanagementservice.awsdata.vo;

public enum WebSocketTopic {

    NOTIFICATION("notification"), TSP_EVENT("tsp_event");

    private String value;

    public String getValue() {
        return this.value;
    }

    WebSocketTopic(String value) {
        this.value = value;
    }

    public static WebSocketTopic from(String value) {
        for (WebSocketTopic webSocketTopic : WebSocketTopic.values()) {
            if (webSocketTopic.getValue().equals(value)) {
                return webSocketTopic;
            }
        }

        return null;
    }

}
