package com.siemens.spm.usermanagementservice.awsdata.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.usermanagementservice.api.vo.TspEventIntersectionVO;
import com.siemens.spm.usermanagementservice.domain.Notification;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WebSocketContentVO {

    @JsonProperty("topic")
    private String topic;

    @JsonProperty("tsp_event")
    private TspEventIntersectionVO tspIntersection;

    @JsonProperty("notification")
    private Notification notification;

}
