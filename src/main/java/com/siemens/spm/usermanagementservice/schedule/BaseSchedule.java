/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : BaseSchedule.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.schedule;

import com.siemens.spm.common.shared.domaintype.IntersectionStatus;

public abstract class BaseSchedule {

    /**
     * @param status {@code True | False}
     * @return Mapped status string {@code True:enabled | False:disabled}
     */
    protected final String getStatusString(IntersectionStatus status) {
        return status == IntersectionStatus.AVAILABLE ? "enabled" : "disabled";
    }

    public abstract void execute();
}
