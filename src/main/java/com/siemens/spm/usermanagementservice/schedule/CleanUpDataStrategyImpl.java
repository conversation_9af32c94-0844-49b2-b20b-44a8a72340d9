/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : CleanUpDataStrategyImpl.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.schedule;

import com.siemens.spm.common.agency.supports.AgencyAware;
import com.siemens.spm.common.shared.domaintype.IntersectionStatus;
import com.siemens.spm.usermanagementservice.config.AsyncConfig;
import com.siemens.spm.usermanagementservice.notificationdata.strategy.CleanUpNotificationStrategy;
import com.siemens.spm.usermanagementservice.repository.IntersectionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class CleanUpDataStrategyImpl implements CleanUpDataStrategy {

    private final IntersectionRepository intersectionRepository;

    private final CleanUpNotificationStrategy cleanUpNotificationStrategy;

    @Value("${data-retention.retention-days.enabled-data}")
    protected Long disabledDataRetentionDays;

    @Value("${data-retention.retention-days.disabled-data}")
    protected Long enabledDataRetentionDays;

    @Override
    @AgencyAware(agencyId = "[0]")
    @Async(AsyncConfig.SCHEDULE_ASYNC_EXECUTOR)
    public void cleanUpNotificationAndMessages(Integer agencyId, LocalDateTime timeDelete) {

        List<String> availableIntIds = new ArrayList<>();
        List<String> unavailableIntIds = new ArrayList<>();

        intersectionRepository.findAll()
                .forEach(
                        intersection -> {
                            String intersectionId = intersection.getId();
                            IntersectionStatus status = IntersectionStatus.valueOf(intersection.getStatus());
                            switch (status) {
                            case UNAVAILABLE -> unavailableIntIds.add(intersectionId);
                            case AVAILABLE -> availableIntIds.add(intersectionId);
                            }
                        }
                );

        //cleanup available intersections
        cleanUpNotificationStrategy.cleanUpNotificationAndMessage(timeDelete.minusDays(enabledDataRetentionDays),
                availableIntIds);

        //cleanup unavailable intersections
        cleanUpNotificationStrategy.cleanUpNotificationAndMessage(timeDelete.minusDays(disabledDataRetentionDays),
                unavailableIntIds);
    }
}
