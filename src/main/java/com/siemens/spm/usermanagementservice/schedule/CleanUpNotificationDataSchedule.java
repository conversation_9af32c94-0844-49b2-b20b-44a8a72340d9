/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : CleanUpNotificationDataSchedule.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.schedule;

import com.siemens.spm.common.agency.master.repository.AgencySchemaRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
@AutoConfiguration
@ConditionalOnProperty(name = "data-retention.schedulers.clean-up-notification-data.enabled",
        havingValue = "true")
public class CleanUpNotificationDataSchedule extends BaseSchedule {

    private final CleanUpDataStrategy cleanUpDataStrategy;

    private final AgencySchemaRepository agencySchemaRepository;

    final String CLEANUP_TYPE = "Notification data retention";

    @Scheduled(cron = "${data-retention.schedulers.clean-up-notification-data.cron}")
    public void execute() {
        String result = "SUCCEED";
        LocalDateTime today = LocalDate.now().atStartOfDay();
        try {
            log.info("{}: Begin cleaning up data", CLEANUP_TYPE);

            //TODO change to find all except master later
            List<Integer> allAgencyIds = agencySchemaRepository.findAgencyIdByActivatedIsTrue();
            if (allAgencyIds.isEmpty()) {
                log.info("Not found any agency");
                return;
            }

            log.info("Found {} agencies", allAgencyIds.size());

            for (Integer agencyId : allAgencyIds) {
                cleanUpDataStrategy.cleanUpNotificationAndMessages(agencyId, today);
            }

        } catch (Exception e) {
            log.error("{}: Failed to cleanup data", CLEANUP_TYPE, e);
            result = "FAILED";
        } finally {
            log.info("{}: End cleaning up data {}", CLEANUP_TYPE, result);
        }
    }
}
