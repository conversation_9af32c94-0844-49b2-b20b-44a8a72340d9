package com.siemens.spm.usermanagementservice.sse;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SseMessage implements Serializable {

    private Long userId;

    /**
     * The agencyId of the user who is receiving the message. It can be {@code null} if the user is a super admin.
     */
    private Integer agencyId;

    private Object message;

}
