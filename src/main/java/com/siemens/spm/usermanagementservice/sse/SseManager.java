package com.siemens.spm.usermanagementservice.sse;

import java.util.List;

public interface SseManager {

    AdvancedSseEventEmitter establishConnection(Long userId, Integer agencyId, List<String> topics) throws SseException;

    void sendMessageToTopic(SseMessage sseMessage, String topic);

    void subscribeTopics(String connectionId, List<String> topics)
            throws InvalidTopicException, ConnectionNotFoundException;

    void unsubscribeTopics(String connectionId, List<String> topics)
            throws InvalidTopicException, ConnectionNotFoundException;

}
