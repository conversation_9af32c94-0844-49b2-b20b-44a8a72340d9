package com.siemens.spm.usermanagementservice.sse;

import com.siemens.spm.usermanagementservice.config.MessagingConfig;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

@Service
public class SseMessagePublisher {

    @Autowired
    private RedisTemplate<String, SseMessage> redisTemplate;

    public void publish(SseMessage message, String channel) {
        if (MessagingConfig.SUPPORTED_TOPICS.contains(channel)) {
            redisTemplate.convertAndSend(channel, message);
        }
    }

}
