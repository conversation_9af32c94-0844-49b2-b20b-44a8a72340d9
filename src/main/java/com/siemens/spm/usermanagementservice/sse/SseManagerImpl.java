package com.siemens.spm.usermanagementservice.sse;

import java.io.IOException;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CopyOnWriteArrayList;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import com.siemens.spm.usermanagementservice.config.MessagingConfig;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class SseManagerImpl implements SseManager {

    // Currently, We have 1 10 minutes time out configuration at the Load Balancer.
    // So we need to set the timeout less than 10 minutes to avoid the connection is closed by the Load Balancer
    @Value("${sse.timeout:9}")
    private int sseTimeoutInMinutes;

    private final List<AdvancedSseEventEmitter> emitters = new CopyOnWriteArrayList<>();

    @Override
    public AdvancedSseEventEmitter establishConnection(Long userId, Integer agencyId, List<String> topics)
            throws SseException {
        AdvancedSseEventEmitter emitter = createEmitter(userId, agencyId, topics);

        emitters.add(emitter);

        // Direct send the connection event to the client to complete the connection establishment process
        //  I don't know why but if I don't send this event, the connection will not be established
        // TODO: Need to find out why
        try {
            SseEmitter.SseEventBuilder event = SseEmitter.event()
                    .id(UUID.randomUUID().toString())
                    .name("Connection_Id")
                    .data(emitter.getId());
            emitter.send(event);
        } catch (IOException e) {
            throw new SseException(e);
        }

        return emitter;
    }

    @Override
    public void sendMessageToTopic(SseMessage sseMessage, String topic) {
        // TODO: Improve performance by using lock only on emitters
        Long userId = sseMessage.getUserId();
        Integer agencyId = sseMessage.getAgencyId();

        for (AdvancedSseEventEmitter emitter : emitters) {
            if (!emitter.getTopics().contains(topic) || !emitter.getUserId().equals(userId)) {
                continue;
            }

            if (agencyId != null && !emitter.getAgencyId().equals(agencyId)) {
                continue;
            }

            SseEmitter.SseEventBuilder event = SseEmitter.event()
                    .id(UUID.randomUUID().toString())
                    .name(topic)
                    .data(sseMessage.getMessage());
            try {
                emitter.send(event);
            } catch (IOException e) {
                log.error(String.format("Error while sending message to user: %s in agency: %s via channel: %s",
                        userId, agencyId, topic), e.getMessage());
                // Log stack trace as debug level to avoid spamming the log
                log.debug("Error while sending message to user: {} in agency: {} via channel: {}",
                        userId, agencyId, topic, e);
            }
        }
    }

    @Override
    public void subscribeTopics(String connectionId, List<String> topics)
            throws InvalidTopicException, ConnectionNotFoundException {
        // FIXME: This method is definitely not thread-safe at the moment and definitely cause problems.
        //  Need to find a way to make it thread-safe
        // 1. Validate topics
        validateTopics(topics);

        AdvancedSseEventEmitter emitter = findEmitter(connectionId);
        if (emitter == null) {
            throw new ConnectionNotFoundException("Connection not found: " + connectionId);
        }

        emitter.addTopics(topics);
    }

    @Override
    public void unsubscribeTopics(String connectionId, List<String> topics)
            throws InvalidTopicException, ConnectionNotFoundException {
        // FIXME: This method is definitely not thread-safe at the moment and definitely cause problems.
        //  Need to find a way to make it thread-safe
        validateTopics(topics);

        AdvancedSseEventEmitter emitter = findEmitter(connectionId);
        if (emitter == null) {
            throw new ConnectionNotFoundException("Connection not found: " + connectionId);
        }

        topics.forEach(emitter.getTopics()::remove);
        // We still hold the emitter (SSE connection) even if it has no topic.
        // Then the client can subscribe again, otherwise the connection will be closed automatically after timeout
    }

    private AdvancedSseEventEmitter createEmitter(Long userId, Integer agencyId, List<String> topics) {
        AdvancedSseEventEmitter emitter = AdvancedSseEventEmitter
                .init(userId, agencyId, topics, sseTimeoutInMinutes * 60 * 1000L);
        emitter.onCompletion(() -> {
            emitters.remove(emitter);
            log.debug("SSE connection is closed. Connection Id: {}", emitter.getId());
        });
        emitter.onTimeout(() -> {
            emitter.complete();
            log.debug("SSE connection is timeout. Connection Id: {}", emitter.getId());
        });
        emitter.onError(e -> {
            emitter.completeWithError(e);
            emitters.remove(emitter);
            log.error("SSE connection is error. Connection Id: " + emitter.getId(), e);
        });
        return emitter;
    }

    private void validateTopics(List<String> topics) throws InvalidTopicException {
        // FIXME: This method currently validate on static list of topics.
        //  This could be improve to support dynamic topics(ex: topic based on agency)
        for (String topic : topics) {
            if (!MessagingConfig.SUPPORTED_TOPICS.contains(topic)) {
                throw new InvalidTopicException("Topic is not supported: " + topic);
            }
        }
    }

    private AdvancedSseEventEmitter findEmitter(String connectionId) {
        for (AdvancedSseEventEmitter emitter : emitters) {
            if (emitter.getId().equals(connectionId)) {
                return emitter;
            }
        }
        return null;
    }

}
