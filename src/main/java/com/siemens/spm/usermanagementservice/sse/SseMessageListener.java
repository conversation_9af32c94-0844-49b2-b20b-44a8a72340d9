package com.siemens.spm.usermanagementservice.sse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class SseMessageListener implements MessageListener {

    @Autowired
    private RedisSerializer<SseMessage> sseMessageRedisSerializer;

    @Autowired
    private SseManager sseManager;

    @Override
    public void onMessage(Message message, byte[] pattern) {
        String channel = new String(message.getChannel());
        byte[] body = message.getBody();
        log.trace("Received message body: {} from channel: {}", message, channel);

        SseMessage sseMessage;
        try {
            sseMessage = sseMessageRedisSerializer.deserialize(body);
        } catch (Exception e) {
            log.error("Error while deserializing message: {}", body, e);
            return;
        }

        if (sseMessage == null) {
            log.warn("Ignore a NULL message received from channel: {}", channel);
            return;
        }

        sseManager.sendMessageToTopic(sseMessage, channel);
    }

}
