package com.siemens.spm.usermanagementservice.sse;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;

import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
public class AdvancedSseEventEmitter extends SseEmitter {

    private final String id;

    private final Long userId;

    private final Integer agencyId;

    private final Set<String> topics;

    private AdvancedSseEventEmitter(String id,
                                    Long userId,
                                    Integer agencyId,
                                    List<String> topics,
                                    Long timeout) {
        super(timeout);
        this.id = id;
        this.userId = userId;
        this.agencyId = agencyId;
        this.topics = new HashSet<>(topics);
    }

    public static AdvancedSseEventEmitter init(Long userId, Integer agencyId, List<String> topics, Long timeout) {
        Objects.requireNonNull(userId, "userId cannot be null");

        String id = UUID.randomUUID().toString();

        return new AdvancedSseEventEmitter(id, userId, agencyId, topics, timeout);
    }

    public void addTopics(List<String> topics) {
        this.topics.addAll(topics);
    }

}
