package com.siemens.spm.usermanagementservice.security;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Component;

import com.siemens.spm.usermanagementservice.domain.Notification;
import com.siemens.spm.usermanagementservice.repository.NotificationRepository;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class NotificationAuthorizationHelper {

    @Autowired
    private NotificationRepository notificationRepository;

    public Long getOwnerId(Long notificationId) {
        // NOTE: This could be improved by fetching the userId only, not the whole notification
        return notificationRepository.findById(notificationId)
                .map(Notification::getUserId)
                .orElseThrow(() -> new AccessDeniedException("Not found userId for notificationId: " + notificationId));
    }

    public Long getOwnerId(List<Long> notificationIds) {
        // NOTE: This could be improved by fetching the userId only, not the whole notification
        List<Notification> notifications = notificationRepository.findAllById(notificationIds);

        Long ownerId = null;
        for (Notification notification : notifications) {
            Long userId = notification.getUserId();
            if (userId == null) {
                throw new AccessDeniedException("Not found userId for notificationId: " + notification.getId());
            }
            if (ownerId == null) {
                ownerId = notification.getUserId();
            } else if (ownerId.longValue() != userId.longValue()) {
                throw new AccessDeniedException("Cannot access notifications from different users");
            }
        }

        if (ownerId == null) {
            throw new AccessDeniedException("Not found userId for notificationIds: " + notificationIds);
        }
        return ownerId;
    }

}
