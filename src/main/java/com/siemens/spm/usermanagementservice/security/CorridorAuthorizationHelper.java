package com.siemens.spm.usermanagementservice.security;

import java.nio.file.AccessDeniedException;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.siemens.spm.usermanagementservice.domain.Corridor;
import com.siemens.spm.usermanagementservice.repository.CorridorRepository;

@Component
public class CorridorAuthorizationHelper {

    @Autowired
    private CorridorRepository corridorRepository;

    // This could be optimized by only fetch the agencyId instead of the whole corridor entity

    public Integer verifyAndGetAgencyId(Integer agencyId, String corridorId) throws AccessDeniedException {
        if (agencyId == null || corridorId == null) {
            throw new IllegalArgumentException("agencyId and corridorId cannot be null");
        }

        return corridorRepository.findById(corridorId)
                .map(Corridor::getAgencyId)
                .filter(agencyId::equals)
                .orElseThrow(() -> new AccessDeniedException("Cannot access corridor from different agency"));
    }

    public Integer verifyAndGetAgencyId(Integer agencyId, List<String> corridorIds) throws AccessDeniedException {
        if (agencyId == null || corridorIds == null) {
            throw new IllegalArgumentException("agencyId and corridorIds cannot be null");
        }

        boolean sameAgencyId = corridorRepository.findAllById(corridorIds)
                .stream()
                .map(Corridor::getAgencyId)
                .distinct()
                .anyMatch(corridorAgencyId -> corridorAgencyId == null || (int) corridorAgencyId == agencyId);
        if (!sameAgencyId) {
            throw new AccessDeniedException("Cannot access corridors from different agencies");
        }
        return agencyId;
    }

}
