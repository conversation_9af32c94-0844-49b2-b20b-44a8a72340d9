/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : CleanUpNotificationStrategy.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.notificationdata.strategy;

import java.time.LocalDateTime;
import java.util.List;

public interface CleanUpNotificationStrategy {

    void cleanUpNotificationAndMessage(LocalDateTime cleanUpTime, List<String> intersectionIds);
}
