/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : NotificationVOBuilder.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.notificationdata.util;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.util.StringUtils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.siemens.spm.common.constant.CommonConstants;
import com.siemens.spm.common.message.MessageService;
import com.siemens.spm.common.shared.domaintype.notification.NotificationContentVO;
import com.siemens.spm.common.shared.domaintype.notification.NotificationType;
import com.siemens.spm.common.shared.vo.DynamicContentVO;
import com.siemens.spm.common.util.BeanFinder;
import com.siemens.spm.common.util.NotificationUtil;
import com.siemens.spm.usermanagementservice.api.vo.AlarmManagementContentVO;
import com.siemens.spm.usermanagementservice.api.vo.AlarmNotificationContentVO;
import com.siemens.spm.usermanagementservice.api.vo.NotificationDetailVO;
import com.siemens.spm.usermanagementservice.api.vo.NotificationSearchVO;
import com.siemens.spm.usermanagementservice.api.vo.NotificationSimpleVO;
import com.siemens.spm.usermanagementservice.api.vo.PMManagementContentVO;
import com.siemens.spm.usermanagementservice.api.vo.PMResultsContentVO;
import com.siemens.spm.usermanagementservice.api.vo.SummaryReportManagementContentVO;
import com.siemens.spm.usermanagementservice.api.vo.SummaryResultContentVO;
import com.siemens.spm.usermanagementservice.api.vo.UserAgencyDeletionContentVO;
import com.siemens.spm.usermanagementservice.domain.Notification;
import com.siemens.spm.usermanagementservice.domain.NotificationMessage;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public final class NotificationVOBuilder {

    private NotificationVOBuilder() {
    }

    /**
     * Object mapper is thread-safe after initialization
     */
    private static final ObjectMapper objectMapper = BeanFinder.getDefaultObjectMapper();

    /**
     * Convert notification to simple value object {@link NotificationSimpleVO}
     *
     * @param notification
     * @return SimpleNotificationVO
     * @throws JsonProcessingException
     * @throws JsonMappingException
     */
    public static NotificationSimpleVO buildNotificationSimpleVO(Notification notification) {
        if (notification == null)
            return null;

        JsonNode actionNode = NotificationUtil.buildActionNode(notification.getAction());

        return NotificationSimpleVO.builder()
                .action(actionNode)
                .name(getName(notification.getNotificationMessage()))
                .description(getDescription(notification.getNotificationMessage()))
                .createdAt(notification.getCreatedAt())
                .flagStatus(notification.getFlagStatus())
                .id(notification.getId())
                .readStatus(notification.getReadStatus())
                .lastModifiedAt(notification.getLastModifiedAt())
                .build();
    }

    /**
     * Build a notification search vo from a given notification {@link NotificationSearchVO}
     *
     * @param notification
     * @return NotificationSearchVO
     * @throws JsonMappingException
     * @throws JsonProcessingException
     */
    public static NotificationSearchVO buildNotificationSearchVO(Notification notification) {
        String intersectionName = notification.getIntersectionName();

        if (intersectionName == null) {
            intersectionName = CommonConstants.NOT_AVAILABLE;
        }

        JsonNode actionNode = NotificationUtil.buildActionNode(notification.getAction());

        return NotificationSearchVO.builder()
                .name(getName(notification.getNotificationMessage()))
                .description(getDescription(notification.getNotificationMessage()))
                .createdAt(notification.getCreatedAt())
                .flagStatus(notification.getFlagStatus())
                .id(notification.getId())
                .intersectionName(intersectionName)
                .readStatus(notification.getReadStatus())
                .type(NotificationType.getById(notification.getTypeId()).getTranslatedName())
                .lastModifiedAt(notification.getLastModifiedAt())
                .action(actionNode)
                .build();
    }

    public static NotificationDetailVO buildNotificationVO(Notification notification, MessageService messageService) {
        JsonNode actionNode = NotificationUtil.buildActionNode(notification.getAction(), messageService);
        return buildNotificationVO(notification, actionNode);
    }

    private static NotificationDetailVO buildNotificationVO(Notification notification, JsonNode actionNode) {
        NotificationDetailVO newNotificationDetailVO = NotificationDetailVO.builder()
                .name(getName(notification.getNotificationMessage()))
                .description(getDescription(notification.getNotificationMessage()))
                .readStatus(notification.getReadStatus())
                .createdAt(notification.getCreatedAt())
                .id(notification.getId())
                .type(NotificationType.getById(notification.getTypeId()).getTranslatedName())
                .lastModifiedAt(notification.getLastModifiedAt())
                .action(actionNode)
                .build();

        buildDynamicContent(notification, newNotificationDetailVO);

        return newNotificationDetailVO;
    }

    public static void buildDynamicContent(Notification notification, NotificationDetailVO notificationDetailVO) {
        if (notification == null || notificationDetailVO == null) {
            return;
        }

        NotificationType notificationType = NotificationType.getById(notification.getTypeId());
        String notificationContent = notification.getContent();

        if (!StringUtils.hasText(notificationContent)) {
            return;
        }

        try {
            List<DynamicContentVO> dynamicContents = new ArrayList<>();

            String content = notification.getContent();

            NotificationContentVO notificationContentVO;
            switch (notificationType) {
            case ALARM_NOTIFICATION:
                notificationContentVO = objectMapper.readValue(content, AlarmNotificationContentVO.class);
                break;
            case ALARM_MANAGEMENT:
                notificationContentVO = objectMapper.readValue(content, AlarmManagementContentVO.class);
                break;
            case SUMMARY_REPORT_NOTIFICATION:
                notificationContentVO = objectMapper.readValue(content, SummaryResultContentVO.class);
                break;
            case SUMMARY_REPORT_MANAGEMENT:
                notificationContentVO = objectMapper.readValue(content, SummaryReportManagementContentVO.class);
                break;
            case PERFORMANCE_METRIC_NOTIFICATION:
                notificationContentVO = objectMapper.readValue(content, PMResultsContentVO.class);
                break;
            case PERFORMANCE_METRIC_MANAGEMENT:
                notificationContentVO = objectMapper.readValue(content, PMManagementContentVO.class);
                break;
            case USER_AGENCY_DELETION:
                notificationContentVO = objectMapper.readValue(content, UserAgencyDeletionContentVO.class);
                break;
            default:
                notificationContentVO = null;
                break;
            }

            if (notificationContentVO != null) {
                dynamicContents.addAll(notificationContentVO.getDynamicContents());
            }

            if (!dynamicContents.isEmpty()) {
                notificationDetailVO.setDynamicContent(dynamicContents);
            }
        } catch (Exception ex) {
            log.error("Cannot generate dynamic content for notification VO!", ex);
        }
    }

    private static Map<String, String> getName(List<NotificationMessage> notiMessages) {
        if (notiMessages == null || notiMessages.isEmpty()) {
            return Collections.emptyMap();
        }

        Map<String, String> nameMap = new HashMap<>();
        for (NotificationMessage message : notiMessages) {
            if (message.getId().getLanguage() != null) {
                nameMap.put(message.getId().getLanguage(), message.getName());
            }
        }

        return nameMap;
    }

    private static Map<String, String> getDescription(List<NotificationMessage> notiMessages) {
        if (notiMessages == null || notiMessages.isEmpty()) {
            return Collections.emptyMap();
        }

        Map<String, String> descriptionMap = new HashMap<>();
        for (NotificationMessage message : notiMessages) {
            if (message.getId().getLanguage() != null) {
                descriptionMap.put(message.getId().getLanguage(), message.getDescription());
            }
        }

        return descriptionMap;
    }

}
