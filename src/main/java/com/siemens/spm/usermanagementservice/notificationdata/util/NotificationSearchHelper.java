/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : NotificationSearchHelper.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.notificationdata.util;

import java.util.Arrays;
import java.util.List;

import com.querydsl.core.types.OrderSpecifier;
import com.siemens.spm.common.audit.AuditableEntity;
import com.siemens.spm.common.util.OrderUtil;
import com.siemens.spm.usermanagementservice.domain.Notification;
import com.siemens.spm.usermanagementservice.domain.QNotification;

public final class NotificationSearchHelper {

    private static final List<String> SORTABLE_COLUMNS = Arrays.asList(Notification.ColumnName.ID,
                                                                       Notification.ColumnName.NAME,
                                                                       AuditableEntity.ColumnName.CREATED_AT,
                                                                       AuditableEntity.ColumnName.LAST_MODIFIED_AT);

    private NotificationSearchHelper() {}

    @SuppressWarnings({ "unchecked" })
    public static OrderSpecifier<String>[] createOrderBy(String[] orderByColumns) {
        if (orderByColumns == null || orderByColumns.length == 0) {
            return new OrderSpecifier[0];
        }

        String entityName = QNotification.notification.getMetadata().getName();
        return OrderUtil.createOrderBy(entityName, orderByColumns, SORTABLE_COLUMNS);
    }

}
