/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : NotificationHelper.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.notificationdata.util;

import com.siemens.spm.common.constant.NotificationConstants;
import com.siemens.spm.common.message.MessageService;
import com.siemens.spm.common.security.SecurityUtils;
import com.siemens.spm.common.shared.vo.NotificationMessageRequestVO;
import com.siemens.spm.common.util.BeanFinder;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public final class NotificationUtil {

    private NotificationUtil() {
    }

    /**
     * @param status
     * @return boolean
     */
    public static boolean isValidReadStatus(String status) {
        return NotificationConstants.ReadStatus.READ.getValue().equals(status)
                || NotificationConstants.ReadStatus.UNREAD.getValue().equals(status);
    }

    /**
     * @param status
     * @return boolean
     */
    public static boolean isValidFlagStatus(String status) {
        return NotificationConstants.FlagStatus.FLAG.getValue().equals(status)
                || NotificationConstants.FlagStatus.UNFLAG.getValue().equals(status);
    }

    public static List<NotificationMessageRequestVO> createAgencyDeletionMessage(String targetDeletion,
                                                                                 List<String> agencies) {

        MessageService messageService = BeanFinder.getDefaultMessageService();
        final String nameKey = String.format("%s-deletion.title", targetDeletion);

        Map<String, String> nameMap = messageService.getMessages(nameKey, null);
        if (nameMap == null) {
            nameMap = new HashMap<>();
            nameMap.put(SecurityUtils.getCurrentUserLanguage(), nameKey);
        }

        String agencyStr = String.join(", ", agencies);
        String[] params = { agencyStr };
        String descriptionKey = "user-agency-deletion.notification.missing_user";

        Map<String, String> contentMap = messageService.getMessages(descriptionKey, params);
        if (contentMap == null) {
            contentMap = new HashMap<>();
            contentMap.put(SecurityUtils.getCurrentUserLanguage(), descriptionKey);
        }

        List<NotificationMessageRequestVO> notificationMessageVOs = new ArrayList<>();
        for (Map.Entry<String, String> nameEntry : nameMap.entrySet()) {
            String language = nameEntry.getKey();
            if (language != null) {
                NotificationMessageRequestVO messageVO = NotificationMessageRequestVO.builder()
                        .name(nameEntry.getValue())
                        .description(contentMap.get(language))
                        .language(language)
                        .build();

                notificationMessageVOs.add(messageVO);
            }
        }

        return notificationMessageVOs;
    }
}
