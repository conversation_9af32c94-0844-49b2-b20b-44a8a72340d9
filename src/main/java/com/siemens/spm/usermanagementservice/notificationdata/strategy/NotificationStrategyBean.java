/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : NotificationStrategyBean.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.notificationdata.strategy;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.querydsl.core.types.OrderSpecifier;
import com.siemens.spm.common.constant.NotificationConstants;
import com.siemens.spm.common.message.MessageService;
import com.siemens.spm.common.security.SecurityUtils;
import com.siemens.spm.common.shared.domaintype.ActionType;
import com.siemens.spm.common.shared.domaintype.IntersectionStatus;
import com.siemens.spm.common.shared.domaintype.notification.NotificationType;
import com.siemens.spm.common.shared.exception.InvalidSortColumnException;
import com.siemens.spm.common.shared.exception.InvalidSortOrderException;
import com.siemens.spm.common.shared.resource.TextKey;
import com.siemens.spm.common.shared.vo.ActionVO;
import com.siemens.spm.common.shared.vo.NotificationMessageRequestVO;
import com.siemens.spm.common.shared.vo.NotificationTypeVO;
import com.siemens.spm.common.util.BeanFinder;
import com.siemens.spm.common.util.DateTimeUtils;
import com.siemens.spm.common.util.ObjectMapperUtil;
import com.siemens.spm.datahub.api.boundary.DataIntegrationService;
import com.siemens.spm.datahub.api.vo.response.AgencySchemaResponseVO;
import com.siemens.spm.spmstudiosdk.dto.StudioAgencyDto;
import com.siemens.spm.spmstudiosdk.dto.StudioUserDto;
import com.siemens.spm.spmstudiosdk.exception.StudioException;
import com.siemens.spm.spmstudiosdk.service.StudioAgencyService;
import com.siemens.spm.spmstudiosdk.service.StudioUserService;
import com.siemens.spm.usermanagementservice.api.vo.AlarmNotificationContentVO;
import com.siemens.spm.usermanagementservice.api.vo.NotificationSearchVO;
import com.siemens.spm.usermanagementservice.api.vo.NotificationSimpleVO;
import com.siemens.spm.usermanagementservice.api.vo.ReleaseNoteVO;
import com.siemens.spm.usermanagementservice.api.vo.request.NotificationSearchRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.NotificationUpdatedRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.NotificationsCreateRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.NotificationDetailsResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.NotificationLatestUnreadResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.NotificationManipulateResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.NotificationSearchResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.NotificationTypeListResultObject;
import com.siemens.spm.usermanagementservice.domain.Intersection;
import com.siemens.spm.usermanagementservice.domain.Notification;
import com.siemens.spm.usermanagementservice.domain.NotificationMessage;
import com.siemens.spm.usermanagementservice.domain.NotificationMessageKey;
import com.siemens.spm.usermanagementservice.intersectiondata.vo.IntersectionStatusHistoryV0;
import com.siemens.spm.usermanagementservice.notificationdata.util.NotificationBuilder;
import com.siemens.spm.usermanagementservice.notificationdata.util.NotificationMessageBuilder;
import com.siemens.spm.usermanagementservice.notificationdata.util.NotificationSearchHelper;
import com.siemens.spm.usermanagementservice.notificationdata.util.NotificationUtil;
import com.siemens.spm.usermanagementservice.notificationdata.util.NotificationVOBuilder;
import com.siemens.spm.usermanagementservice.repository.IntersectionRepository;
import com.siemens.spm.usermanagementservice.repository.NotificationRepository;
import com.siemens.spm.usermanagementservice.repository.NotificationTypeRepository;
import com.siemens.spm.usermanagementservice.repository.filterdata.IntersectionFilterDataVO;
import com.siemens.spm.usermanagementservice.repository.filterdata.NotificationFilterDataVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.LocaleUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Slf4j
@Service
@Transactional
public class NotificationStrategyBean implements NotificationStrategy {

    @Autowired
    private IntersectionRepository intersectionRepository;

    // repositories
    @Autowired
    private NotificationRepository notificationRepository;

    @Autowired
    private NotificationTypeRepository notificationTypeRepository;

    @Autowired
    private RealtimeNotificationSender realtimeNotificationSender;

    @Autowired
    private MessageService messageService;

    @Autowired
    private StudioUserService studioUserService;

    @Autowired
    private StudioAgencyService studioAgencyService;

    @Autowired
    private DataIntegrationService dataIntegrationService;

    @Autowired
    private ObjectMapper objectMapper;
    private static final String STUDIO_API_ERROR_MSG = "Error while calling Studio";
    private static final String STUDIO_USER_NOT_FOUND_MSG = "User with email {} not found from Studio";
    private static final String INTERNAL_SERVER_ERROR_MSG = "Internal server error";


    /**
     * {@inheritDoc}
     */
    @Override
    public void createNotificationsForUsersInternalAsync(NotificationsCreateRequestVO createRequest) {
        List<NotificationMessageRequestVO> notificationMessageVOs = createRequest.getNotificationMessages();
        if (notificationMessageVOs == null || notificationMessageVOs.isEmpty()) {
            log.error("Cannot create an empty notification!");
            return;
        }

        // Save notifications to database
        List<Notification> notifications = buildNotifications(createRequest);

        notificationRepository.saveAll(notifications);

        for (Notification notification : notifications) {
            List<NotificationMessage> messages = NotificationMessageBuilder.build(notification.getId(),
                    notificationMessageVOs);

            // this will save the notification message to database
            notification.setNotificationMessage(messages);
        }

        // Send to client(s) via sse
        realtimeNotificationSender.sendToUsersAsync(notifications);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void createReleaseNoteNotifications(ReleaseNoteVO releaseNoteVO) {
        log.debug("Sending release notes");

        if (releaseNoteVO == null) {
            log.info("Have no release notes provided, try to reading release notes from class path resources");

            ClassPathResource classPathResource = new ClassPathResource("notifications/release_notes.json");
            releaseNoteVO = ObjectMapperUtil.readValue(classPathResource, ReleaseNoteVO.class);
            if (releaseNoteVO == null) {
                log.info("Not found release notes from class path. Do not create release note notification");
                return;
            }
        }

        List<StudioAgencyDto> agencyDtoList;
        try {
            agencyDtoList = studioAgencyService.getAllActiveAgencies();
        } catch (StudioException e) {
            log.error(STUDIO_API_ERROR_MSG, e);
            return;
        }

        if (agencyDtoList.isEmpty()) {
            log.warn("Not found any active agency, don't send release note notification");
            return;
        }

        Set<Long> userIds = new HashSet<>();

        for (StudioAgencyDto agencyDto : agencyDtoList) {
            List<StudioUserDto> userDtoList;
            try {
                userDtoList = studioUserService.findAllByAgencyId(agencyDto.getAgencyNo());
            } catch (StudioException e) {
                log.error(STUDIO_API_ERROR_MSG, e);
                // Not interrupt whole process if cannot found user for specific agency
                continue;
            }

            userIds.addAll(userDtoList.stream()
                    .map(StudioUserDto::getId)
                    .map(Long::valueOf)
                    .toList()
            );
        }

        // NOTE: In the case: one user belong multi agencies:
        // 1. Save only ONE notification for one user in all agencies
        // --> Set agencyId is null in notification record
        // 2. Send notifications via socket connections to all pair <User - Agency> (Auto when agencyId is null)

        // NOTE: agencyId will be null here
        List<Notification> notificationsToSave = buildNotifications(releaseNoteVO, new ArrayList<>(userIds));
        notificationRepository.saveAll(notificationsToSave);

        // Save the notification message to database
        List<NotificationMessageRequestVO> notificationMsgRequestVOs = createNotificationMessages(releaseNoteVO);
        for (var notification : notificationsToSave) {
            List<NotificationMessage> messages = NotificationMessageBuilder
                    .build(notification.getId(), notificationMsgRequestVOs);
            notification.setNotificationMessage(messages);
        }

        realtimeNotificationSender.sendToUsersAsync(notificationsToSave);
    }

    /**
     * Build list of notification for release note without agencyId.
     *
     * @param releaseNoteVO release note to build notifications.
     * @param userIds       list of user's id to build notifications.
     * @return list of notification for release note. Each notification have a null agencyId.
     */
    private List<Notification> buildNotifications(ReleaseNoteVO releaseNoteVO, List<Long> userIds) {
        ActionVO actionVO = ActionVO.builder()
                .type(ActionType.OPEN_POPUP)
                .data(releaseNoteVO)
                .build();

        NotificationsCreateRequestVO requestVO = NotificationsCreateRequestVO.builder()
                .typeId(NotificationType.RELEASE_NOTE_NOTIFICATION.getId())
                .action(actionVO)
                .userIds(userIds.isEmpty() ? new ArrayList<>()
                        : userIds.stream().map(Long::intValue).toList())
                .build();

        return NotificationBuilder.buildNotifications(requestVO);
    }

    private List<NotificationMessageRequestVO> createNotificationMessages(ReleaseNoteVO releaseNoteVO) {
        Object[] descriptionParams = {
                releaseNoteVO.getVersion(),
                LocalDate.now().format(DateTimeFormatter.ISO_DATE)
        };

        return com.siemens.spm.common.util.NotificationUtil
                .createNotificationMessage(TextKey.RELEASE_NOTES_TITLE, null, TextKey.RELEASE_NOTES_DESC,
                        descriptionParams);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public NotificationLatestUnreadResultObject getLatestUnreadNotifications(Integer agencyId,
                                                                             Integer page,
                                                                             Integer size) {
        String userEmail = SecurityUtils.getCurrentUserEmail();
        Optional<StudioUserDto> userDtoOptional;
        try {
            userDtoOptional = studioUserService.findByEmail(userEmail);
        } catch (StudioException e) {
            log.error(STUDIO_API_ERROR_MSG, e);
            return new NotificationLatestUnreadResultObject(null,
                    NotificationLatestUnreadResultObject.StatusCode.ERROR);
        }
        if (userDtoOptional.isEmpty()) {
            log.debug(STUDIO_USER_NOT_FOUND_MSG, userEmail);
            return new NotificationLatestUnreadResultObject(null,
                    NotificationLatestUnreadResultObject.StatusCode.USER_NOT_FOUND);
        }
        Long userId = Long.valueOf(userDtoOptional.get().getId());

        List<Notification> notifications = notificationRepository
                .findPageByUserIdAndAgencyIdAndLatestUnread(userId, agencyId, page, size);

        Long totalCount = notificationRepository.countTotalByUserIdAndAgencyIdAndUnreadStatus(userId, agencyId);

        try {
            List<NotificationSimpleVO> notificationSimpleVOs = new ArrayList<>();

            for (var notification : notifications) {
                notificationSimpleVOs.add(NotificationVOBuilder.buildNotificationSimpleVO(notification));
            }

            var data = NotificationLatestUnreadResultObject.ResponseData.builder()
                    .notifications(notificationSimpleVOs)
                    .totalCount(totalCount)
                    .build();

            return new NotificationLatestUnreadResultObject(data,
                    NotificationLatestUnreadResultObject.StatusCode.SUCCESS);
        } catch (Exception ex) {
            log.error("Problem occurs while getting latest unread notifications", ex);

            return new NotificationLatestUnreadResultObject(null,
                    NotificationLatestUnreadResultObject.StatusCode.ERROR);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public NotificationSearchResultObject searchNotifications(NotificationSearchRequestVO searchRequest) {
        /* knt>20250103
         * CR: according to License Management business
         * - only show notifications for available intersections
         * */

        List<String> unavailableIntersectionIds = new ArrayList<>();
        try {
            IntersectionFilterDataVO intersectionFilter = buildUnavailableIntersectionFilter(searchRequest);
            List<Intersection> unavailableIntersections = intersectionRepository.findAllByFilter(intersectionFilter);
            if (CollectionUtils.isNotEmpty(unavailableIntersections)) {
                unavailableIntersectionIds = unavailableIntersections.stream().map(Intersection::getId).toList();
            }

        } catch (Exception e) {
            log.error(INTERNAL_SERVER_ERROR_MSG, e);
            unavailableIntersectionIds = new ArrayList<>();
        }

        String userEmail = SecurityUtils.getCurrentUserEmail();
        Optional<StudioUserDto> userDtoOptional;
        try {
            userDtoOptional = studioUserService.findByEmail(userEmail);
        } catch (StudioException e) {
            log.error(STUDIO_API_ERROR_MSG, e);
            return new NotificationSearchResultObject(null,
                    NotificationSearchResultObject.StatusCode.ERROR);
        }
        if (userDtoOptional.isEmpty()) {
            log.debug(STUDIO_USER_NOT_FOUND_MSG, userEmail);
            return new NotificationSearchResultObject(null,
                    NotificationSearchResultObject.StatusCode.USER_NOT_FOUND);
        }
        Long userId = Long.valueOf(userDtoOptional.get().getId());

        Pair<List<Notification>, Long> notificationsAndCountPair;

        try {
            notificationsAndCountPair = getNotificationsAndCountFromDB(searchRequest, userId,
                    unavailableIntersectionIds);
        } catch (InvalidSortColumnException ex) {
            return new NotificationSearchResultObject(null,
                    NotificationSearchResultObject.StatusCode.INVALID_SORT_COLUMN);
        } catch (InvalidSortOrderException ex) {
            return new NotificationSearchResultObject(null,
                    NotificationSearchResultObject.StatusCode.INVALID_SORT_ORDER);
        }

        List<Notification> notifications = notificationsAndCountPair.getFirst();

        Long totalCount = notificationsAndCountPair.getSecond();

        List<NotificationSearchVO> notificationSearchVOs = notifications.stream()
                .map(NotificationVOBuilder::buildNotificationSearchVO)
                .toList();

        var responseData = NotificationSearchResultObject.ResponseData.builder()
                .notifications(notificationSearchVOs)
                .totalCount(totalCount)
                .build();

        return new NotificationSearchResultObject(responseData,
                NotificationSearchResultObject.StatusCode.SUCCESS);
    }

    private IntersectionFilterDataVO buildUnavailableIntersectionFilter(NotificationSearchRequestVO searchRequest) {
        IntersectionFilterDataVO filter = IntersectionFilterDataVO.builder()
                .status(IntersectionStatus.UNAVAILABLE.getInsight()) //get unavailable intersection
                .build();

        Integer agencyId = searchRequest.getAgencyId();
        if (agencyId != null && agencyId > 0) {
            filter.setAgencyId(agencyId);
        }
        return filter;
    }

    private Pair<List<Notification>, Long> getNotificationsAndCountFromDB(NotificationSearchRequestVO searchRequest,
                                                                          Long userId,
                                                                          List<String> unavailableIds) {
        OrderSpecifier<String>[] orderSpecifiers = NotificationSearchHelper
                .createOrderBy(searchRequest.getOrderByColumns());

        var notificationFilterDataVO = NotificationFilterDataVO.builder()
                .agencyId(searchRequest.getAgencyId())
                .createdAtFrom(searchRequest.getCreatedAtFrom())
                .createdAtTo(searchRequest.getCreatedAtTo())
                .allowNullIntersection(true)
                .flagStatus(searchRequest.getFlagStatus())
                .excludeIntIds(unavailableIds)
                .readStatus(searchRequest.getReadStatus())
                .text(searchRequest.getText())
                .typeId(searchRequest.getTypeId())
                .userId(userId)
                .intersection(searchRequest.getIntersection())
                .build();

        List<Notification> notifications = notificationRepository.findPageByFilter(notificationFilterDataVO,
                orderSpecifiers, searchRequest.getPage(), searchRequest.getSize());

        long totalCount = notificationRepository.countTotalByFilter(notificationFilterDataVO);

        return Pair.of(notifications, totalCount);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public NotificationManipulateResultObject updateNotifications(NotificationUpdatedRequestVO updateRequest) {
        // notificationIds was validated by jakarta validation in
        // NotificationUpdatedRequestVO -> no need to validate again
        List<Long> notificationIds = updateRequest.getNotiIds();
        String newReadStatus = updateRequest.getReadStatus();
        String newFlagStatus = updateRequest.getFlagStatus();

        if (!(newReadStatus == null || NotificationUtil.isValidReadStatus(newReadStatus))) {
            log.error("Invalid read status: {}", newReadStatus);

            return new NotificationManipulateResultObject(
                    NotificationManipulateResultObject.StatusCode.INVALID_READ_STATUS);
        }

        if (!(newFlagStatus == null || NotificationUtil.isValidFlagStatus(newFlagStatus))) {
            return new NotificationManipulateResultObject(
                    NotificationManipulateResultObject.StatusCode.INVALID_FLAG_STATUS);
        }

        var filterDataVO = NotificationFilterDataVO.builder()
                .ids(notificationIds)
                .build();

        long totalCount = notificationRepository.countTotalByFilter(filterDataVO);

        if (totalCount == 0) {
            return new NotificationManipulateResultObject(
                    NotificationManipulateResultObject.StatusCode.NOTIFICATION_NOT_FOUND);
        }

        String userEmail = SecurityUtils.getCurrentUserEmail();
        Optional<StudioUserDto> userDtoOptional;
        try {
            userDtoOptional = studioUserService.findByEmail(userEmail);
        } catch (StudioException e) {
            log.error(STUDIO_API_ERROR_MSG, e);
            return new NotificationManipulateResultObject(NotificationManipulateResultObject.StatusCode.ERROR);
        }
        if (userDtoOptional.isEmpty()) {
            log.debug(STUDIO_USER_NOT_FOUND_MSG, userEmail);
            return new NotificationManipulateResultObject(NotificationManipulateResultObject.StatusCode.USER_NOT_FOUND);
        }

        Long userId = Long.valueOf(userDtoOptional.get().getId());
        filterDataVO.setUserId(userId);

        totalCount = notificationRepository.countTotalByFilter(filterDataVO);

        // Return forbidden when user try to update only invalid notification.
        if (totalCount == 0) {
            return new NotificationManipulateResultObject(
                    NotificationManipulateResultObject.StatusCode.USER_DOES_NOT_HAVE_PERMISSION);
        }

        // If contain valid notification, try to update them and
        // ignore invalid notification

        if (newReadStatus != null) {
            notificationRepository.changeReadStatusOfNotificationsByIdIn(userId, notificationIds, newReadStatus);
        }

        if (newFlagStatus != null) {
            notificationRepository.changeFlagStatusOfNotificationsByIdIn(userId, notificationIds, newFlagStatus);
        }

        return new NotificationManipulateResultObject(NotificationManipulateResultObject.StatusCode.NO_CONTENT);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public NotificationManipulateResultObject markAllNotificationsAsRead(Integer agencyId) {
        String userEmail = SecurityUtils.getCurrentUserEmail();
        Optional<StudioUserDto> userDtoOptional;
        try {
            userDtoOptional = studioUserService.findByEmail(userEmail);
        } catch (StudioException e) {
            log.error(STUDIO_API_ERROR_MSG, e);
            return new NotificationManipulateResultObject(NotificationManipulateResultObject.StatusCode.ERROR);
        }
        if (userDtoOptional.isEmpty()) {
            log.debug(STUDIO_USER_NOT_FOUND_MSG, userEmail);
            return new NotificationManipulateResultObject(NotificationManipulateResultObject.StatusCode.USER_NOT_FOUND);
        }

        Long userId = Long.valueOf(userDtoOptional.get().getId());
        notificationRepository.markAllUnreadNotificationsAsReadByUserIdAndAgencyId(userId, agencyId);

        return new NotificationManipulateResultObject(NotificationManipulateResultObject.StatusCode.NO_CONTENT);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public NotificationManipulateResultObject deleteNotifications(List<Long> notiIds) {
        NotificationFilterDataVO filterDataVO = NotificationFilterDataVO.builder()
                .ids(notiIds)
                .build();

        long totalCount = notificationRepository.countTotalByFilter(filterDataVO);

        if (totalCount == 0) {
            return new NotificationManipulateResultObject(
                    NotificationManipulateResultObject.StatusCode.NOTIFICATION_NOT_FOUND);
        }

        String userEmail = SecurityUtils.getCurrentUserEmail();
        Optional<StudioUserDto> userDtoOptional;
        try {
            userDtoOptional = studioUserService.findByEmail(userEmail);
        } catch (StudioException e) {
            log.error(STUDIO_API_ERROR_MSG, e);
            return new NotificationManipulateResultObject(NotificationManipulateResultObject.StatusCode.ERROR);
        }
        if (userDtoOptional.isEmpty()) {
            log.debug(STUDIO_USER_NOT_FOUND_MSG, userEmail);
            return new NotificationManipulateResultObject(NotificationManipulateResultObject.StatusCode.USER_NOT_FOUND);
        }

        Long userId = Long.valueOf(userDtoOptional.get().getId());
        filterDataVO.setUserId(userId);

        totalCount = notificationRepository.countTotalByFilter(filterDataVO);

        // Return forbidden when user try to delete only invalid notifications.
        if (totalCount == 0) {
            return new NotificationManipulateResultObject(
                    NotificationManipulateResultObject.StatusCode.USER_DOES_NOT_HAVE_PERMISSION);
        }

        for (Long notiId : notiIds) {
            notificationRepository.deleteByIdAndUserId(notiId, userId);
        }

        return new NotificationManipulateResultObject(NotificationManipulateResultObject.StatusCode.NO_CONTENT);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public NotificationDetailsResultObject getNotificationDetails(Long notificationId) {
        String userEmail = SecurityUtils.getCurrentUserEmail();
        Optional<StudioUserDto> userDtoOptional;
        try {
            userDtoOptional = studioUserService.findByEmail(userEmail);
        } catch (StudioException e) {
            log.error(STUDIO_API_ERROR_MSG, e);
            return new NotificationDetailsResultObject(null, NotificationDetailsResultObject.StatusCode.ERROR);
        }
        if (userDtoOptional.isEmpty()) {
            log.debug(STUDIO_USER_NOT_FOUND_MSG, userEmail);
            return new NotificationDetailsResultObject(null, NotificationDetailsResultObject.StatusCode.USER_NOT_FOUND);
        }

        Long userId = Long.valueOf(userDtoOptional.get().getId());
        Notification notification = notificationRepository.findByIdAndUserId(notificationId, userId);

        if (notification != null) {
            //validate intersection status to restrict user access from URL:
            String intId = notification.getIntersectionId();

            //intId of notification might be null. if null no need to check intersection status
            if (intId != null) {
                Optional<Intersection> intersectionOptional = intersectionRepository.findById(intId);
                if (intersectionOptional.isEmpty() || intersectionOptional.get().getStatus()
                        .equals(IntersectionStatus.UNAVAILABLE.getInsight())) {
                    log.error("Intersection {} is unavailable", intId);
                    return new NotificationDetailsResultObject(null,
                            NotificationDetailsResultObject.StatusCode.NO_DATA);
                }
            }

            var notificationVO = NotificationVOBuilder.buildNotificationVO(notification, messageService);

            return new NotificationDetailsResultObject(notificationVO,
                    NotificationDetailsResultObject.StatusCode.SUCCESS);
        } else {
            return new NotificationDetailsResultObject(null,
                    NotificationDetailsResultObject.StatusCode.NOTIFICATION_NOT_FOUND);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public NotificationTypeListResultObject getAllNotificationTypes() {
        List<NotificationType> notificationTypes = notificationTypeRepository.findAll();

        List<NotificationTypeVO> notificationTypeVOs = notificationTypes.stream()
                .map(type -> NotificationTypeVO.builder()
                        .id(type.getId())
                        .name(type.getTranslatedName())
                        .build())
                .toList();

        NotificationTypeListResultObject.ResponseData responseData = NotificationTypeListResultObject.ResponseData
                .builder()
                .notificationTypes(notificationTypeVOs)
                .totalCount((long) notificationTypeVOs.size())
                .build();

        return new NotificationTypeListResultObject(responseData,
                NotificationTypeListResultObject.StatusCode.SUCCESS);
    }

    @Override
    public void createNotificationForIntersectionStatusChanged
            (List<IntersectionStatusHistoryV0> intersectionStatusHistoryV0s, Integer agencyId) {
        try {
            log.debug("Start create notification for intersection status changes");
            var userIds = studioUserService.findAllByAgencyId(agencyId).stream()
                    .map(StudioUserDto::getId)
                    .toList();
            // Save notifications to database
            List<Notification> notifications = NotificationBuilder.buildNotifications(intersectionStatusHistoryV0s,
                    userIds, agencyId);

            log.debug("Initiate {} notifications", notifications.size());

            var notificationsSaved = notificationRepository.saveAll(notifications);

            var createNotificationMessageRequest = createNotificationMessage(intersectionStatusHistoryV0s);
            for (Notification notification : notificationsSaved) {
                var notificationMessage = NotificationMessage.builder()
                        .id(new NotificationMessageKey(notification.getId(),
                                createNotificationMessageRequest.getLanguage()))
                        .description(createNotificationMessageRequest.getDescription())
                        .name(createNotificationMessageRequest.getName())
                        .build();
                notification.setNotificationMessage(List.of(notificationMessage));
            }

            // Send to client(s) via sse
            realtimeNotificationSender.sendToUsersAsync(notifications);

            log.debug("End create notification for intersection status changes, notifications saved: {}",
                    notificationsSaved.size());

        } catch (Exception e) {
            log.error("Error when notifying status changed for agency {}", agencyId, e);
        }

    }

    private NotificationMessageRequestVO createNotificationMessage(List<IntersectionStatusHistoryV0> intersectionStatusHistories) {
        MessageService messageService = BeanFinder.getDefaultMessageService();

        var userLanguage = SecurityUtils.getCurrentUserLanguage();

        var locale = LocaleUtils.toLocale(userLanguage);

        var name = messageService.getMessage("notification.intersection_status_changed_name", null, locale);

        var contentBuilder = new StringBuilder();
        contentBuilder.append(
                messageService.getMessage("notification.intersection_status_changed_content_header", null, locale));

        for (var intersectionStatusHistory : intersectionStatusHistories) {
            var status = IntersectionStatus.AVAILABLE.getInsight()
                    .equals(intersectionStatusHistory.getStatus()) ? "Enabled" : "Disabled";
            var time = "disabled_at{" + intersectionStatusHistory.getFromTime().toInstant().toString() + "}";
            Object[] descriptionParams = {
                    intersectionStatusHistory.getIntersectionName(),
                    status,
                    time
            };
            contentBuilder.append(
                    messageService.getMessage("notification.intersection_status_changed_content_description",
                            descriptionParams, locale));
        }

        log.debug("notify content: {}", contentBuilder.toString());

        return NotificationMessageRequestVO.builder()
                .name(name)
                .description(contentBuilder.toString())
                .language(userLanguage)
                .build();
    }

    private Timestamp convertToUtc(LocalDateTime alarmTime, AlarmNotificationContentVO notificationContentVO, Integer agencyId) {
        if (notificationContentVO == null || agencyId == null) {
            log.warn("Missing agency ID for timezone conversion, using UTC");
            return DateTimeUtils.sqlTimestampFromLocalDateTime(alarmTime, ZoneOffset.UTC);
        }

        try {
            // First get agency schema to get timezoneId
            AgencySchemaResponseVO agencySchema = dataIntegrationService.getAgencySchema(agencyId);
            if (agencySchema == null || agencySchema.getTimezoneId() == null) {
                log.warn("No timezone found in agency schema for agency {}, using UTC", agencyId);
                return DateTimeUtils.sqlTimestampFromLocalDateTime(alarmTime, ZoneOffset.UTC);
            }

            ZoneId zoneId = ZoneId.of(agencySchema.getTimezoneId());
            return DateTimeUtils.sqlTimestampFromLocalDateTime(alarmTime, zoneId.getRules().getOffset(Instant.now()));
        } catch (Exception e) {
            log.error("Error getting timezone information for agency {}: {}",agencyId , e.getMessage());
            return DateTimeUtils.sqlTimestampFromLocalDateTime(alarmTime, ZoneOffset.UTC);
        }
    }

    public List<Notification> buildNotifications(NotificationsCreateRequestVO requestVO) {
        String action;
        try {
            action = objectMapper.writeValueAsString(requestVO.getAction());
        } catch (Exception e) {
            log.error("Cannot generate action for notifications!", e);
            action = null;
        }

        Long alarmCategoryId = null;
        String alarmCategoryName = null;
        Timestamp alarmTimeUtc = null;
        String intersectionId = null;
        String intersectionName = null;

        boolean isAlarmNotification = requestVO.getTypeId().equals(NotificationType.ALARM_NOTIFICATION.getId());

        if (isAlarmNotification) {
            try {
                AlarmNotificationContentVO notificationContentVO = objectMapper.readValue(requestVO.getContent(),
                        AlarmNotificationContentVO.class);

                alarmCategoryId = notificationContentVO.getAlarmCategoryId();
                alarmCategoryName = notificationContentVO.getAlarmCategoryName();

                LocalDateTime alarmTime = notificationContentVO.getAlarmTime();
                alarmTimeUtc = convertToUtc(alarmTime, notificationContentVO, requestVO.getAgencyId());

                intersectionId = notificationContentVO.getIntersectionId();
                intersectionName = notificationContentVO.getIntersectionName();
            } catch (Exception e) {
                log.error("Cannot deserialize content of alarm notification!", e);
            }
        }

        List<Notification> notifications = new ArrayList<>();

        for (Integer userId : requestVO.getUserIds()) {
            Notification notification = buildNotification(action, Long.valueOf(userId), requestVO);

            if (isAlarmNotification) {
                notification.setAlarmCategoryId(alarmCategoryId);
                notification.setAlarmCategoryName(alarmCategoryName);
                notification.setAlarmTimeUtc(alarmTimeUtc);
                notification.setIntersectionId(intersectionId);
                notification.setIntersectionName(intersectionName);
            }

            notifications.add(notification);
        }

        return notifications;
    }

    public static Notification buildNotification(String action, Long userId, NotificationsCreateRequestVO requestVO) {
        return Notification.builder()
                .action(action)
                .agencyId(requestVO.getAgencyId())
                .content(requestVO.getContent())
                //                           .description(requestVO.getDescription())
                .flagStatus(NotificationConstants.FlagStatus.UNFLAG.getValue())
                //                           .name(requestVO.getName())
                .readStatus(NotificationConstants.ReadStatus.UNREAD.getValue())
                .sendStatus(NotificationConstants.SendStatus.UNSENT.getValue())
                .typeId(requestVO.getTypeId())
                .userId(userId)
                .build();
    }

}
