package com.siemens.spm.usermanagementservice.notificationdata.strategy;

import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.siemens.spm.common.constant.NotificationConstants;
import com.siemens.spm.spmstudiosdk.constant.AgencyConstant;
import com.siemens.spm.spmstudiosdk.dto.StudioAgencyRoleDto;
import com.siemens.spm.spmstudiosdk.dto.StudioUserAssignmentDto;
import com.siemens.spm.spmstudiosdk.exception.StudioException;
import com.siemens.spm.spmstudiosdk.service.StudioUserService;
import com.siemens.spm.usermanagementservice.config.MessagingConfig;
import com.siemens.spm.usermanagementservice.domain.Notification;
import com.siemens.spm.usermanagementservice.sse.SseMessage;
import com.siemens.spm.usermanagementservice.sse.SseMessagePublisher;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@Transactional
public class RealtimeNotificationSenderImpl implements RealtimeNotificationSender {

    @Autowired
    private SseMessagePublisher sseMessagePublisher;

    @Autowired
    private StudioUserService studioUserService;

    @Override
    public void sendToUsersAsync(List<Notification> notifications) {
        if (notifications == null || notifications.isEmpty()) {
            return;
        }
        log.debug("Start sending notifications to users, notifications: {}", notifications);

        for (var notification : notifications) {
            Long userId = notification.getUserId();
            Integer agencyId = notification.getAgencyId();

            Optional<StudioUserAssignmentDto> studioUserAssignment = Optional.empty();
            try {
                studioUserAssignment = studioUserService.findUserAssignmentById(userId);
            } catch (StudioException ex) {
                log.error("Studio Error", ex);
            }

            if (studioUserAssignment.isEmpty()) {
                log.warn("Cannot find user with id = {} in Studio", userId);
                continue;
            }

            List<Integer> agencyIdList = studioUserAssignment.get()
                    .getAgencyRoles()
                    // FIXME: This could potentially cause NPE
                    .stream()
                    .map(StudioAgencyRoleDto::getAgencyId)
                    .toList();

            if (agencyIdList.contains(agencyId)) {
                SseMessage sseMessage = SseMessage.builder()
                        .userId(userId)
                        .agencyId(agencyId)
                        .message(notification)
                        .build();
                sseMessagePublisher.publish(sseMessage, MessagingConfig.NOTIFICATION_CHANNEL);
            }

            // Send notification to the System Agency chanel to make sure that admin can receive it
            if (agencyIdList.contains(AgencyConstant.SYSTEM_AGENCY_ID)) {
                SseMessage sseMessage = SseMessage.builder()
                        .userId(userId)
                        .agencyId(AgencyConstant.SYSTEM_AGENCY_ID)
                        .message(notification)
                        .build();
                sseMessagePublisher.publish(sseMessage, MessagingConfig.NOTIFICATION_CHANNEL);
            }

            // TODO: Check SENT status here
            notification.setSendStatus(NotificationConstants.SendStatus.SENT.getValue());
        }
    }
}
