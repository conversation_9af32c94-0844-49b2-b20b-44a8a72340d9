/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : NotificationFacadeBean.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.notificationdata.boundary;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.siemens.spm.usermanagementservice.api.boundary.NotificationService;
import com.siemens.spm.usermanagementservice.api.vo.ReleaseNoteVO;
import com.siemens.spm.usermanagementservice.api.vo.request.NotificationSearchRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.NotificationUpdatedRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.NotificationsCreateRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.NotificationDetailsResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.NotificationLatestUnreadResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.NotificationManipulateResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.NotificationSearchResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.NotificationTypeListResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.NotificationsCreateResultObject;
import com.siemens.spm.usermanagementservice.notificationdata.strategy.NotificationStrategy;

@Service
public class NotificationFacadeBean implements NotificationService {

    @Autowired
    private NotificationStrategy notificationStrategy;

    /**
     * {@inheritDoc}
     */
    @Override
    public NotificationsCreateResultObject createNotificationsForUsersInternal(NotificationsCreateRequestVO requestVO) {
        notificationStrategy.createNotificationsForUsersInternalAsync(requestVO);
        return new NotificationsCreateResultObject(NotificationsCreateResultObject.StatusCode.SUCCESS);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public NotificationsCreateResultObject createReleaseNoteNotifications(ReleaseNoteVO releaseNoteVO) {
        notificationStrategy.createReleaseNoteNotifications(releaseNoteVO);
        return new NotificationsCreateResultObject(NotificationsCreateResultObject.StatusCode.SUCCESS);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public NotificationLatestUnreadResultObject getLatestUnreadNotifications(Integer agencyId,
                                                                             Integer page,
                                                                             Integer size) {
        return notificationStrategy.getLatestUnreadNotifications(agencyId, page, size);
    }

    /**
     * {@inheritDoc}
     *
     * @throws JsonProcessingException
     * @throws JsonMappingException
     */
    @Override
    public NotificationSearchResultObject searchNotifications(NotificationSearchRequestVO searchRequest) {
        return notificationStrategy.searchNotifications(searchRequest);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public NotificationManipulateResultObject updateNotifications(NotificationUpdatedRequestVO updateRequest) {
        return notificationStrategy.updateNotifications(updateRequest);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public NotificationManipulateResultObject markAllNotificationsAsRead(Integer agencyId) {
        return notificationStrategy.markAllNotificationsAsRead(agencyId);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public NotificationManipulateResultObject deleteNotifications(List<Long> notiIds) {
        return notificationStrategy.deleteNotifications(notiIds);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public NotificationDetailsResultObject getNotificationDetails(Long notificationId) {
        return notificationStrategy.getNotificationDetails(notificationId);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public NotificationTypeListResultObject getAllNotificationTypes() {
        return notificationStrategy.getAllNotificationTypes();
    }

}
