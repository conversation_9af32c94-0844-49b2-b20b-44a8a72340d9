/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : NotificationStrategy.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.notificationdata.strategy;

import java.util.List;

import com.siemens.spm.usermanagementservice.intersectiondata.vo.IntersectionStatusHistoryV0;
import org.springframework.scheduling.annotation.Async;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.siemens.spm.usermanagementservice.api.vo.ReleaseNoteVO;
import com.siemens.spm.usermanagementservice.api.vo.request.NotificationSearchRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.NotificationUpdatedRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.NotificationsCreateRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.NotificationDetailsResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.NotificationLatestUnreadResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.NotificationManipulateResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.NotificationSearchResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.NotificationTypeListResultObject;

public interface NotificationStrategy {

    /**
     * Create notifications for users
     *
     * @param createRequest
     * @return void
     */
    @Async
    void createNotificationsForUsersInternalAsync(NotificationsCreateRequestVO createRequest);

    /**
     * Create release note notifications to all users
     */
    @Async
    void createReleaseNoteNotifications(ReleaseNoteVO releaseNoteVO);

    /**
     * Get latest unread notifications by page and size
     *
     * @param page
     * @param size
     * @return NotificationLatestUnreadResultObject
     */
    NotificationLatestUnreadResultObject getLatestUnreadNotifications(Integer agencyId, Integer page, Integer size);

    /**
     * search/filter notifications
     *
     * @param searchRequest
     * @return NotificationSearchResultObject
     * @throws JsonProcessingException
     * @throws JsonMappingException
     */
    NotificationSearchResultObject searchNotifications(NotificationSearchRequestVO searchRequest);

    /**
     * Update notifications
     *
     * @param updateRequest
     * @return NotificationManipulateResultObject
     */
    NotificationManipulateResultObject updateNotifications(NotificationUpdatedRequestVO updateRequest);

    /**
     * Mark all notifications as read (for current user)
     *
     * @return NotificationManipulateResultObject
     */
    NotificationManipulateResultObject markAllNotificationsAsRead(Integer agencyId);

    /**
     * Delete notifications (for current user)
     *
     * @param notiIds
     * @return NotificationManipulateResultObject
     */
    NotificationManipulateResultObject deleteNotifications(List<Long> notiIds);

    /**
     * Get Notification in detail
     *
     * @param notificationId
     * @return NotificationDetailsResultObject
     */
    NotificationDetailsResultObject getNotificationDetails(Long notificationId);

    /**
     * Get all notification types
     *
     * @return NotificationTypeListResultObject
     */
    NotificationTypeListResultObject getAllNotificationTypes();

    @Async
    void createNotificationForIntersectionStatusChanged(List<IntersectionStatusHistoryV0> intersectionStatusHistoryV0s,
                                                        Integer agencyId);

}
