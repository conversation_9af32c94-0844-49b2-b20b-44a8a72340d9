package com.siemens.spm.usermanagementservice.notificationdata.util;

import java.util.ArrayList;
import java.util.List;

import com.siemens.spm.common.shared.vo.NotificationMessageRequestVO;
import com.siemens.spm.usermanagementservice.domain.NotificationMessage;
import com.siemens.spm.usermanagementservice.domain.NotificationMessageKey;

public class NotificationMessageBuilder {

    private NotificationMessageBuilder() {
    }

    public static List<NotificationMessage> build(Long notificationId, List<NotificationMessageRequestVO> requestVOs) {
        List<NotificationMessage> ret = new ArrayList<>();
        for (NotificationMessageRequestVO message : requestVOs) {
            NotificationMessage notificationMessage = NotificationMessage.builder()
                    .id(new NotificationMessageKey(notificationId, message.getLanguage()))
                    .description(message.getDescription())
                    .name(message.getName())
                    .build();

            ret.add(notificationMessage);
        }

        return ret;
    }
}
