/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : CleanUpNotificationStrategyImpl.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.notificationdata.strategy;

import com.siemens.spm.common.agency.utils.AgencyPersistenceConstants;
import com.siemens.spm.usermanagementservice.repository.NotificationMessageRepository;
import com.siemens.spm.usermanagementservice.repository.NotificationRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
public class CleanUpNotificationStrategyImpl implements CleanUpNotificationStrategy {

    private final NotificationMessageRepository notificationMessageRepository;

    private final NotificationRepository notificationRepository;

    @Override
    @Transactional(transactionManager = AgencyPersistenceConstants.AGENCY_TRANSACTION_MANAGER)
    public void cleanUpNotificationAndMessage(LocalDateTime cleanUpTime, List<String> intersectionIds) {

        notificationMessageRepository.deleteMessages(cleanUpTime, intersectionIds);
        notificationRepository.deleteNotifications(cleanUpTime, intersectionIds);
    }
}
