/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : NotificationBuilder.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.notificationdata.util;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.siemens.spm.common.constant.NotificationConstants;
import com.siemens.spm.common.shared.domaintype.notification.NotificationType;
import com.siemens.spm.common.util.BeanFinder;
import com.siemens.spm.common.util.DateTimeUtils;
import com.siemens.spm.usermanagementservice.api.vo.AlarmNotificationContentVO;
import com.siemens.spm.usermanagementservice.api.vo.request.NotificationsCreateRequestVO;
import com.siemens.spm.usermanagementservice.domain.Notification;
import com.siemens.spm.usermanagementservice.intersectiondata.vo.IntersectionStatusHistoryV0;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public final class NotificationBuilder {

    private NotificationBuilder() {
    }

    /**
     * Object mapper is thread-safe after initialization
     */
    private static final ObjectMapper objectMapper = BeanFinder.getDefaultObjectMapper();

    /**
     * Create a notification from action (string), user id and request value object
     * {@link NotificationsCreateRequestVO}
     *
     * @param requestVO
     * @return Notification
     */
    public static Notification buildNotification(String action, Long userId, NotificationsCreateRequestVO requestVO) {
        return Notification.builder()
                .action(action)
                .agencyId(requestVO.getAgencyId())
                .content(requestVO.getContent())
                //                           .description(requestVO.getDescription())
                .flagStatus(NotificationConstants.FlagStatus.UNFLAG.getValue())
                //                           .name(requestVO.getName())
                .readStatus(NotificationConstants.ReadStatus.UNREAD.getValue())
                .sendStatus(NotificationConstants.SendStatus.UNSENT.getValue())
                .typeId(requestVO.getTypeId())
                .userId(userId)
                .build();
    }

    /**
     * Create a list of notifications from request value object
     * {@link NotificationsCreateRequestVO}
     *
     * @param requestVO
     * @return List<Notification>
     */
    public static List<Notification> buildNotifications(NotificationsCreateRequestVO requestVO) {
        String action;
        try {
            action = objectMapper.writeValueAsString(requestVO.getAction());
        } catch (Exception e) {
            log.error("Cannot generate action for notifications!", e);
            action = null;
        }

        Long alarmCategoryId = null;
        String alarmCategoryName = null;
        Timestamp alarmTimeUtc = null;
        String intersectionId = null;
        String intersectionName = null;

        boolean isAlarmNotification = requestVO.getTypeId().equals(NotificationType.ALARM_NOTIFICATION.getId());

        if (isAlarmNotification) {
            try {
                AlarmNotificationContentVO notificationContentVO = objectMapper.readValue(requestVO.getContent(),
                        AlarmNotificationContentVO.class);

                alarmCategoryId = notificationContentVO.getAlarmCategoryId();
                alarmCategoryName = notificationContentVO.getAlarmCategoryName();

                LocalDateTime alarmTime = notificationContentVO.getAlarmTime();
                ZoneOffset zoneOffset = notificationContentVO.getZoneOffset();
                alarmTimeUtc = DateTimeUtils.sqlTimestampFromLocalDateTime(alarmTime, zoneOffset);

                intersectionId = notificationContentVO.getIntersectionId();
                intersectionName = notificationContentVO.getIntersectionName();
            } catch (Exception e) {
                log.error("Cannot deserialize content of alarm notification!", e);
            }
        }

        List<Notification> notifications = new ArrayList<>();

        for (Integer userId : requestVO.getUserIds()) {
            Notification notification = buildNotification(action, Long.valueOf(userId), requestVO);

            if (isAlarmNotification) {
                notification.setAlarmCategoryId(alarmCategoryId);
                notification.setAlarmCategoryName(alarmCategoryName);
                notification.setAlarmTimeUtc(alarmTimeUtc);
                notification.setIntersectionId(intersectionId);
                notification.setIntersectionName(intersectionName);
            }

            notifications.add(notification);
        }

        return notifications;
    }

    public static List<Notification> buildNotifications(List<IntersectionStatusHistoryV0> intersectionStatusHistories,
                                                        List<Integer> userIds,
                                                        Integer agencyId) {
        var notifications = new ArrayList<Notification>();
        for (Integer userId : userIds) {
            var notification = Notification.builder()
                    .agencyId(agencyId)
                    .flagStatus(NotificationConstants.FlagStatus.UNFLAG.getValue())
                    .readStatus(NotificationConstants.ReadStatus.UNREAD.getValue())
                    .sendStatus(NotificationConstants.SendStatus.UNSENT.getValue())
                    .typeId(NotificationType.OTHER.getId())
                    .userId(Long.valueOf(userId))
                    .build();

            notifications.add(notification);
        }
        return notifications;
    }
}
