/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionFacadeBean.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.intersectiondata.boundary;

import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionStatusHistoriesResultObject;
import com.siemens.spm.usermanagementservice.repository.IntersectionRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.siemens.spm.usermanagementservice.api.boundary.IntersectionService;
import com.siemens.spm.usermanagementservice.api.vo.request.IntersectionSearchRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionDetailResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionInternalSearchResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionSearchResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionSimpleListResultObject;
import com.siemens.spm.usermanagementservice.intersectiondata.strategy.IntersectionSearchStrategy;

import java.time.LocalDateTime;

@Service
@Transactional
@RequiredArgsConstructor
public class IntersectionFacadeBean implements IntersectionService {

    private final IntersectionSearchStrategy searchStrategy;

    private final IntersectionRepository intersectionRepository;

    /**
     * {@inheritDoc}
     */
    @Override
    public IntersectionSearchResultObject searchIntersections(IntersectionSearchRequestVO searchRequest) {
        return searchStrategy.searchIntersections(searchRequest);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public IntersectionSimpleListResultObject getAllSimpleIntersections(Integer agencyId, String status) {
        return searchStrategy.getAllSimpleIntersections(agencyId, status);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public IntersectionDetailResultObject getIntersectionDetail(Integer agencyId, String intersectionId) {
        return searchStrategy.getIntersectionDetail(agencyId, intersectionId);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public IntersectionInternalSearchResultObject searchIntersectionsInternal(IntersectionSearchRequestVO searchRequest) {
        return searchStrategy.searchIntersectionsInternal(searchRequest);
    }

    @Override
    public IntersectionStatusHistoriesResultObject getIntersectionStatusHistories(Integer agencyId,
                                                                                  String intersectionId,
                                                                                  Integer page,
                                                                                  Integer size) {
        return searchStrategy.getIntersectionStatusHistories(agencyId, intersectionId, page, size);
    }

    @Override
    public void updatePerflogUploadedFromInfoEvent(String intersectionId, LocalDateTime uploadedTime) {
        intersectionRepository.updatePerfLogRecentTime(intersectionId, uploadedTime);
    }

}
