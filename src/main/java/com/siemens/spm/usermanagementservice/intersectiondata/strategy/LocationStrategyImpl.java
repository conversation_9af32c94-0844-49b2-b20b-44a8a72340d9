/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : LocationFacadeImpl.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.intersectiondata.strategy;

import com.siemens.spm.usermanagementservice.intersectiondata.vo.IntersectionMetadataVO;
import com.siemens.spm.usermanagementservice.intersectiondata.vo.LocationInfoDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.TimeToLive;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class LocationStrategyImpl implements LocationStrategy {

    private static final String GET_TIMEZONE_ENDPOINT = "https://api.wheretheiss.at/v1/coordinates/";
    public static final String TIMEZONE_CACHE = "timezone_cache";

    private final RestTemplate restTemplate;

    @Override
    @Cacheable(key = "#location.latitude + '_' + #location.longitude", cacheNames = TIMEZONE_CACHE)
    @TimeToLive(unit = TimeUnit.DAYS)
    public String getTimeZoneFromLocation(IntersectionMetadataVO.Location location) {
        log.warn("Cache miss. Getting timezone from API for latitude and longitude : {}, {}", location.getLatitude(),
                location.getLongitude());

        Double latitude = location.getLatitude();
        Double longitude = location.getLongitude();
        String getCoordinateEndpoint = String.format(GET_TIMEZONE_ENDPOINT + "%f,%f", latitude,
                longitude);

        try {
            ResponseEntity<LocationInfoDto> response = restTemplate.getForEntity(getCoordinateEndpoint,
                    LocationInfoDto.class);

            if (response.getStatusCode().is2xxSuccessful() && response.hasBody()) {
                String timezone = Optional.ofNullable(response.getBody()).map(LocationInfoDto::getTimezoneID)
                        .orElse(null);
                log.info("Timezone found for latitude {} and longitude {} is {}", latitude, longitude, timezone);
                return timezone;
            }

        } catch (RestClientException exception) {
            log.error("Error getting coordinate data from endpoint {}. Exception : {}", getCoordinateEndpoint,
                    exception.getMessage());
        }

        return null;
    }
}
