/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionChangeStrategyBean.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.intersectiondata.strategy;

import com.siemens.spm.common.agency.utils.AgencyPersistenceConstants;
import com.siemens.spm.common.shared.domaintype.IntersectionStatus;
import com.siemens.spm.usermanagementservice.domain.Intersection;
import com.siemens.spm.usermanagementservice.exception.UserManagementBusinessException;
import com.siemens.spm.usermanagementservice.intersectiondata.vo.IntersectionMetadataVO;
import com.siemens.spm.usermanagementservice.repository.IntersectionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;
import java.util.TimeZone;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(transactionManager = AgencyPersistenceConstants.AGENCY_TRANSACTION_MANAGER)
public class IntersectionChangeStrategyBean implements IntersectionChangeStrategy {

    private final IntersectionRepository intersectionRepository;

    private final LocationStrategy locationStrategy;

    @Override
    public Intersection createIntersection(IntersectionMetadataVO eventVO) {
        validateIntersectionDoesNotExist(eventVO.getId());
        return createIntersectionFromEvent(eventVO);
    }

    @Override
    public Intersection updateIntersection(IntersectionMetadataVO eventVO) {
        return intersectionRepository.findById(eventVO.getId())
                .map(intersection -> updateExistingIntersection(intersection, eventVO))
                .orElseGet(() -> createIntersectionFromEvent(eventVO));
    }

    @Override
    public void deleteIntersection(String id) {
        intersectionRepository.deleteById(id);
        log.info("Deleted intersection with id: {}", id);
    }

    private void validateIntersectionDoesNotExist(String intersectionId) {
        if (intersectionRepository.existsById(intersectionId)) {
            throw new UserManagementBusinessException("Intersection Id already exists: " + intersectionId);
        }
    }

    private Intersection updateExistingIntersection(Intersection intersection, IntersectionMetadataVO eventVO) {
        mapIntersectionMetadataFromEvent(eventVO, intersection);
        return intersectionRepository.save(intersection);
    }

    private void mapIntersectionMetadataFromEvent(IntersectionMetadataVO eventVO, Intersection intersection) {
        IntersectionStatus status = determineIntersectionStatus(eventVO);
        IntersectionMetadataVO.IntersectionInfo data = eventVO.getData().getData();
        intersection.setName(data.getName());
        intersection.setModel(data.getDeviceSoftware());
        intersection.setVersion(data.getSoftwareVersion());
        intersection.setStatus(status.getInsight());
        updateLocation(intersection, data);
    }

    private void updateLocation(Intersection intersection, IntersectionMetadataVO.IntersectionInfo data) {
        intersection.setLatitude(data.getLocation().getLatitude());
        intersection.setLongitude(data.getLocation().getLongitude());
    }

    private Intersection createIntersectionFromEvent(IntersectionMetadataVO eventVO) {
        return createAndSaveIntersection(eventVO);
    }

    private Intersection createAndSaveIntersection(IntersectionMetadataVO eventVO) {
        IntersectionMetadataVO.IntersectionInfo data = eventVO.getData().getData();
        IntersectionStatus status = determineIntersectionStatus(eventVO);
        String timeZoneFromLocation = locationStrategy.getTimeZoneFromLocation(data.getLocation());

        Intersection intersection = Intersection.builder()
                .id(eventVO.getId())
                .name(data.getName())
                .model(data.getDeviceSoftware())
                .version(data.getSoftwareVersion())
                .status(status.getInsight())
                .latitude(data.getLocation().getLatitude())
                .longitude(data.getLocation().getLongitude())
                .timeZone(TimeZone.getTimeZone(timeZoneFromLocation))
                .build();

        return intersectionRepository.save(intersection);
    }

    private IntersectionStatus determineIntersectionStatus(IntersectionMetadataVO eventVO) {
        boolean spmEnable = Optional.ofNullable(eventVO.getData())
                .map(IntersectionMetadataVO.NestedData::getData)
                .map(IntersectionMetadataVO.IntersectionInfo::getSpmEnable)
                .orElse(true);
        return spmEnable ? IntersectionStatus.AVAILABLE : IntersectionStatus.UNAVAILABLE;
    }
}
