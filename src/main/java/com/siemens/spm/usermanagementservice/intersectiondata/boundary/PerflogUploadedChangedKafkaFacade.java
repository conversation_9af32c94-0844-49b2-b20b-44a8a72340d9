/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionKafkaFacadeBean.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.intersectiondata.boundary;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

import com.siemens.spm.common.agency.master.AgencySchemaReadService;
import org.springframework.stereotype.Service;

import com.siemens.spm.common.agency.supports.AgencyAware;
import com.siemens.spm.usermanagementservice.api.boundary.IntersectionService;
import com.yunex.data.kafka.event.PerflogUploadedChangedDto;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class PerflogUploadedChangedKafkaFacade {

    private final IntersectionService intersectionService;

    private final AgencySchemaReadService agencySchemaReadService;

    @AgencyAware(agencyId = "[0].agencyId")
    public void handlePerflogUploadedChangedKafkaEvent(PerflogUploadedChangedDto eventVO) {
        if (!eventVO.isLatest()) {
            return;
        }

        if (agencySchemaReadService.isExists(eventVO.getAgencyId())) {
            log.info("Handling perflog uploaded changed event for intersection {} and agency {}",
                    eventVO.getIntersectionId(), eventVO.getAgencyId());
            Instant instant = Instant.ofEpochSecond(eventVO.getLastEventTimestamp());
            LocalDateTime perflogChangedTime = LocalDateTime.ofInstant(instant, ZoneId.of("UTC"));
            intersectionService.updatePerflogUploadedFromInfoEvent(eventVO.getIntersectionId(), perflogChangedTime);
        } else {
            log.warn("Agency id {} is not existed. Do nothing", eventVO.getAgencyId());
        }
    }
    
}
