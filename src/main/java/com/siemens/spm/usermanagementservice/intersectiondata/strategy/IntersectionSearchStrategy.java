/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionSearchStrategy.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.intersectiondata.strategy;

import com.siemens.spm.usermanagementservice.api.vo.request.IntersectionSearchRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionDetailResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionInternalSearchResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionSearchResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionSimpleListResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionStatusHistoriesResultObject;

public interface IntersectionSearchStrategy {

    /**
     * Get intersection detail
     *
     * @param agencyId       id of agency
     * @param intersectionId id of intersection
     * @return IntersectionDetailsResultObject
     */
    IntersectionDetailResultObject getIntersectionDetail(Integer agencyId, String intersectionId);

    /**
     * Search intersections
     *
     * @param searchRequest
     * @return IntersectionSearchResultObject
     */
    IntersectionSearchResultObject searchIntersections(IntersectionSearchRequestVO searchRequest);

    /**
     * Get all simple intersections by agencyId
     *
     * @param agencyId
     * @return IntersectionSimpleListResultObject
     */
    IntersectionSimpleListResultObject getAllSimpleIntersections(Integer agencyId, String status);

    /**
     * Search intersections internally for other service(s)
     *
     * @param searchRequest
     * @return IntersectionInternalSearchResultObject
     */
    IntersectionInternalSearchResultObject searchIntersectionsInternal(IntersectionSearchRequestVO searchRequest);

    /**
     * Get intersection status histories
     *
     * @param agencyId       id of agency
     * @param intersectionId id of intersection
     * @return IntersectionDetailsResultObject
     */
    IntersectionStatusHistoriesResultObject getIntersectionStatusHistories(Integer agencyId,
                                                                           String intersectionId,
                                                                           Integer page,
                                                                           Integer size);
}
