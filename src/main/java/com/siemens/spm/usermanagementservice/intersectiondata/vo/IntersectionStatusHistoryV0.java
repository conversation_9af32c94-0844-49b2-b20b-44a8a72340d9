package com.siemens.spm.usermanagementservice.intersectiondata.vo;

import com.siemens.spm.usermanagementservice.domain.IntersectionStatusHistory;
import lombok.Builder;
import lombok.Data;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Objects;

@Data
@Builder
public class IntersectionStatusHistoryV0 {
    private String id;

    private String intersectionId;

    private String status;

    private Timestamp fromTime;

    private Timestamp toTime;

    private String intersectionName;


    public static IntersectionStatusHistoryV0 from(IntersectionStatusHistory intersectionStatusHistory,
                                                   String intersectionName) {
        return IntersectionStatusHistoryV0.builder()
                .intersectionId(intersectionStatusHistory.getIntersectionId())
                .id(intersectionStatusHistory.getId())
                .status(intersectionStatusHistory.getStatus())
                .fromTime(intersectionStatusHistory.getFromTime())
                .toTime(intersectionStatusHistory.getToTime())
                .intersectionName(intersectionName)
                .build();
    }
}
