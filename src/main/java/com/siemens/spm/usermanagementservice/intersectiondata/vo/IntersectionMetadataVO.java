/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionMetadataVO.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.intersectiondata.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.kafka.common.KafkaEventVO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@EqualsAndHashCode(callSuper = true)
public class IntersectionMetadataVO extends KafkaEventVO {

    @NotNull
    @JsonProperty("id")
    private String id;

    @Valid
    @JsonProperty("data")
    private NestedData data;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NestedData {

        @Valid
        @JsonProperty("data")
        private IntersectionInfo data;

        @JsonProperty("metadata")
        private Object metadata;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IntersectionInfo {

        @NotNull
        @JsonProperty("name")
        private String name;

        @NotNull
        @JsonProperty("shortName")
        private String shortName;

        @NotNull
        @JsonProperty("deviceSoftware")
        private String deviceSoftware;

        @NotNull
        @JsonProperty("softwareVersion")
        private String softwareVersion;

        @Valid
        @NotNull
        @JsonProperty("geoLocation")
        private Location location;

        @JsonProperty("spmEnable")
        private Boolean spmEnable = true;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Location {

        @NotNull
        @JsonProperty("latitude")
        private Double latitude;

        @NotNull
        @JsonProperty("longitude")
        private Double longitude;

    }
}
