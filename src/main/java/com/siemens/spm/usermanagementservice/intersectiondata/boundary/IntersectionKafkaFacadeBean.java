/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionKafkaFacadeBean.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.intersectiondata.boundary;

import com.siemens.spm.common.agency.master.AgencySchemaReadService;
import com.siemens.spm.common.agency.supports.AgencyAware;
import com.siemens.spm.common.kafka.exception.KafkaRetryException;
import com.siemens.spm.usermanagementservice.common.ChangeTypeEnum;
import com.siemens.spm.usermanagementservice.exception.InvalidKafkaPayloadException;
import com.siemens.spm.usermanagementservice.intersectiondata.strategy.IntersectionChangeStrategy;
import com.siemens.spm.usermanagementservice.intersectiondata.vo.IntersectionMetadataVO;
import com.siemens.spm.usermanagementservice.repository.CorridorIntersectionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class IntersectionKafkaFacadeBean implements IntersectionKafkaFacade {

    private final IntersectionChangeStrategy intersectionChangeStrategy;

    private final CorridorIntersectionRepository corridorIntersectionRepository;

    private final AgencySchemaReadService agencySchemaReadService;

    @Override
    @AgencyAware(agencyId = "[1].agencyId")
    public void handleIntersectionMetadataKafkaEvent(ChangeTypeEnum changeTypeEnum, IntersectionMetadataVO eventVO) {
        log.debug("Handle agency kafka event in thread: {}", Thread.currentThread().getName());
        switch (changeTypeEnum) {
        case CREATE -> handleCreateEvent(eventVO);
        case DELETE -> handleDeleteEvent(eventVO);
        case UPDATE -> handleUpdateEvent(eventVO);
        default -> throw new InvalidKafkaPayloadException("Change type not valid: {}" + changeTypeEnum.name(),
                eventVO.toString());
        }
    }

    private void handleCreateEvent(IntersectionMetadataVO eventVO) {
        if (!isAgencyExists(eventVO)) {
            throw new KafkaRetryException("Agency id " + eventVO.getAgencyId() + " is not existed");
        }
        log.info("Handling create intersection event with type {}, intersection id {} and agency id {} ",
                ChangeTypeEnum.CREATE, eventVO.getId(), eventVO.getAgencyId());
        intersectionChangeStrategy.createIntersection(eventVO);
    }

    private void handleUpdateEvent(IntersectionMetadataVO eventVO) {
        if (!isAgencyExists(eventVO)) {
            throw new KafkaRetryException("Agency id " + eventVO.getAgencyId() + " is not existed");
        }
        log.info("Handling update intersection event with type {}, intersection id {} and agency id {} ",
                ChangeTypeEnum.UPDATE, eventVO.getId(), eventVO.getAgencyId());
        intersectionChangeStrategy.updateIntersection(eventVO);
    }

    private void handleDeleteEvent(IntersectionMetadataVO eventVO) {
        if (isAgencyExists(eventVO)) {
            String intId = eventVO.getId();
            log.info("Handling delete intersection event with type {}, intersection id {} ", ChangeTypeEnum.DELETE, intId);
            corridorIntersectionRepository.deleteAllByIntersectionId(intId);
            intersectionChangeStrategy.deleteIntersection(intId);
        } else {
            log.warn("Agency id {} is not existed. Do nothing", eventVO.getAgencyId());
        }
    }

    private boolean isAgencyExists(IntersectionMetadataVO eventVO) {
        return agencySchemaReadService.isExists(eventVO.getAgencyId());
    }

}
