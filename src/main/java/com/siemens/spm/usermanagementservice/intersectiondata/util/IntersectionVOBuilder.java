/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionVOBuilder.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.intersectiondata.util;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

import com.siemens.spm.usermanagementservice.api.vo.IntersectionDetailVO;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionInternalVO;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionSearchVO;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionSimpleVO;
import com.siemens.spm.usermanagementservice.domain.Intersection;

public final class IntersectionVOBuilder {

    private IntersectionVOBuilder() {
    }

    /**
     * This method is used when Intersection provide perfLogRecentTime on its own
     *
     * @param intersection
     * @return
     */
    public static IntersectionSearchVO buildIntersectionSearchVO(Intersection intersection) {
        if (intersection == null) {
            return new IntersectionSearchVO();
        }
        
        return IntersectionSearchVO.builder()
                .address(intersection.getAddress())
                .id(intersection.getId())
                .timezone(intersection.getTimeZone())
                .perflogRecentTime(getPerfLogUploadedTime(intersection))
                .lastModifiedAt(intersection.getLastModifiedAt())
                .latitude(intersection.getLatitude())
                .longitude(intersection.getLongitude())
                .model(intersection.getModel())
                .name(intersection.getName())
                .number(intersection.getNumber())
                .status(intersection.getStatus())
                .version(intersection.getVersion())
                .build();
    }

    public static IntersectionSimpleVO buildIntersectionSimpleVO(Intersection intersection) {
        if (intersection == null) {
            return new IntersectionSimpleVO();
        }

        return IntersectionSimpleVO.builder()
                .id(intersection.getId())
                .latitude(intersection.getLatitude())
                .longitude(intersection.getLongitude())
                .name(intersection.getName())
                .number(intersection.getNumber())
                .timeZone(intersection.getTimeZone())
                .build();
    }

    public static LocalDateTime getPerfLogUploadedTime(Intersection intersection) {
        LocalDateTime perfLogUploadedTime = intersection.getPerfLogRecentTime();
        if (perfLogUploadedTime != null && intersection.getTimeZone() != null) {
            Instant utc = perfLogUploadedTime.atZone(ZoneId.systemDefault()).toInstant();
            perfLogUploadedTime = LocalDateTime.ofInstant(utc, intersection.getTimeZone().toZoneId());
        }
        
        return perfLogUploadedTime;
    }
    
    /**
     * This method is used when Intersection provide perfLogRecentTime on its own
     *
     * @param intersection
     * @return IntersectionDetailVO
     */
    public static IntersectionDetailVO buildIntersectionDetailVO(Intersection intersection) {
        if (intersection == null) {
            return new IntersectionDetailVO();
        }

        return IntersectionDetailVO.builder()
                .id(intersection.getId())
                .number(intersection.getNumber())
                .name(intersection.getName())
                .address(intersection.getAddress())
                .model(intersection.getModel())
                .version(intersection.getVersion())
                .status(intersection.getStatus())
                .perflogRecentTime(getPerfLogUploadedTime(intersection))
                .lastModifiedAt(intersection.getLastModifiedAt())
                .build();
    }

    public static IntersectionInternalVO buildIntersectionInternalVO(Intersection intersection) {
        if (intersection == null) {
            return new IntersectionInternalVO();
        }

        return IntersectionInternalVO.builder()
                .id(intersection.getId())
                .name(intersection.getName())
                .number(intersection.getNumber())
                .status(intersection.getStatus())
                .build();
    }
}
