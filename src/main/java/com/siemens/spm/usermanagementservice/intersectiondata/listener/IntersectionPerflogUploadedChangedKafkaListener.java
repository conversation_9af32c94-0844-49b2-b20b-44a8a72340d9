package com.siemens.spm.usermanagementservice.intersectiondata.listener;

import java.util.Map;
import java.util.Optional;

import org.apache.kafka.clients.consumer.ConsumerGroupMetadata;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.serializer.ErrorHandlingDeserializer;
import org.springframework.kafka.support.serializer.JsonDeserializer;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.stereotype.Component;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;

import com.siemens.spm.common.kafka.common.KafkaDeserializerFactory;
import com.siemens.spm.common.kafka.common.KafkaEventListener;
import com.siemens.spm.common.kafka.config.KafkaConsumerConfig;
import com.siemens.spm.usermanagementservice.intersectiondata.boundary.PerflogUploadedChangedKafkaFacade;
import com.yunex.data.kafka.event.PerflogUploadedChangedDto;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@ConditionalOnProperty(value = "spring.kafka.enable", havingValue = "true")
public class IntersectionPerflogUploadedChangedKafkaListener
        extends KafkaEventListener<String, byte[], PerflogUploadedChangedDto> {

    @Value("${spring.kafka.topic.perflog-uploaded-changed.name:datahub-data-perflog-uploaded-changed}")
    private String topicName;

    private final ErrorHandlingDeserializer<PerflogUploadedChangedDto> deserializer;

    private final PerflogUploadedChangedKafkaFacade perflogUploadedChangedKafkaFacade;

    public IntersectionPerflogUploadedChangedKafkaListener(LocalValidatorFactoryBean validator,
                                                           PerflogUploadedChangedKafkaFacade perflogUploadedChangedKafkaFacade) {
        this.deserializer = KafkaDeserializerFactory.constructErrorHandlingDeserializer(
                new JsonDeserializer<>(PerflogUploadedChangedDto.class), false, validator);
        this.perflogUploadedChangedKafkaFacade = perflogUploadedChangedKafkaFacade;
    }

    @Override
    @KafkaListener(
            topics = "${spring.kafka.topic.perflog-uploaded-changed.name:datahub-data-perflog-uploaded-changed}",
            containerFactory = KafkaConsumerConfig.BYTE_CONSUMER_LISTENER_FACTORY_BEAN_NAME,
            concurrency = "${spring.kafka.topic.perflog-uploaded-changed.concurrency:3}"
    )
    public void listen(@Headers Map<String, Object> headers, ConsumerRecord<String, byte[]> event,
                       KafkaConsumer<String, byte[]> consumer) {
        Optional<ConsumerGroupMetadata> consumerGroupMetadataOpt = Optional.ofNullable(consumer.groupMetadata());
        log.info("Processing event with offset {} and partition {} by consumerId {} in groupId {} ", event.offset(),
                event.partition(), consumerGroupMetadataOpt.map(ConsumerGroupMetadata::memberId).orElse(null),
                consumerGroupMetadataOpt.map(ConsumerGroupMetadata::memberId).orElse(null));

        PerflogUploadedChangedDto eventVO = convertRawDataToEvent(event.value());
        if (eventVO == null) {
            log.warn("Received null or empty event");
            return;
        }
        doBusinessLogic(eventVO);

        log.info("Done processing event of agency: {} and intersection id: {}", eventVO.getAgencyId(),
                eventVO.getIntersectionId());
    }

    @Override
    protected PerflogUploadedChangedDto convertRawDataToEvent(byte[] rawData) {
        if (rawData == null || rawData.length == 0) {
            log.warn("Received null or empty event");
            return null;
        }
        return deserializer.deserialize(topicName, rawData);
    }

    @Override
    protected void doBusinessLogic(PerflogUploadedChangedDto event) {
        perflogUploadedChangedKafkaFacade.handlePerflogUploadedChangedKafkaEvent(event);
    }

}
