/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionSearchStrategyBean.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.intersectiondata.strategy;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.querydsl.core.types.OrderSpecifier;
import com.siemens.spm.common.message.MessageService;
import com.siemens.spm.common.shared.domaintype.ActionTarget;
import com.siemens.spm.common.shared.domaintype.ActionType;
import com.siemens.spm.common.shared.domaintype.IntersectionStatus;
import com.siemens.spm.common.shared.domaintype.analysis.AnalysisType;
import com.siemens.spm.common.shared.exception.InvalidSortColumnException;
import com.siemens.spm.common.shared.exception.InvalidSortOrderException;
import com.siemens.spm.common.shared.vo.ActionVO;
import com.siemens.spm.common.shared.vo.AnalysisActionDataVO;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionDetailVO;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionInternalListVO;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionInternalVO;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionSearchVO;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionSimpleVO;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionStatusHistoryVO;
import com.siemens.spm.usermanagementservice.api.vo.request.IntersectionSearchRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionDetailResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionInternalSearchResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionSearchResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionSimpleListResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionStatusHistoriesResultObject;
import com.siemens.spm.usermanagementservice.domain.Intersection;
import com.siemens.spm.usermanagementservice.domain.IntersectionStatusHistory;
import com.siemens.spm.usermanagementservice.intersectiondata.util.IntersectionSearchHelper;
import com.siemens.spm.usermanagementservice.intersectiondata.util.IntersectionVOBuilder;
import com.siemens.spm.usermanagementservice.repository.IntersectionRepository;
import com.siemens.spm.usermanagementservice.repository.IntersectionStatusHistoryRepository;
import com.siemens.spm.usermanagementservice.repository.filterdata.IntersectionFilterDataVO;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@Transactional
public class IntersectionSearchStrategyBean implements IntersectionSearchStrategy {

    // repositories
    @Autowired
    private IntersectionRepository intersectionRepository;

    @Autowired
    private IntersectionStatusHistoryRepository intersectionStatusHistoryRepository;

    // others
    @Autowired
    private MessageService translator;

    /**
     * {@inheritDoc}
     */
    @Override
    public IntersectionDetailResultObject getIntersectionDetail(Integer agencyId, String intersectionId) {
        Optional<Intersection> optIntersection = intersectionRepository.findById(intersectionId);

        if (optIntersection.isEmpty()) {
            return new IntersectionDetailResultObject(null,
                    IntersectionDetailResultObject.StatusCode.INTERSECTION_NOT_FOUND);
        }

        Intersection intersection = optIntersection.get();
        IntersectionDetailVO intersectionDetailVO = IntersectionVOBuilder.buildIntersectionDetailVO(intersection);

        return new IntersectionDetailResultObject(intersectionDetailVO,
                IntersectionDetailResultObject.StatusCode.SUCCESS);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public IntersectionSearchResultObject searchIntersections(IntersectionSearchRequestVO searchRequest) {
        Integer agencyId = searchRequest.getAgencyId();

        Pair<List<Intersection>, Long> intersectionsCountPair;

        try {
            intersectionsCountPair = getIntersectionsAndCountFromDB(searchRequest, agencyId);
        } catch (InvalidSortColumnException ex) {
            log.error("Invalid sort column!", ex);

            return new IntersectionSearchResultObject(null,
                    IntersectionSearchResultObject.StatusCode.INVALID_SORT_COLUMN);
        } catch (InvalidSortOrderException ex) {
            log.error("Invalid sort order!", ex);

            return new IntersectionSearchResultObject(null,
                    IntersectionSearchResultObject.StatusCode.INVALID_SORT_ORDER);
        }

        List<Intersection> intersections = intersectionsCountPair.getFirst();

        Long totalCount = intersectionsCountPair.getSecond();

        if (intersections.isEmpty()) {
            var responseData = IntersectionSearchResultObject.ResponseData.builder()
                    .intersections(new ArrayList<>())
                    .totalCount(totalCount)
                    .build();

            return new IntersectionSearchResultObject(responseData,
                    IntersectionSearchResultObject.StatusCode.SUCCESS);
        }

        List<IntersectionSearchVO> intersectionSearchVOs = buildIntersectionSearchVOsForIntersections(intersections,
                agencyId);

        var responseData = IntersectionSearchResultObject.ResponseData.builder()
                .intersections(intersectionSearchVOs)
                .totalCount(totalCount)
                .build();

        return new IntersectionSearchResultObject(responseData,
                IntersectionSearchResultObject.StatusCode.SUCCESS);
    }

    private List<IntersectionSearchVO> buildIntersectionSearchVOsForIntersections(List<Intersection> intersections,
                                                                                  Integer agencyId) {
        return intersections.stream()
                .map(intersection -> {
                    IntersectionSearchVO intersectionSearchVO = IntersectionVOBuilder
                            .buildIntersectionSearchVO(intersection);
                    intersectionSearchVO.setActionMap(createActionMap(agencyId, intersection.getId()));

                    return intersectionSearchVO;
                })
                .toList();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public IntersectionSimpleListResultObject getAllSimpleIntersections(Integer agencyId, String status) {

        IntersectionFilterDataVO intersectionFilterDataVO = IntersectionFilterDataVO.builder()
                .agencyId(agencyId)
                .status(status)
                .build();
        List<Intersection> intersections = intersectionRepository.findAllByFilter(intersectionFilterDataVO);

        ArrayList<IntersectionSimpleVO> responseData = intersections.stream()
                .map(IntersectionVOBuilder::buildIntersectionSimpleVO)
                .sorted(Comparator.comparing(IntersectionSimpleVO::getName, Comparator.nullsLast(String::compareTo)))
                .collect(Collectors.toCollection(ArrayList::new));

        return new IntersectionSimpleListResultObject(responseData,
                IntersectionSimpleListResultObject.StatusCode.SUCCESS);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public IntersectionInternalSearchResultObject searchIntersectionsInternal(
            IntersectionSearchRequestVO searchRequest) {
        Integer agencyId = searchRequest.getAgencyId();

        Pair<List<Intersection>, Long> intersectionsCountPair;

        try {
            intersectionsCountPair = getIntersectionsAndCountFromDB(searchRequest, agencyId);
        } catch (InvalidSortColumnException ex) {
            log.error("Invalid sort column", ex);

            return new IntersectionInternalSearchResultObject(null,
                    IntersectionInternalSearchResultObject.StatusCode.INVALID_SORT_COLUMN);
        } catch (InvalidSortOrderException ex) {
            log.error("Invalid sort order", ex);

            return new IntersectionInternalSearchResultObject(null,
                    IntersectionInternalSearchResultObject.StatusCode.INVALID_SORT_ORDER);
        }

        List<Intersection> intersections = intersectionsCountPair.getFirst();

        Long totalCount = intersectionsCountPair.getSecond();

        List<IntersectionInternalVO> intersectionVOs = intersections.stream()
                .map(IntersectionVOBuilder::buildIntersectionInternalVO)
                .toList();

        IntersectionInternalListVO responseData = IntersectionInternalListVO.builder()
                .intersections(intersectionVOs)
                .totalCount(totalCount)
                .build();

        return new IntersectionInternalSearchResultObject(responseData,
                IntersectionInternalSearchResultObject.StatusCode.SUCCESS);
    }

    @Override
    public IntersectionStatusHistoriesResultObject getIntersectionStatusHistories(Integer agencyId,
                                                                                  String intersectionId,
                                                                                  Integer page,
                                                                                  Integer size) {
        var sort = Sort.by(
                Sort.Order.desc(IntersectionStatusHistory.Constants.FROM_TIME_SORT)); // Sort by fromTime desc
        page = (page == null) ? 0 : page;
        size = (size == null) ? 20 : size;
        var pageRequest = PageRequest.of(page, size, sort);

        // Find all history with status = UNAVAILABLE
        var intersectionStatusHistoriesPage = intersectionStatusHistoryRepository
                .findAllByIntersectionIdAndStatus(intersectionId, IntersectionStatus.UNAVAILABLE.getInsight(),
                        pageRequest);

        var intersectionStatusHistories =
                intersectionStatusHistoriesPage.stream()
                        .map(intersectionStatusHistory -> IntersectionStatusHistoryVO.builder()
                                .id(intersectionStatusHistory.getId())
                                .status(intersectionStatusHistory.getStatus())
                                .intersectionId(intersectionStatusHistory.getIntersectionId())
                                .fromTime(intersectionStatusHistory.getFromTime())
                                .toTime(intersectionStatusHistory.getToTime())
                                .build())
                        .toList();

        var data = IntersectionStatusHistoriesResultObject.ResponseData.builder()
                .totalCount(intersectionStatusHistoriesPage.getTotalElements())
                .intersectionStatusHistories(intersectionStatusHistories)
                .build();

        return new IntersectionStatusHistoriesResultObject(data,
                IntersectionStatusHistoriesResultObject.StatusCode.SUCCESS);
    }

    private Pair<List<Intersection>, Long> getIntersectionsAndCountFromDB(IntersectionSearchRequestVO searchRequest,
                                                                          Integer agencyId) {
        OrderSpecifier<String>[] orderSpecifiers = IntersectionSearchHelper
                .createOrderBy(searchRequest.getOrderByColumns());

        IntersectionFilterDataVO filterDataVO = IntersectionFilterDataVO.builder()
                .agencyId(agencyId)
                .ids(searchRequest.getIntersectionIds())
                .exclusionaryIds(searchRequest.getExclusionaryIds())
                .status(searchRequest.getStatus())
                .text(searchRequest.getText())
                .topLeft(searchRequest.getTopLeft())
                .bottomRight(searchRequest.getBottomRight())
                .build();

        boolean shouldPaginate = searchRequest.getShouldPaginate() != null && searchRequest.getShouldPaginate();

        return shouldPaginate
                ? paginateIntersections(filterDataVO, orderSpecifiers, searchRequest.getPage(), searchRequest.getSize())
                : getAllIntersections(filterDataVO);
    }

    private Pair<List<Intersection>, Long> paginateIntersections(IntersectionFilterDataVO filterData,
                                                                 OrderSpecifier<String>[] orderSpecifiers,
                                                                 Integer page,
                                                                 Integer size) {
        List<Intersection> intersections = intersectionRepository
                .findPageByFilter(filterData, orderSpecifiers, page, size);
        Long totalCount = intersectionRepository.countTotalByFilter(filterData);
        return Pair.of(intersections, totalCount);
    }

    private Pair<List<Intersection>, Long> getAllIntersections(IntersectionFilterDataVO filterData) {
        List<Intersection> intersections = intersectionRepository.findAllByFilter(filterData);
        return Pair.of(intersections, (long) intersections.size());
    }

    /**
     * Create action map (redirect links) for each intersection.
     *
     * @param agencyId agency Id
     * @param intUUID  intersection Id
     * @return Map: analysisTypeId ==> Action
     */
    private Map<String, ActionVO> createActionMap(Integer agencyId, String intUUID) {
        // Use LinkedHashMap instead of HashMap to keep order
        Map<String, ActionVO> actionMap = new LinkedHashMap<>();
        for (AnalysisType type : AnalysisType.values()) {
            AnalysisActionDataVO actionDataVO = AnalysisActionDataVO.builder()
                    .agencyId(agencyId)
                    .intUUID(intUUID)
                    .analysisId(type.getId())
                    .build();
            ActionVO actionVO = ActionVO.builder()
                    .labelKey(type.getName())
                    .type(ActionType.REDIRECT)
                    .target(ActionTarget.ANALYSIS)
                    .data(actionDataVO)
                    .build();
            actionVO.acceptTranslator(translator);
            actionMap.put(type.getId(), actionVO);
        }

        return actionMap;
    }

}
