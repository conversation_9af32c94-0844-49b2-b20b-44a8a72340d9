/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionChangeStrategy.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.intersectiondata.strategy;

import com.siemens.spm.usermanagementservice.domain.Intersection;
import com.siemens.spm.usermanagementservice.intersectiondata.vo.IntersectionMetadataVO;

public interface IntersectionChangeStrategy {

    void deleteIntersection(String id);

    Intersection createIntersection(IntersectionMetadataVO intersectionMetadataVO);

    Intersection updateIntersection(IntersectionMetadataVO intersectionMetadataVO);
}
