/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionKafaFacade.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.intersectiondata.boundary;

import com.siemens.spm.usermanagementservice.common.ChangeTypeEnum;
import com.siemens.spm.usermanagementservice.intersectiondata.vo.IntersectionMetadataVO;

public interface IntersectionKafkaFacade {

    void handleIntersectionMetadataKafkaEvent(ChangeTypeEnum changeTypeEnum, IntersectionMetadataVO eventVO);
}
