package com.siemens.spm.usermanagementservice.intersectiondata.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LocationInfoDto {

    private Double latitude;

    private Double longitude;

    @JsonProperty("timezone_id")
    private String timezoneID;

    private int offset;

    @JsonProperty("country_code")
    private String countryCode;

    @JsonProperty("map_url")
    private String mapURL;
}
