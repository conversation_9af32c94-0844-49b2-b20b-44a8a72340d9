/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionMetadataListener.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.intersectiondata.listener;

import com.fasterxml.jackson.core.type.TypeReference;
import com.siemens.spm.common.kafka.common.KafkaDeserializerFactory;
import com.siemens.spm.common.kafka.common.KafkaEventListener;
import com.siemens.spm.common.kafka.config.KafkaConsumerConfig;
import com.siemens.spm.usermanagementservice.common.ChangeTypeEnum;
import com.siemens.spm.usermanagementservice.intersectiondata.boundary.IntersectionKafkaFacade;
import com.siemens.spm.usermanagementservice.intersectiondata.vo.IntersectionMetadataVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerGroupMetadata;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.serializer.ErrorHandlingDeserializer;
import org.springframework.kafka.support.serializer.JsonDeserializer;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.stereotype.Component;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;

import java.util.Map;
import java.util.Optional;

@Slf4j
@Component
@ConditionalOnProperty(name = "spring.kafka.enable", havingValue = "true")
public class IntersectionMetadataListener extends KafkaEventListener<String, byte[], IntersectionMetadataVO> {

    @Value("${spring.kafka.topic.intersection.name:studio-signal-changed}")
    public String topicName;

    private final ErrorHandlingDeserializer<IntersectionMetadataVO> deserializer;

    private final IntersectionKafkaFacade intersectionKafkaFacade;

    public IntersectionMetadataListener(LocalValidatorFactoryBean validator,
                                        IntersectionKafkaFacade intersectionKafkaFacade) {
        this.deserializer =
                KafkaDeserializerFactory.constructErrorHandlingDeserializer(
                        new JsonDeserializer<>(new TypeReference<>() {
                        }), false, validator);
        this.intersectionKafkaFacade = intersectionKafkaFacade;
    }

    @Override
    @KafkaListener(
            topics = "${spring.kafka.topic.intersection.name:studio-signal-changed}",
            containerFactory = KafkaConsumerConfig.BYTE_CONSUMER_LISTENER_FACTORY_BEAN_NAME,
            concurrency = "${spring.kafka.topic.intersection.concurrency:1}"
    )
    public void listen(@Headers Map<String, Object> headers, ConsumerRecord<String, byte[]> record,
                       KafkaConsumer<String, byte[]> consumer) {
        Optional<ConsumerGroupMetadata> consumerGroupMetadataOpt = Optional.ofNullable(consumer.groupMetadata());
        log.info("Processing event with offset {} and partition {} by consumerId {} in groupId {} ", record.offset(),
                record.partition(), consumerGroupMetadataOpt.map(ConsumerGroupMetadata::memberId).orElse(null),
                consumerGroupMetadataOpt.map(ConsumerGroupMetadata::memberId).orElse(null));

        IntersectionMetadataVO eventVO = convertRawDataToEvent(record.value());

        if (eventVO == null) {
            log.warn("Received null for eventVO. Skipping processing.");
            return;
        }
        doBusinessLogic(eventVO);
        log.info("Done processing event with eventId : {}", eventVO.getId());
    }

    @Override
    protected IntersectionMetadataVO convertRawDataToEvent(byte[] rawData) {
        if (rawData == null || rawData.length == 0) {
            log.warn("Received null or empty for raw data. Skipping processing.");
            return null;
        }
        IntersectionMetadataVO eventVO = deserializer.deserialize(topicName, rawData);
        log.info("Successfully deserializing intersection info event with id : {}", eventVO.getId());
        return eventVO;
    }

    @Override
    protected void doBusinessLogic(IntersectionMetadataVO event) {
        intersectionKafkaFacade.handleIntersectionMetadataKafkaEvent(
                ChangeTypeEnum.fromIgnoreCaseString(event.getChangeType()), event);
    }

}
