/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : VolumeCountVOBuilder.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.intersectiondata.util;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import com.siemens.spm.common.util.ListUtil;
import com.siemens.spm.usermanagementservice.api.vo.VolumeCountVO;

public final class VolumeCountVOBuilder {

    private VolumeCountVOBuilder() {
    }

    public static List<VolumeCountVO> buildVolumeCountVOS(List<com.siemens.spm.perflogcrawler.api.vo.VolumeCountVO> requestVOS) {
        return ListUtil.hasItem(requestVOS)
                ? requestVOS.stream()
                .map(VolumeCountVOBuilder::buildVolumeCountVO)
                .sorted(Comparator.comparing(VolumeCountVO::getFromTime))
                .toList()
                : Collections.emptyList();
    }

    public static VolumeCountVO buildVolumeCountVO(com.siemens.spm.perflogcrawler.api.vo.VolumeCountVO requestVO) {
        return VolumeCountVO.builder()
                .fromTime(requestVO.getFromTime())
                .toTime(requestVO.getToTime())
                .detectorHit(requestVO.getDetectorHit())
                .build();
    }

}
