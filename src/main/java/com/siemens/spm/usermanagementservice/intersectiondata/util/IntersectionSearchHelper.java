/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionSearchHelper.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.intersectiondata.util;

import java.util.Arrays;
import java.util.List;

import com.querydsl.core.types.OrderSpecifier;
import com.siemens.spm.common.util.OrderUtil;
import com.siemens.spm.usermanagementservice.domain.Intersection;
import com.siemens.spm.usermanagementservice.domain.QIntersection;

public final class IntersectionSearchHelper {

    private static final List<String> SORTABLE_COLUMNS = Arrays.asList(
            Intersection.ColumnName.NUMBER,
            Intersection.ColumnName.ID,
            Intersection.ColumnName.NAME,
            Intersection.ColumnName.ADDRESS,
            Intersection.ColumnName.MODEL,
            Intersection.ColumnName.VERSION,
            Intersection.ColumnName.LATITUDE,
            Intersection.ColumnName.LONGITUDE);

    private IntersectionSearchHelper() {
    }

    @SuppressWarnings({ "unchecked" })
    public static OrderSpecifier<String>[] createOrderBy(String[] orderByColumns) {
        if (orderByColumns == null || orderByColumns.length == 0) {
            return new OrderSpecifier[0];
        }

        String entityName = QIntersection.intersection.getMetadata().getName();
        return OrderUtil.createOrderBy(entityName, orderByColumns, SORTABLE_COLUMNS);
    }

}
