/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionVolumeCountVOBuilder.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.intersectiondata.util;

import com.siemens.spm.usermanagementservice.api.vo.IntersectionVolumeCountVO;

import static com.siemens.spm.usermanagementservice.intersectiondata.util.VolumeCountVOBuilder.buildVolumeCountVOS;

public final class IntersectionVolumeCountVOBuilder {

    private IntersectionVolumeCountVOBuilder() {
    }

    public static IntersectionVolumeCountVO buildIntersectionVolumeCountVO(com.siemens.spm.perflogcrawler.api.vo.IntersectionVolumeCountVO requestVO, String intersectionName) {
        if (requestVO == null) {
            return new IntersectionVolumeCountVO();
        }
        return IntersectionVolumeCountVO.builder()
                .intersectionId(requestVO.getIntersectionId())
                .intersectionName(intersectionName)
                .totalCount(requestVO.getTotalCount())
                .volumeCounts(buildVolumeCountVOS(requestVO.getVolumeCounts()))
                .build();
    }

}
