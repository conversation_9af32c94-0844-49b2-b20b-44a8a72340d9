package com.siemens.spm.usermanagementservice.domain;

import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

import static com.siemens.spm.common.constant.AgencyConstants.AGENCY_SCHEMA_PLACEHOLDER;

@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Table(name = NotificationMessage.TABLE_NAME, schema = AGENCY_SCHEMA_PLACEHOLDER)
public class NotificationMessage implements Serializable {

    public static final String TABLE_NAME = "notification_message";

    @EmbeddedId
    private NotificationMessageKey id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "notification_id", updatable = false, insertable = false)
    private Notification notification;

    @Column(name = Notification.ColumnName.NAME, columnDefinition = "TEXT")
    private String name;

    @Column(name = Notification.ColumnName.DESCRIPTION, columnDefinition = "TEXT")
    private String description;

}
