package com.siemens.spm.usermanagementservice.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.MapsId;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;

import static com.siemens.spm.common.constant.AgencyConstants.AGENCY_SCHEMA_PLACEHOLDER;

/**
 * This class represents the notification settings of a user in specific agency. The settings are used by system to
 * determine when to send notifications (via email or system notifications) to user for specific events.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "noti_setting", schema = AGENCY_SCHEMA_PLACEHOLDER)
public class NotiSetting {

    @Id
    private Long id;

    // "alarm_summary" in column name should be abbreviated to "as"

    @Column(name = "as_enabled")
    private boolean alarmSummaryEnabled;

    @Column(name = "as_frequency")
    private String alarmSummaryFrequency;

    // Other notification/email settings should be added below
    // .......................

    @MapsId
    @OneToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "id", nullable = false)
    private AgencyUserSetting agencyUserSetting;

    @Transient
    private LocalDate alarmToDate;

}
