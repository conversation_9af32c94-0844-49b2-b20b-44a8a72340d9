/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : User.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.domain;

import com.siemens.spm.common.audit.AuditableEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

import static com.siemens.spm.common.constant.AgencyConstants.AGENCY_SCHEMA_PLACEHOLDER;

/**
 * User entity.
 */
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
@Table(
    name = User.TABLE_NAME,
    indexes = {
        @Index(name = "idx_email", columnList = "email", unique = true)
    },
    schema = AGENCY_SCHEMA_PLACEHOLDER)
public class User extends AuditableEntity {

    // Using plural for table name because "user" is a reserved keyword in Postgres
    public static final String TABLE_NAME = "users";


    public static final class ColumnName {

        private ColumnName() {
        }

        public static final String ID = "id";
        public static final String NAME = "name";
        public static final String EMAIL = "email";
        public static final String USER_KEY = "user_key";
        public static final String USER_KEY_EXPIRATION = "user_key_expiration";
        public static final String LANGUAGE = "language";

    }


    @Id
    @Column(name = ColumnName.ID)
    private Long id;

    @Column(name = ColumnName.EMAIL, nullable = false, unique = true)
    @Email(message = "invalid_email")
    private String email;

    @Column(name = ColumnName.USER_KEY, unique = true)
    private String userKey;

    @Column(name = ColumnName.USER_KEY_EXPIRATION)
    @Temporal(TemporalType.DATE)
    private Date userKeyExpiration;

    @Size(max = 10)
    @Column(name = ColumnName.LANGUAGE, length = 10)
    private String language;

}
