package com.siemens.spm.usermanagementservice.domain;

import com.siemens.spm.common.audit.AuditableEntity;
import com.siemens.spm.common.constant.AgencyConstants;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

import static com.siemens.spm.common.constant.AgencyConstants.AGENCY_SCHEMA_PLACEHOLDER;
import static com.siemens.spm.usermanagementservice.domain.IntersectionStatusHistory.Constants.TAB_NAME;

/**
 * Author: hungnm Date: 18/12/2024
 */
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(
    name = TAB_NAME, indexes = {
        @Index(name = "intersection_status_hist_iid_idx", columnList = IntersectionStatusHistory.Constants.COL_INT_ID)
    },
    schema = AGENCY_SCHEMA_PLACEHOLDER
)
public class IntersectionStatusHistory extends AuditableEntity {

    public static class Constants {
        private Constants() {
        }

        public static final String TAB_NAME = "intersection_status_history";
        public static final String COL_HIST_ID = "id";
        public static final String COL_INT_ID = "intersection_id";
        public static final String COL_STATUS = "status";
        public static final String COL_TIME_FROM = "time_from";
        public static final String COL_TIME_TO = "time_to";
        public static final String FROM_TIME_SORT = "fromTime";

    }


    @Id
    @Column(name = Constants.COL_HIST_ID)
    private String id;

    @Column(name = Constants.COL_INT_ID)
    private String intersectionId;

    @Column(name = Constants.COL_STATUS)
    private String status;

    @Column(name = Constants.COL_TIME_FROM, nullable = false)
    private Timestamp fromTime;

    @Column(name = Constants.COL_TIME_TO)
    private Timestamp toTime;

}
