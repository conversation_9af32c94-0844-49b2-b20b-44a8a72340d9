package com.siemens.spm.usermanagementservice.domain;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.siemens.spm.common.util.BeanFinder;
import com.siemens.spm.common.util.DateTimeConverter;
import com.siemens.spm.usermanagementservice.api.vo.MapViewWidgetVO;
import com.siemens.spm.usermanagementservice.api.vo.NotificationWidgetVO;
import com.siemens.spm.usermanagementservice.api.vo.OpenAlarmWidgetVO;
import com.siemens.spm.usermanagementservice.api.vo.SummaryReportWidgetVO;
import com.siemens.spm.usermanagementservice.api.vo.TopIntOpenAlarmWidgetVO;
import com.siemens.spm.usermanagementservice.api.vo.VolumeReportWidgetVO;
import com.siemens.spm.usermanagementservice.userdata.util.dashboard.MapViewWidgetFactory;
import com.siemens.spm.usermanagementservice.userdata.util.dashboard.NotificationWidgetFactory;
import com.siemens.spm.usermanagementservice.userdata.util.dashboard.OpenAlarmWidgetFactory;
import com.siemens.spm.usermanagementservice.userdata.util.dashboard.SummaryReportWidgetFactory;
import com.siemens.spm.usermanagementservice.userdata.util.dashboard.TopIntOpenAlarmWidgetFactory;
import com.siemens.spm.usermanagementservice.userdata.util.dashboard.VolumeReportWidgetFactory;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.MapsId;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import static com.siemens.spm.common.constant.AgencyConstants.AGENCY_SCHEMA_PLACEHOLDER;

@Slf4j
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "dashboard_setting", schema = AGENCY_SCHEMA_PLACEHOLDER)
public class DashboardSetting {

    public static class ColumnName {
        private ColumnName() {
        }

        public static final String ID = "id";

    }


    @Id
    @Column(name = ColumnName.ID)
    private Long id;

    @Column(name = "open_alarm_widget", columnDefinition = "TEXT")
    private String openAlarmWidgetJson;

    @Column(name = "map_view_widget", columnDefinition = "TEXT")
    private String mapViewWidgetJson;

    @Column(name = "top_int_open_alarm_widget", columnDefinition = "TEXT")
    private String topIntOpenAlarmWidgetJson;

    @Column(name = "summary_report_widget", columnDefinition = "TEXT")
    private String summaryReportWidgetJson;

    @Column(name = "volume_report_widget", columnDefinition = "TEXT")
    private String volumeReportWidgetJson;

    @Column(name = "notification_widget", columnDefinition = "TEXT")
    private String notificationWidgetJson;

    @MapsId
    @OneToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = ColumnName.ID, nullable = false)
    private AgencyUserSetting agencyUserSetting;

    @Transient
    private OpenAlarmWidgetVO openAlarmWidgetVO;

    @Transient
    private MapViewWidgetVO mapViewWidgetVO;

    @Transient
    private TopIntOpenAlarmWidgetVO topIntOpenAlarmWidgetVO;

    @Transient
    private SummaryReportWidgetVO summaryReportWidgetVO;

    @Transient
    private VolumeReportWidgetVO volumeReportWidgetVO;

    @Transient
    private NotificationWidgetVO notificationWidgetVO;

    /**
     * Synchronize VO transient object fields with the String json fields. This method should be called before all
     * access to vo objects
     */
    public void syncObjectsFromJsons() {
        ObjectMapper objectMapper = BeanFinder.getDefaultObjectMapper();

        if (StringUtils.hasText(openAlarmWidgetJson)) {
            try {
                openAlarmWidgetVO = objectMapper.readValue(openAlarmWidgetJson, OpenAlarmWidgetVO.class);
            } catch (JsonProcessingException e) {
                log.error("Error while parsing open alarm widget json: " + openAlarmWidgetJson, e);

                openAlarmWidgetVO = OpenAlarmWidgetFactory.defaultVO();
            }
        } else {
            openAlarmWidgetVO = null;
        }
        if (StringUtils.hasText(mapViewWidgetJson)) {
            try {
                mapViewWidgetVO = objectMapper.readValue(mapViewWidgetJson, MapViewWidgetVO.class);
            } catch (JsonProcessingException e) {
                log.error("Error while parsing map view widget json: " + mapViewWidgetJson, e);

                mapViewWidgetVO = MapViewWidgetFactory.defaultVO(null);
            }
        } else {
            mapViewWidgetVO = null;
        }
        if (StringUtils.hasText(topIntOpenAlarmWidgetJson)) {
            try {
                topIntOpenAlarmWidgetVO =
                    objectMapper.readValue(topIntOpenAlarmWidgetJson, TopIntOpenAlarmWidgetVO.class);
            } catch (JsonProcessingException e) {
                log.error("Error while parsing top int open alarm widget json: " + topIntOpenAlarmWidgetJson, e);

                topIntOpenAlarmWidgetVO = TopIntOpenAlarmWidgetFactory.defaultVO();
            }
        } else {
            topIntOpenAlarmWidgetVO = null;
        }
        if (StringUtils.hasText(summaryReportWidgetJson)) {
            try {
                summaryReportWidgetVO = objectMapper.readValue(summaryReportWidgetJson, SummaryReportWidgetVO.class);
            } catch (JsonProcessingException e) {
                log.error("Error while parsing summary report widget json: " + summaryReportWidgetJson, e);

                summaryReportWidgetVO = SummaryReportWidgetFactory.defaultVO();
            }
        } else {
            summaryReportWidgetVO = null;
        }
        if (StringUtils.hasText(volumeReportWidgetJson)) {
            try {
                volumeReportWidgetVO = objectMapper.readValue(volumeReportWidgetJson, VolumeReportWidgetVO.class);
            } catch (JsonProcessingException e) {
                log.error("Error while parsing volume report widget json: " + volumeReportWidgetJson, e);

                volumeReportWidgetVO = VolumeReportWidgetFactory.defaultVO();
            }
        } else {
            volumeReportWidgetVO = null;
        }
        if (StringUtils.hasText(notificationWidgetJson)) {
            try {
                notificationWidgetVO = objectMapper.readValue(notificationWidgetJson, NotificationWidgetVO.class);
            } catch (JsonProcessingException e) {
                log.error("Error while parsing notification widget json: " + notificationWidgetJson, e);

                notificationWidgetVO = NotificationWidgetFactory.defaultVO();
            }
        } else {
            notificationWidgetVO = null;
        }
    }


    /**
     * Synchronize VO transient object fields with the String json fields. This method should be called before all
     * access to vo objects
     */
    public void syncObjectsFromJsons(String agencyTimeZoneId) {
        syncObjectsFromJsons();
        if (mapViewWidgetVO != null) {
            if (mapViewWidgetVO.getFromLocalDateTime() == null && mapViewWidgetVO.getFromTime() != null) {
                mapViewWidgetVO.setFromLocalDateTime(DateTimeConverter.toLocalDateTime(mapViewWidgetVO.getFromTime(), agencyTimeZoneId));
            }

            if (mapViewWidgetVO.getToLocalDateTime() == null && mapViewWidgetVO.getToTime() != null) {
                mapViewWidgetVO.setToLocalDateTime(DateTimeConverter.toLocalDateTime(mapViewWidgetVO.getToTime(), agencyTimeZoneId));
            }
        }

        if (summaryReportWidgetVO != null) {
            if (summaryReportWidgetVO.getFromLocalDateTime() == null && summaryReportWidgetVO.getFromTime() != null) {
                summaryReportWidgetVO.setFromLocalDateTime(DateTimeConverter.toLocalDateTime(summaryReportWidgetVO.getFromTime(), agencyTimeZoneId));
            }

            if (summaryReportWidgetVO.getToLocalDateTime() == null && summaryReportWidgetVO.getToTime() != null) {
                summaryReportWidgetVO.setToLocalDateTime(DateTimeConverter.toLocalDateTime(summaryReportWidgetVO.getToTime(), agencyTimeZoneId));
            }
        }

        if (volumeReportWidgetVO != null) {
            if (volumeReportWidgetVO.getFromLocalDateTime() == null && volumeReportWidgetVO.getFromTime() != null) {
                volumeReportWidgetVO.setFromLocalDateTime(DateTimeConverter.toLocalDateTime(volumeReportWidgetVO.getFromTime(), agencyTimeZoneId));
            }

            if (volumeReportWidgetVO.getToLocalDateTime() == null && volumeReportWidgetVO.getToTime() != null) {
                volumeReportWidgetVO.setToLocalDateTime(DateTimeConverter.toLocalDateTime(volumeReportWidgetVO.getToTime(), agencyTimeZoneId));
            }
        }

        if (notificationWidgetVO != null) {
            if (notificationWidgetVO.getFromLocalDateTime() == null && notificationWidgetVO.getFromTime() != null) {
                notificationWidgetVO.setFromLocalDateTime(DateTimeConverter.toLocalDateTime(notificationWidgetVO.getFromTime(), agencyTimeZoneId));
            }

            if (notificationWidgetVO.getToLocalDateTime() == null && notificationWidgetVO.getToTime() != null) {
                notificationWidgetVO.setToLocalDateTime(DateTimeConverter.toLocalDateTime(notificationWidgetVO.getToTime(), agencyTimeZoneId));
            }
        }
    }
}
