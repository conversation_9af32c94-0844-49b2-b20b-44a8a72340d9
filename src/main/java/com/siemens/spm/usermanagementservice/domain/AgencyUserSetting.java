package com.siemens.spm.usermanagementservice.domain;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToOne;
import jakarta.persistence.PrimaryKeyJoinColumn;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import static com.siemens.spm.common.constant.AgencyConstants.AGENCY_SCHEMA_PLACEHOLDER;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "agency_user_setting", schema = AGENCY_SCHEMA_PLACEHOLDER)
public class AgencyUserSetting {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "user_id")
    @NotNull
    private Long userId;

    @Column(name = "agency_id")
    @NotNull
    private Integer agencyId;

    @OneToOne(mappedBy = "agencyUserSetting", cascade = CascadeType.ALL)
    @PrimaryKeyJoinColumn
    private DashboardSetting dashboardSetting;

    @OneToOne(mappedBy = "agencyUserSetting", cascade = CascadeType.ALL)
    @PrimaryKeyJoinColumn
    private NotiSetting notiSetting;

}
