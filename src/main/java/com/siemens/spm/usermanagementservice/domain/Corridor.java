/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : Corridor.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.domain;

import com.siemens.spm.common.audit.AuditableEntity;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

import static com.siemens.spm.common.constant.AgencyConstants.AGENCY_SCHEMA_PLACEHOLDER;

/**
 * Corridor entity.
 */
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = true)
@Table(name = Corridor.TABLE_NAME, schema = AGENCY_SCHEMA_PLACEHOLDER)
public class Corridor extends AuditableEntity {

    public static final String TABLE_NAME = "corridor";


    public static final class ColumnName {

        private ColumnName() {
        }

        public static final String ID = "id";
        public static final String NAME = "name";
        public static final String STATUS = "status";
        public static final String SPEED = "speed";
        public static final String UPSTREAM = "upstream";
        public static final String DOWNSTREAM = "downstream";
        public static final String AGENCY_ID = "agency_id";
        public static final String CORRIDOR = "corridor";

    }


    @Id
    @NotNull
    @Size(max = 50)
    @Column(name = ColumnName.ID, length = 50, nullable = false)
    private String id;

    @Column(name = ColumnName.NAME)
    private String name;

    @Column(name = ColumnName.STATUS, length = 30)
    private String status;

    @Column(name = ColumnName.SPEED)
    private Double speed;

    @Min(1)
    @Max(16)
    @Column(name = ColumnName.UPSTREAM)
    private Integer globalUpstreamPhase;

    @Min(1)
    @Max(16)
    @Column(name = ColumnName.DOWNSTREAM)
    private Integer globalDownstreamPhase;

    @Column(name = ColumnName.AGENCY_ID, nullable = false)
    private Integer agencyId;

    @OneToMany(mappedBy = ColumnName.CORRIDOR, cascade = CascadeType.ALL)
    public List<CorridorIntersection> corridorIntersections;

}
