package com.siemens.spm.usermanagementservice.domain;

import java.util.Optional;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AlarmSummaryFrequency {

    DAILY("DAILY"),
    WEEKLY("WEEKLY"),
    MONTHLY("MONTHLY");

    private final String value;

    public static Optional<AlarmSummaryFrequency> resolve(String value) {
        if (value == null) {
            return Optional.empty();
        }

        for (AlarmSummaryFrequency frequency : AlarmSummaryFrequency.values()) {
            if (frequency.value.equals(value)) {
                return Optional.of(frequency);
            }
        }

        return Optional.empty();
    }

}
