/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : Notification.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.domain;

import com.siemens.spm.common.audit.AuditableEntity;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

import static com.siemens.spm.common.constant.AgencyConstants.AGENCY_SCHEMA_PLACEHOLDER;

/**
 * Notification entity.
 */
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = true)
@Table(
    name = Notification.TABLE_NAME,
    indexes = {
        @Index(name = "idx_notification", columnList = "user_id, agency_id, type_id, read_status")
    },
    schema = AGENCY_SCHEMA_PLACEHOLDER)
public class Notification extends AuditableEntity implements Serializable {

    public static final String TABLE_NAME = "notification";


    public static final class ColumnName {

        private ColumnName() {
        }

        public static final String ID = "id";
        public static final String TYPE_ID = "type_id";
        public static final String USER_ID = "user_id";
        public static final String AGENCY_ID = "agency_id";
        public static final String NAME = "name";
        public static final String DESCRIPTION = "description";
        public static final String CONTENT = "content";
        public static final String READ_TIME = "read_time";
        public static final String READ_STATUS = "read_status";
        public static final String FLAG_STATUS = "flag_status";
        public static final String SEND_STATUS = "send_status";
        public static final String ACTION = "action";

        // below columns are for increasing performance when search for alarm
        // notifications
        public static final String ALARM_TIME_UTC = "alarm_time_utc";
        public static final String INTERSECTION_ID = "intersection_id";
        public static final String INTERSECTION_NAME = "intersection_name";
        public static final String ALARM_CATEGORY_ID = "alarm_category_id";
        public static final String ALARM_CATEGORY_NAME = "alarm_category_name";

    }


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = ColumnName.ID)
    private Long id;

    @Column(name = ColumnName.TYPE_ID, nullable = false)
    private Long typeId;

    @Column(name = ColumnName.USER_ID)
    private Long userId;

    @Column(name = ColumnName.AGENCY_ID, length = 50)
    private Integer agencyId;

    @Column(name = ColumnName.CONTENT, columnDefinition = "TEXT")
    private String content;

    @Column(name = ColumnName.READ_TIME)
    private Timestamp readTime;

    @Column(name = ColumnName.READ_STATUS, length = 30)
    private String readStatus;

    @Column(name = ColumnName.FLAG_STATUS, length = 30)
    private String flagStatus;

    @Column(name = ColumnName.SEND_STATUS)
    private String sendStatus;

    @Column(name = ColumnName.ACTION, columnDefinition = "TEXT")
    private String action;

    // below columns are for increasing performance when search for alarm
    // notifications
    @Column(name = ColumnName.ALARM_TIME_UTC)
    private Timestamp alarmTimeUtc;

    @Column(name = ColumnName.INTERSECTION_ID, length = 50)
    private String intersectionId;

    @Column(name = ColumnName.INTERSECTION_NAME)
    private String intersectionName;

    @Column(name = ColumnName.ALARM_CATEGORY_ID)
    private Long alarmCategoryId;

    @Column(name = ColumnName.ALARM_CATEGORY_NAME)
    private String alarmCategoryName;

    @OneToMany(mappedBy = "notification", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<NotificationMessage> notificationMessage;

}
