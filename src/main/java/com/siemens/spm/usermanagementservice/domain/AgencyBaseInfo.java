/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AgencyFacadeBean.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.domain;

import com.siemens.spm.common.audit.AuditableEntity;
import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import lombok.Getter;
import lombok.Setter;

@MappedSuperclass
@Setter
@Getter
public abstract class AgencyBaseInfo extends AuditableEntity {

    public static final class ColumnName {

        private ColumnName() {
        }

        public static final String NAME = "name";
        public static final String STATUS = "status";

    }

    @Column(name = ColumnName.NAME)
    private String name;

    @Column(name = ColumnName.STATUS)
    private Boolean status;
}
