/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : CorridorIntersection.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.domain;

import com.siemens.spm.common.audit.AuditableEntity;
import com.siemens.spm.common.constant.AgencyConstants;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import static com.siemens.spm.common.constant.AgencyConstants.*;

/**
 * CorridorIntersection entity.
 */
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = true)
@Table(name = CorridorIntersection.TABLE_NAME, schema = AGENCY_SCHEMA_PLACEHOLDER)
public class CorridorIntersection extends AuditableEntity {

    public static final String TABLE_NAME = "corridor_intersection";


    public static final class ColumnName {

        private ColumnName() {
        }

        public static final String ID = "id";
        public static final String NUMBER = "number";
        public static final String DISTANCE = "distance";
        public static final String SPEED = "speed";
        public static final String UPSTREAM = "upstream";
        public static final String DOWNSTREAM = "downstream";
        public static final String CORRIDOR_ID = "corridor_id";
        public static final String INTERSECTION_ID = "intersection_id";

    }


    @Id
    @NotNull
    @Size(max = 50)
    @Column(name = ColumnName.ID, length = 50, nullable = false)
    private String id;

    @Column(name = ColumnName.NUMBER)
    private Integer number;

    @Column(name = ColumnName.DISTANCE)
    private Double distance;

    @Column(name = ColumnName.SPEED)
    private Double speed;

    @Min(1)
    @Max(16)
    @Column(name = ColumnName.UPSTREAM)
    private Integer upstream;

    @Min(1)
    @Max(16)
    @Column(name = ColumnName.DOWNSTREAM)
    private Integer downstream;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = ColumnName.CORRIDOR_ID, updatable = false)
    private Corridor corridor;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = ColumnName.INTERSECTION_ID, updatable = false)
    private Intersection intersection;

}
