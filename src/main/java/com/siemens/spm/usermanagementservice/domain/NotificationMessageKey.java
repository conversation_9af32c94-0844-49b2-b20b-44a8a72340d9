package com.siemens.spm.usermanagementservice.domain;

import java.io.Serializable;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Embeddable
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NotificationMessageKey implements Serializable {

    /**
     * 
     */
    private static final long serialVersionUID = -2047419878799507337L;

    @Column(name = "notification_id")
    @NotNull
    private Long notificationId;

    @Column(name = "language")
    @NotNull
    private String language;
}
