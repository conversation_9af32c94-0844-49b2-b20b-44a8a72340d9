/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AgencySettings.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.domain;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.siemens.spm.common.constant.JpaConstants;
import com.siemens.spm.common.util.BeanFinder;
import com.siemens.spm.usermanagementservice.api.vo.AgencySaturationVO;
import com.siemens.spm.usermanagementservice.api.vo.DisplaySettingsVO;
import com.siemens.spm.usermanagementservice.api.vo.MetricDisplaySettingVO;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.PostLoad;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import static com.siemens.spm.common.constant.AgencyConstants.AGENCY_SCHEMA_PLACEHOLDER;
import static com.siemens.spm.common.constant.AgencyConstants.DEFAULT_ZONE_OFFSET;

@Slf4j
@Entity
@Table(name = "agency_settings", schema = AGENCY_SCHEMA_PLACEHOLDER)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class AgencySettings extends AgencyBaseInfo {

    /**
     * Create default settings
     *
     * @param agencyId
     * @return
     */
    public static AgencySettings getDefault(Integer agencyId) {

        DisplaySettingsVO displaySettingsVO = DisplaySettingsVO.getDefault();
        AgencySaturationVO agencySaturationVO = AgencySaturationVO.getDefault();
        AgencySettings settings =
            AgencySettings.builder().agencyId(agencyId).zoneOffset(DEFAULT_ZONE_OFFSET).showUnavailableMapData(true)
                          // Don't set displaySettingsVO by builder
                          .build();

        try {
            settings.setDisplaySettingsVO(displaySettingsVO);
            settings.setAgencySaturationVO(agencySaturationVO);
        } catch (JsonProcessingException e) {
            // Exception is declared, but if exception is thrown, there are bugs
            log.error("There are bugs when serializing displaySettingsVO", e);
        }

        return settings;
    }

    @Id
    @Column(name = "agency_id", nullable = false)
    private Integer agencyId;

    // Top left point of agency
    @Column(name = "top_left_lat")
    private Double topLeftLat;

    @Column(name = "top_left_long")
    private Double topLeftLong;

    @Column(name = "central_intersection_latitude")
    private Double centralIntersectionLatitude;

    @Column(name = "central_intersection_longitude")
    private Double centralIntersectionLongitude;

    // this should be something like "-hh:mm" or "+hh:mm" or "Z"
    @Column(name = "zone_offset", length = JpaConstants.ZONE_OFFSET_MAX_LENGTH)
    private String zoneOffset;

    @Column(name = "display_settings_lob", columnDefinition = "TEXT")
    private String displaySettingsLob;

    @Column(name = "saturation_lob", columnDefinition = "TEXT")
    private String saturationLob;

    @Column(name = "show_unavailable_map_data")
    private Boolean showUnavailableMapData;

    @Transient
    private DisplaySettingsVO displaySettingsVO;

    @Transient
    private AgencySaturationVO agencySaturationVO;

    /**
     * Set displaySettingsVO and serialize to displaySettingsLob
     *
     * @param displaySettingsVO
     * @throws JsonProcessingException
     */
    public void setDisplaySettingsVO(DisplaySettingsVO displaySettingsVO) throws JsonProcessingException {
        this.displaySettingsVO = displaySettingsVO;
        this.displaySettingsLob = BeanFinder.getDefaultObjectMapper().writeValueAsString(displaySettingsVO);
    }

    public void setAgencySaturationVO(AgencySaturationVO agencySaturationVO) throws JsonProcessingException {
        this.agencySaturationVO = agencySaturationVO;
        this.saturationLob = BeanFinder.getDefaultObjectMapper().writeValueAsString(agencySaturationVO);
    }

    @PostLoad
    private void postLoad() {
        ObjectMapper objectMapper = BeanFinder.getDefaultObjectMapper();

        // Build display setting data
        try {
            if (!StringUtils.hasText(displaySettingsLob)) {
                displaySettingsVO = DisplaySettingsVO.getDefault();
                log.debug("Using default value for display setting of agency, agencyId = {}", agencyId);
            } else {
                displaySettingsVO = objectMapper.readValue(displaySettingsLob, DisplaySettingsVO.class);
            }

            // NOTE: Add additional display setting here to support old agency
            if (displaySettingsVO != null) {
                if (displaySettingsVO.getAvgPedDelayDisplaySettingVO() == null) {
                    displaySettingsVO.setAvgPedDelayDisplaySettingVO(
                        MetricDisplaySettingVO.getDefaultAvgPedDelayDisplaySetting());
                }

                if (displaySettingsVO.getAvgQueueLengthDisplaySettingVO() == null) {
                    displaySettingsVO.setAvgQueueLengthDisplaySettingVO(
                        MetricDisplaySettingVO.getDefaultAvgQueueLengthDisplaySetting());
                }

                if (displaySettingsVO.getAvgAppDelayDisplaySettingVO() == null) {
                    displaySettingsVO.setAvgAppDelayDisplaySettingVO(
                        MetricDisplaySettingVO.getDefaultAvgAppDelayDisplaySetting());
                }

                if (displaySettingsVO.getAogPercentDisplaySettingVO() == null) {
                    displaySettingsVO.setAogPercentDisplaySettingVO(
                        MetricDisplaySettingVO.getDefaultAogPercentDisplaySetting());
                }

                if (displaySettingsVO.getVehicleVolumeDisplaySettingVO() == null) {
                    displaySettingsVO.setVehicleVolumeDisplaySettingVO(
                        MetricDisplaySettingVO.getDefaultVehicleVolumeDisplaySetting());
                }

                if (displaySettingsVO.getCoordHealthDisplaySettingVO() == null) {
                    displaySettingsVO.setCoordHealthDisplaySettingVO(
                        MetricDisplaySettingVO.getDefaultCoordHealthDisplaySetting());
                }

                if (displaySettingsVO.getSfDisplaySettingVO() == null) {
                    displaySettingsVO.setSfDisplaySettingVO(
                        MetricDisplaySettingVO.getDefaultSplitFailureDisplaySetting());
                }

                if (displaySettingsVO.getPpRequestsDisplaySettingVO() == null) {
                    displaySettingsVO.setPpRequestsDisplaySettingVO(
                        MetricDisplaySettingVO.getDefaultPpRequestsDisplaySetting());
                }

                if (displaySettingsVO.getRlvDisplaySettingVO() == null) {
                    displaySettingsVO.setRlvDisplaySettingVO(MetricDisplaySettingVO.getDefaultRlvDisplaySetting());
                }
            }
        } catch (JsonProcessingException e) {
            // There should be a bug
            log.error("Error occur while parse agency display setting, agencyId = {}", agencyId, e);

            displaySettingsVO = null;
        }

        // Build saturation setting data
        try {
            if (!StringUtils.hasText(saturationLob)) {
                agencySaturationVO = AgencySaturationVO.getDefault();
                log.debug("Using default value for saturation setting of agency, agencyId = {}", agencyId);
            } else {
                agencySaturationVO = objectMapper.readValue(saturationLob, AgencySaturationVO.class);
                if (agencySaturationVO == null) {
                    agencySaturationVO = AgencySaturationVO.getDefault();
                    log.debug("agency saturation setting have no value!Using default! agencyId = {}", agencyId);
                }
            }
        } catch (JsonProcessingException e) {
            log.error(
                "Error occur while parse agencySaturation setting to object! Using default! AgencyId =" + agencyId, e);
            agencySaturationVO = AgencySaturationVO.getDefault();
        }
    }

}
