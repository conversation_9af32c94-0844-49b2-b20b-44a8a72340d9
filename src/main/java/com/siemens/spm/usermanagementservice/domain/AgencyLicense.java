package com.siemens.spm.usermanagementservice.domain;

import com.siemens.spm.common.audit.AuditableEntity;
import com.siemens.spm.common.converter.SimpleListToJsonConverter;
import com.siemens.spm.common.shared.domaintype.License;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.Hibernate;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static com.siemens.spm.common.constant.AgencyConstants.AGENCY_SCHEMA_PLACEHOLDER;

@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Entity
@Table(name = "agency_license", schema = AGENCY_SCHEMA_PLACEHOLDER)
public class AgencyLicense extends AuditableEntity {

    public static final class ColumnName {

        private ColumnName() {
        }

        public static final String AGENCY_ID = "agency_id";
        public static final String LICENSE = "license_ids";

    }


    @Id
    @Column(name = ColumnName.AGENCY_ID)
    @NotNull
    private Integer agencyId;

    @NotNull
    @Column(name = ColumnName.LICENSE, nullable = false)
    @Convert(converter = SimpleListToJsonConverter.class)
    private List<String> licensesIds;

    public static AgencyLicense getDefault(Integer agencyId) {
        List<String> licenses =
            Arrays.stream(License.values()).filter(License::isDefaultEnabled).map(License::getId).toList();

        return AgencyLicense.builder().agencyId(agencyId).licensesIds(licenses).build();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) {
            return false;
        }
        AgencyLicense that = (AgencyLicense) o;
        return Objects.equals(agencyId, that.agencyId);
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }

}
