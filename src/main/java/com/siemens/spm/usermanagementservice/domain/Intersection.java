/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : Intersection.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.domain;

import com.siemens.spm.common.audit.AuditableEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.TimeZone;

import static com.siemens.spm.common.constant.AgencyConstants.AGENCY_SCHEMA_PLACEHOLDER;

/**
 * Intersection entity.
 */
@Entity
@Table(
    name = "intersection",
    indexes = {
        @Index(name = "idx_intersection", columnList = "status, longitude, latitude")
    },
    schema = AGENCY_SCHEMA_PLACEHOLDER)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = true)
public class Intersection extends AuditableEntity {

    public static final class ColumnName {

        private ColumnName() {
        }

        public static final String ID = "id";
        public static final String NUMBER = "number";
        public static final String NAME = "name";
        public static final String ADDRESS = "address";
        public static final String MODEL = "model";
        public static final String VERSION = "version";
        public static final String STATUS = "status";
        public static final String LATITUDE = "latitude";
        public static final String LONGITUDE = "longitude";
        public static final String TIMEZONE = "timezone";
        public static final String PERFLOG_RECENT_TIME = "perflog_recent_time";

    }


    @Id
    @NotNull
    @Size(max = 50)
    @Column(name = ColumnName.ID, length = 50, nullable = false)
    private String id;

    @Column(name = ColumnName.NUMBER)
    private String number;

    @Column(name = ColumnName.NAME)
    private String name;

    @Column(name = ColumnName.ADDRESS)
    private String address;

    @Column(name = ColumnName.MODEL)
    private String model;

    @Column(name = ColumnName.VERSION)
    private String version;

    @Column(name = ColumnName.STATUS, length = 30)
    private String status;

    @Column(name = ColumnName.LATITUDE)
    private Double latitude;

    @Column(name = ColumnName.LONGITUDE)
    private Double longitude;

    @Column(name = ColumnName.TIMEZONE)
    private TimeZone timeZone;

    /**
     * Actual stored value is in UTC 
     */
    @Column(name = ColumnName.PERFLOG_RECENT_TIME)
    private LocalDateTime perfLogRecentTime;

}
