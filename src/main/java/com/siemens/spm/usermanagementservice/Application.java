/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : Application.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice;

import com.siemens.spm.common.agency.supports.multiagencies.EnableMultiAgenciesPersistence;
import com.siemens.spm.common.agency.supports.provision.EnableAgencyProvisionAutoConfiguration;
import com.siemens.spm.common.audit.AuditorAwareBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.liquibase.LiquibaseAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;

@SpringBootApplication(
    exclude = {
        LiquibaseAutoConfiguration.class,
        HibernateJpaAutoConfiguration.class
    })
@EnableJpaAuditing(auditorAwareRef = "auditorAware")
@ComponentScan("com.siemens")
@EnableMultiAgenciesPersistence(
    basePackages = "com.siemens.spm.usermanagementservice.repository",
    entityPackages = "com.siemens.spm.usermanagementservice.domain")
@EnableCaching
@EnableAgencyProvisionAutoConfiguration
@Slf4j
public class Application implements CommandLineRunner {

    private static final long MEGABYTE = 1024 * 1024L;

    public static void main(String[] args) {
        SpringApplication springApplication = new SpringApplication(Application.class);

        // Quick and dirty fix to allow restricted headers (Host) in HTTP requests to DataHub
        System.setProperty("sun.net.http.allowRestrictedHeaders", "true");

        springApplication.run(args);
    }

    @Bean
    public AuditorAware<String> auditorAware() {
        return new AuditorAwareBean();
    }

    @Override
    public void run(String... args) {
        long freeMemory = Runtime.getRuntime().freeMemory() / MEGABYTE;
        long totalMemory = Runtime.getRuntime().totalMemory() / MEGABYTE;
        long maxMemory = Runtime.getRuntime().maxMemory() / MEGABYTE;

        log.debug("JVM freeMemory: {} Mb", freeMemory);
        log.debug("JVM totalMemory: {} Mb", totalMemory);
        log.debug("JVM maxMemory: {} Mb", maxMemory);
    }

}
