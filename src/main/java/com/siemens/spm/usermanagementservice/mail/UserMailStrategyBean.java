package com.siemens.spm.usermanagementservice.mail;

import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import com.siemens.spm.notification.config.MailConfig;
import com.siemens.spm.notification.exception.NotificationSenderException;
import com.siemens.spm.notification.service.NotificationSenderService;
import com.siemens.spm.notification.vo.EmailMessageVO;
import com.siemens.spm.usermanagementservice.api.vo.UserVO;
import com.siemens.spm.usermanagementservice.config.UserConfig;
import lombok.extern.slf4j.Slf4j;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring6.SpringTemplateEngine;

@Slf4j
@Service
public class UserMailStrategyBean implements UserMailStrategy {

    @Autowired
    private UserConfig userConfig;

    @Autowired
    private MailConfig mailConfig;

    @Autowired
    private MessageSource messageSource;

    @Autowired
    private SpringTemplateEngine templateEngine;

    @Autowired
    private NotificationSenderService notificationService;

    @Override
    public void sendUserKeyExpirationNotification(UserVO userVO, int expiredInDays) {
        if (userVO == null) {
            log.error("Can not send email to a null user!");
            return;
        }

        Map<String, Object> variables = createDefaultVariable(userVO);
        variables.put(MailConstants.REMAINING_DAYS, expiredInDays);

        sendMail("user_key_expiration", mailConfig.getSenderEmail(), userVO.getEmail(), variables,
                "user-key-expiration.title");
    }

    private Map<String, Object> createDefaultVariable(UserVO userVO) {
        Map<String, Object> ret = new HashMap<>();
        ret.put(MailConstants.USER, userVO);
        ret.put(MailConstants.FAVICON_URL, mailConfig.getFaviconUrl());
        ret.put(MailConstants.WEB_UI_ENDPOINT, userConfig.getWebUiEndpoint());

        return ret;
    }

    /**
     * @param templateName Template file in resources/templates/mail
     * @param from         Sender email
     * @param to           Receiver email
     * @param variables
     * @param subjectKey
     */
    private void sendMail(String templateName,
                          String from,
                          String to,
                          Map<String, Object> variables,
                          String subjectKey) {
        Locale locale = Locale.getDefault();
        Context context = new Context(locale);
        context.setVariables(variables);

        String content = templateEngine.process(templateName, context);
        String subject = messageSource.getMessage(subjectKey, null, locale);

        EmailMessageVO emailVO = EmailMessageVO.builder()
                .sender(from)
                .toRecipients(List.of(to))
                .subject(subject)
                .content(content)
                .build();

        try {
            notificationService.sendMessage(emailVO);
        } catch (NotificationSenderException e) {
            log.error("Error while sending email to: " + to, e);
        }
    }

}
