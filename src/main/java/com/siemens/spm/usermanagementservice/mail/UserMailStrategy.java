package com.siemens.spm.usermanagementservice.mail;

import org.springframework.scheduling.annotation.Async;

import com.siemens.spm.usermanagementservice.api.vo.UserVO;

public interface UserMailStrategy {

    /**
     * Send email to notify user that their user key is about expire.
     *
     * @param userVO
     */
    @Async
    void sendUserKeyExpirationNotification(UserVO userVO, int expiredInDays);
}
