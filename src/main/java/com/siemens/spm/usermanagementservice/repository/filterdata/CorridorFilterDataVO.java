/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : CorridorFilterDataVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.repository.filterdata;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CorridorFilterDataVO {

    private Integer agencyId;

    private List<String> ids;

    private List<String> excludeIds;

    private String text;

    private String status;

    @JsonProperty("pagination")
    private boolean shouldPaginate;
}
