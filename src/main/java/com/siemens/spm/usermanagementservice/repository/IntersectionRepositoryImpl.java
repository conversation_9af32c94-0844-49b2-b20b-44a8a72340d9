/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionRepositoryImpl.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.repository;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.group.GroupBy;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.JPQLTemplates;
import com.querydsl.jpa.impl.JPAQuery;
import com.siemens.spm.common.constant.JpaConstants;
import com.siemens.spm.usermanagementservice.domain.Intersection;
import com.siemens.spm.usermanagementservice.domain.QIntersection;
import com.siemens.spm.usermanagementservice.repository.filterdata.IntersectionFilterDataVO;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

public class IntersectionRepositoryImpl implements IntersectionRepositoryCustom {

    @PersistenceContext
    private EntityManager entityManager;

    @Transactional
    @Override
    public Map<String, String> findNamesByIdIn(List<String> ids) {
        var filterDataVO = IntersectionFilterDataVO.builder()
                .ids(ids)
                .build();

        BooleanBuilder whereClause = buildWhereClauseByFilter(filterDataVO);

        QIntersection qIntersection = QIntersection.intersection;

        var query = new JPAQuery<>(entityManager, JPQLTemplates.DEFAULT);

        return query.from(qIntersection)
                .where(whereClause)
                .transform(GroupBy.groupBy(qIntersection.id).as(qIntersection.name));
    }

    @Override
    public List<Intersection> findAllByFilter(IntersectionFilterDataVO filterDataVO) {
        BooleanBuilder whereClause = buildWhereClauseByFilter(filterDataVO);

        QIntersection qIntersection = QIntersection.intersection;

        var query = new JPAQuery<>(entityManager);

        return query.select(qIntersection)
                .from(qIntersection)
                .where(whereClause)
                .fetch();
    }

    @Override
    public List<Intersection> findPageByFilter(IntersectionFilterDataVO filterDataVO,
                                               OrderSpecifier<String>[] orderSpecifiers, Integer page, Integer size) {
        page = (page == null) ? 0 : page;

        size = (size == null) ? JpaConstants.Size.DEFAULT_SIZE : size;
        size = size > JpaConstants.Size.BIG_MAX_SIZE ? JpaConstants.Size.BIG_MAX_SIZE : size;

        BooleanBuilder whereClause = buildWhereClauseByFilter(filterDataVO);

        QIntersection qIntersection = QIntersection.intersection;

        var query = new JPAQuery<>(entityManager);

        return query.select(qIntersection)
                .from(qIntersection)
                .where(whereClause)
                .limit(size)
                .offset((long) page * size)
                .orderBy(orderSpecifiers)
                .fetch();
    }

    @Override
    public Long countTotalByFilter(IntersectionFilterDataVO filterDataVO) {
        BooleanBuilder whereClause = buildWhereClauseByFilter(filterDataVO);

        QIntersection qIntersection = QIntersection.intersection;

        var query = new JPAQuery<>(entityManager);

        return query.select(qIntersection)
                .from(qIntersection)
                .where(whereClause)
                .fetchCount();
    }

    private BooleanBuilder buildWhereClauseByFilter(IntersectionFilterDataVO filterDataVO) {
        QIntersection qIntersection = QIntersection.intersection;

        var booleanBuilder = new BooleanBuilder();

        // order predicates by cardinality (higher first)
        // refer to here for definition of cardinality:
        // https://en.wikipedia.org/wiki/Cardinality_(SQL_statements)

        List<String> ids = filterDataVO.getIds();
        if (ids != null && !ids.isEmpty()) {
            booleanBuilder.and(qIntersection.id.in(ids));
        }

        List<String> exclusionaryIds = filterDataVO.getExclusionaryIds();
        if (exclusionaryIds != null && !exclusionaryIds.isEmpty()) {
            booleanBuilder.and(qIntersection.id.notIn(exclusionaryIds));
        }

        Double[] bottomRight = filterDataVO.getBottomRight();
        Double[] topLeft = filterDataVO.getTopLeft();
        if (topLeft != null && bottomRight != null && topLeft.length == 2 && bottomRight.length == 2) {
            Double minLat = Math.min(bottomRight[0], topLeft[0]);
            Double maxLat = Math.max(bottomRight[0], topLeft[0]);

            Double minLon = Math.min(bottomRight[1], topLeft[1]);
            Double maxLon = Math.max(bottomRight[1], topLeft[1]);

            Predicate latitudePredicate = qIntersection.latitude.between(minLat, maxLat);
            Predicate longitudePredicate = qIntersection.longitude.between(minLon, maxLon);

            booleanBuilder.and(latitudePredicate).and(longitudePredicate);
        }

        String text = filterDataVO.getText();
        if (StringUtils.hasText(text)) {
            BooleanExpression textBooleanExpression = qIntersection.name.likeIgnoreCase("%" + text + "%")
                    .or(qIntersection.id.likeIgnoreCase("%" + text + "%"));

            booleanBuilder.and(textBooleanExpression);
        }

        String name = filterDataVO.getName();
        if (StringUtils.hasText(name)) {
            BooleanExpression nameBooleanExpression = qIntersection.name.likeIgnoreCase("%" + name + "%");
            booleanBuilder.and(nameBooleanExpression);
        }

        String status = filterDataVO.getStatus();
        if (StringUtils.hasText(status)) {
            booleanBuilder.and(qIntersection.status.eq(status));
        }

        return booleanBuilder;
    }

}
