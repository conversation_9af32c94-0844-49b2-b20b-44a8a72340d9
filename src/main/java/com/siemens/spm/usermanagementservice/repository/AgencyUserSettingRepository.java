/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AgencyUserSettingRepository.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.repository;

import com.siemens.spm.common.agency.utils.AgencyPersistenceConstants;
import com.siemens.spm.usermanagementservice.domain.AgencyUserSetting;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface AgencyUserSettingRepository extends JpaRepository<AgencyUserSetting, Long> {

    AgencyUserSetting findByAgencyIdAndUserId(@NotNull Integer agencyId, @NotNull Long userId);

    boolean existsByAgencyIdAndUserId(@NotNull Integer agencyId, @NotNull Long userId);

    @Transactional(transactionManager = AgencyPersistenceConstants.AGENCY_TRANSACTION_MANAGER)
    void deleteByAgencyId(@NotNull Integer agencyId);

}
