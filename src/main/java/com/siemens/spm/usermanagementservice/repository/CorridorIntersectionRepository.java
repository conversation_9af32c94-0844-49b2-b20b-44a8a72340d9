package com.siemens.spm.usermanagementservice.repository;

import com.siemens.spm.common.agency.utils.AgencyPersistenceConstants;
import com.siemens.spm.usermanagementservice.domain.Corridor;
import com.siemens.spm.usermanagementservice.domain.CorridorIntersection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Set;

/**
 * Spring Data JPA repository for the CorridorIntersection entity.
 */
@Repository
public interface CorridorIntersectionRepository extends JpaRepository<CorridorIntersection, String> {

    @Transactional(transactionManager = AgencyPersistenceConstants.AGENCY_TRANSACTION_MANAGER)
    void deleteAllByCorridor(Corridor corridor);


    @Transactional(transactionManager = AgencyPersistenceConstants.AGENCY_TRANSACTION_MANAGER)
    void deleteAllByIntersectionId(String intID);

    Set<CorridorIntersection> findAllByIntersectionIdIn(Set<String> intersectionIds);

}
