package com.siemens.spm.usermanagementservice.repository;

import com.siemens.spm.usermanagementservice.domain.IntersectionStatusHistory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Author: hungnm Date: 18/12/2024
 */
@Repository
public interface IntersectionStatusHistoryRepository extends JpaRepository<IntersectionStatusHistory, Integer> {
    Page<IntersectionStatusHistory> findAllByIntersectionIdAndStatus(String intersectionId,
                                                                     String status,
                                                                     Pageable pageable);

    Optional<IntersectionStatusHistory> findFirstByIntersectionIdOrderByLastModifiedAtDesc(String intersectionId);

    Optional<IntersectionStatusHistory> findFirstByOrderByLastModifiedAtDesc();
}
