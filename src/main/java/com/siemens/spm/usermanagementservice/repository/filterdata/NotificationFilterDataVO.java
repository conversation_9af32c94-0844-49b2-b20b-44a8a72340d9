/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : NotificationFilterDataVO.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.repository.filterdata;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NotificationFilterDataVO {

    private Long alarmCategoryId;

    private Timestamp alarmTimeUtcFrom;

    private Timestamp alarmTimeUtcTo;

    private Integer agencyId;

    private Timestamp createdAtFrom;

    private Timestamp createdAtTo;

    private List<Long> ids;

    private List<String> intIds;

    // exclude intersection Ids
    private List<String> excludeIntIds;

    private List<Long> excludeIds;

    private String flagStatus;

    // this is for searching by intersection name or intersection id
    private String intersection;

    // this is for searching by intersection id only
    private String intersectionId;

    private String readStatus;

    private String text;

    private List<Long> typeIds;

    private Long typeId;

    private Long userId;

    @Builder.Default
    private boolean allowNullIntersection = true;

}
