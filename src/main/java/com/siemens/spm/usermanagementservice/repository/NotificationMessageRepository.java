package com.siemens.spm.usermanagementservice.repository;

import com.siemens.spm.usermanagementservice.domain.NotificationMessage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface NotificationMessageRepository extends JpaRepository<NotificationMessage, Long> {

    @Modifying
    @Transactional
    @Query("""
            delete from NotificationMessage m where m.notification.id in (
                select n.id from Notification n where n.createdAt < ?1 and n.intersectionId in (?2)
            )
            """)
    int deleteMessages(LocalDateTime toTime, List<String> intersectionIds);
}
