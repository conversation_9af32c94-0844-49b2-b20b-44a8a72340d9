/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : NotificationRepository.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.repository;

import com.siemens.spm.common.agency.utils.AgencyPersistenceConstants;
import com.siemens.spm.usermanagementservice.domain.Notification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface NotificationRepository extends JpaRepository<Notification, Long>, NotificationRepositoryCustom {

    List<Notification> findAllByUserId(Long userId);

    Notification findByIdAndUserId(Long id, Long userId);

    @Transactional(transactionManager = AgencyPersistenceConstants.AGENCY_TRANSACTION_MANAGER)
    void deleteByIdAndUserId(Long id, Long userId);

    @Transactional(transactionManager = AgencyPersistenceConstants.AGENCY_TRANSACTION_MANAGER)
    void deleteAllByUserIdIn(List<Long> userIds);

    @Modifying
    @Transactional
    @Query("delete from Notification n where n.createdAt < ?1 and n.intersectionId in (?2)")
    int deleteNotifications(LocalDateTime toTime, List<String> intersectionIds);
}
