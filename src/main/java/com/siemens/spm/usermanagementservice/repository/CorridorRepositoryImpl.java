/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : CorridorRepositoryImpl.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.repository;

import java.util.List;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import org.springframework.util.StringUtils;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQuery;
import com.siemens.spm.common.constant.JpaConstants;
import com.siemens.spm.common.util.ListUtil;
import com.siemens.spm.usermanagementservice.domain.Corridor;
import com.siemens.spm.usermanagementservice.domain.QCorridor;
import com.siemens.spm.usermanagementservice.repository.filterdata.CorridorFilterDataVO;

public class CorridorRepositoryImpl implements CorridorRepositoryCustom {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public List<Corridor> findPageByFilter(CorridorFilterDataVO filterDataVO,
                                           OrderSpecifier<String>[] orderSpecifiers,
                                           Integer page,
                                           Integer size) {
        page = (page == null) ? JpaConstants.Page.DEFAULT_PAGE : page;
        size = (size == null) ? JpaConstants.Size.DEFAULT_SIZE : size;
        orderSpecifiers = (orderSpecifiers == null) ? new OrderSpecifier[0] : orderSpecifiers;

        int limit = size > JpaConstants.Size.BIG_MAX_SIZE ? JpaConstants.Size.BIG_MAX_SIZE : size;

        BooleanBuilder whereClause = buildWhereClauseByFilter(filterDataVO);

        QCorridor qCorridor = QCorridor.corridor;

        if(filterDataVO.isShouldPaginate()){
            return new JPAQuery<>(entityManager)
                    .select(qCorridor)
                    .from(qCorridor)
                    .where(whereClause)
                    .limit(limit)
                    .offset((long) page * size)
                    .orderBy(orderSpecifiers)
                    .fetch();
        }
        return new JPAQuery<>(entityManager)
                .select(qCorridor)
                .from(qCorridor)
                .where(whereClause)
                .orderBy(orderSpecifiers)
                .fetch();
    }

    @Override
    public Long countTotalByFilter(CorridorFilterDataVO filterDataVO) {
        BooleanBuilder whereClause = buildWhereClauseByFilter(filterDataVO);

        QCorridor qCorridor = QCorridor.corridor;

        var query = new JPAQuery<>(entityManager);

        return query.select(qCorridor)
                .from(qCorridor)
                .where(whereClause)
                .fetchCount();
    }

    private BooleanBuilder buildWhereClauseByFilter(CorridorFilterDataVO filterDataVO) {
        QCorridor qCorridor = QCorridor.corridor;

        var booleanBuilder = new BooleanBuilder();

        Integer agencyId = filterDataVO.getAgencyId();
        if (agencyId != null) {
            booleanBuilder.and(qCorridor.agencyId.eq(agencyId));
        }

        List<String> ids = filterDataVO.getIds();
        if (ListUtil.hasItem(ids)) {
            booleanBuilder.and(qCorridor.id.in(ids));
        }

        List<String> excludeIds = filterDataVO.getExcludeIds();
        if (ListUtil.hasItem(excludeIds)) {
            booleanBuilder.and(qCorridor.id.notIn(excludeIds));
        }

        String text = filterDataVO.getText();
        if (StringUtils.hasText(text)) {
            BooleanExpression textBE = qCorridor.name.likeIgnoreCase("%" + text + "%");

            booleanBuilder.and(textBE);
        }

        String status = filterDataVO.getStatus();
        if (StringUtils.hasText(status)) {
            booleanBuilder.and(qCorridor.status.eq(status));
        }

        return booleanBuilder;
    }

}
