/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : NotificationTypeRepository.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.repository;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.springframework.stereotype.Component;

import com.siemens.spm.common.shared.domaintype.notification.NotificationType;

@Component
public class NotificationTypeRepository {

    public List<NotificationType> findAll() {
        return Arrays.asList(NotificationType.class.getEnumConstants());
    }

    public List<NotificationType> findAllByIdIn(List<Long> ids) {
        List<NotificationType> notificationTypes = new ArrayList<>();

        for (Long id : ids) {
            NotificationType notificationType = NotificationType.getById(id);

            if (notificationType != null) {
                notificationTypes.add(notificationType);
            }
        }

        return notificationTypes;
    }

}