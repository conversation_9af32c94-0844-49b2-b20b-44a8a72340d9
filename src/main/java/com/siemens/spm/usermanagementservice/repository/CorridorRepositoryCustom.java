/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : CorridorRepositoryCustom.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.repository;

import java.util.List;

import com.querydsl.core.types.OrderSpecifier;
import com.siemens.spm.usermanagementservice.domain.Corridor;
import com.siemens.spm.usermanagementservice.repository.filterdata.CorridorFilterDataVO;

/**
 * Spring Data JPA repository for the Corridor entity.
 */
public interface CorridorRepositoryCustom {

    List<Corridor> findPageByFilter(CorridorFilterDataVO filterDataVO,
                                    OrderSpecifier<String>[] orderSpecifiers,
                                    Integer page,
                                    Integer size);

    Long countTotalByFilter(CorridorFilterDataVO filterDataVO);

}
