/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionRepositoryCustom.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.repository;

import java.util.List;
import java.util.Map;

import com.querydsl.core.types.OrderSpecifier;
import com.siemens.spm.usermanagementservice.domain.Intersection;
import com.siemens.spm.usermanagementservice.repository.filterdata.IntersectionFilterDataVO;

/**
 * Spring Data JPA repository for the Intersection entity.
 */
public interface IntersectionRepositoryCustom {

    Map<String, String> findNamesByIdIn(List<String> ids);

    List<Intersection> findAllByFilter(IntersectionFilterDataVO filterDataVO);

    List<Intersection> findPageByFilter(IntersectionFilterDataVO filterDataVO,
                                        OrderSpecifier<String>[] orderSpecifiers,
                                        Integer page,
                                        Integer size);

    Long countTotalByFilter(IntersectionFilterDataVO filterDataVO);

}

