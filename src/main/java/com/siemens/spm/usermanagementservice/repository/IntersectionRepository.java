/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionRepository.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.repository;

import com.siemens.spm.usermanagementservice.domain.Intersection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Spring Data JPA repository for the Intersection entity.
 */
@Repository
public interface IntersectionRepository extends JpaRepository<Intersection, String>, IntersectionRepositoryCustom {

    List<Intersection> findAllByIdIn(List<String> ids);

    @Transactional
    @Modifying
    @Query("update Intersection set perfLogRecentTime = :recentTime where id = :intUUID")
    void updatePerfLogRecentTime(@Param("intUUID") String intUUID, @Param("recentTime") LocalDateTime recentTime);

}
