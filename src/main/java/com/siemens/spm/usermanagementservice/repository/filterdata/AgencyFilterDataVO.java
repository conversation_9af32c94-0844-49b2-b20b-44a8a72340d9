/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AgencyFilterDataVO.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.repository.filterdata;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgencyFilterDataVO {

    private List<Integer> ids;

    private String status;

    private String text;

    private String zoneOffset;

}
