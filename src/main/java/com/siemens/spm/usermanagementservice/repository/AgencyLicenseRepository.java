/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AgencyLicenseRepository.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.repository;

import com.siemens.spm.common.agency.utils.AgencyPersistenceConstants;
import com.siemens.spm.usermanagementservice.domain.AgencyLicense;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.transaction.annotation.Transactional;

public interface AgencyLicenseRepository extends JpaRepository<AgencyLicense, Integer> {

    @Transactional(transactionManager = AgencyPersistenceConstants.AGENCY_TRANSACTION_MANAGER)
    void deleteAgencyLicenseByAgencyId(Integer agencyId);

}
