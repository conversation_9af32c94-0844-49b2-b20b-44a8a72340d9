/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : NotificationRepositoryImpl.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.repository;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.group.GroupBy;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.JPQLTemplates;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.querydsl.sql.SQLExpressions;
import com.querydsl.sql.SQLQuery;
import com.siemens.spm.common.constant.JpaConstants;
import com.siemens.spm.common.constant.NotificationConstants.ReadStatus;
import com.siemens.spm.common.security.SecurityUtils;
import com.siemens.spm.common.shared.domaintype.notification.NotificationType;
import com.siemens.spm.common.util.ListUtil;
import com.siemens.spm.usermanagementservice.common.DashboardConstants;
import com.siemens.spm.usermanagementservice.domain.Notification;
import com.siemens.spm.usermanagementservice.domain.QNotification;
import com.siemens.spm.usermanagementservice.domain.QNotificationMessage;
import com.siemens.spm.usermanagementservice.repository.filterdata.NotificationFilterDataVO;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
public class NotificationRepositoryImpl implements NotificationRepositoryCustom {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public void markAllUnreadNotificationsAsReadByUserIdAndAgencyId(Long userId, Integer agencyId) {
        if (userId == null) {
            log.error("Invalid user id");
            return;
        }

        BooleanBuilder whereClause = buildWhereClauseByUserIdAndAgencyIdAndUnreadStatus(userId, agencyId);

        QNotification qNotification = QNotification.notification;

        JPAQueryFactory queryFactory = new JPAQueryFactory(entityManager);

        queryFactory.update(qNotification)
                .where(whereClause)
                .set(qNotification.readStatus, ReadStatus.READ.getValue())
                .set(qNotification.lastModifiedAt, new Timestamp(System.currentTimeMillis()))
                .execute();
    }

    @Override
    public void changeReadStatusOfNotificationsByIdIn(Long userId, List<Long> notificationIds, String newReadStatus) {
        if (userId == null) {
            log.error("Could not update notification when user is unknown!");
            return;
        }

        var filterDataVO = NotificationFilterDataVO.builder()
                .userId(userId)
                .ids(notificationIds)
                .build();

        BooleanBuilder whereClause = buildWhereClauseByFilter(filterDataVO);

        QNotification qNotification = QNotification.notification;

        JPAQueryFactory queryFactory = new JPAQueryFactory(entityManager);

        queryFactory.update(qNotification)
                .where(whereClause)
                .set(qNotification.readStatus, newReadStatus)
                .set(qNotification.lastModifiedAt, new Timestamp(System.currentTimeMillis()))
                .execute();
    }

    @Override
    public void changeFlagStatusOfNotificationsByIdIn(Long userId, List<Long> notificationIds, String newFlagStatus) {
        if (userId == null) {
            log.error("Could not update notification when user is unknown!");
            return;
        }

        var filterDataVO = NotificationFilterDataVO.builder()
                .userId(userId)
                .ids(notificationIds)
                .build();

        BooleanBuilder whereClause = buildWhereClauseByFilter(filterDataVO);

        QNotification qNotification = QNotification.notification;

        JPAQueryFactory queryFactory = new JPAQueryFactory(entityManager);

        queryFactory.update(qNotification)
                .where(whereClause)
                .set(qNotification.flagStatus, newFlagStatus)
                .set(qNotification.lastModifiedAt, new Timestamp(System.currentTimeMillis()))
                .execute();
    }

    @Override
    public List<Notification> findPageByUserIdAndAgencyIdAndLatestUnread(Long userId,
                                                                         Integer agencyId,
                                                                         Integer page,
                                                                         Integer size) {
        page = (page == null) ? 0 : page;

        size = (size == null) ? JpaConstants.Size.DEFAULT_SIZE : size;
        size = size > JpaConstants.Size.MAX_SIZE ? JpaConstants.Size.MAX_SIZE : size;

        BooleanBuilder whereClause = buildWhereClauseByUserIdAndAgencyIdAndUnreadStatus(userId, agencyId);

        QNotification qNotification = QNotification.notification;

        var query = new JPAQuery<>(entityManager);

        return query.select(qNotification)
                .from(qNotification)
                .where(whereClause)
                .limit(size)
                .offset((long) page * size)
                .orderBy(qNotification.createdAt.desc())
                .fetch();
    }

    @Override
    public List<Notification> findAllByFilter(NotificationFilterDataVO filterDataVO) {
        BooleanBuilder whereClause = buildWhereClauseByFilter(filterDataVO);

        QNotification qNotification = QNotification.notification;
        var query = new JPAQuery<>(entityManager);

        return query.select(qNotification)
                .from(qNotification)
                .where(whereClause)
                .fetch();
    }

    @Override
    public Long countTotalByUserIdAndAgencyIdAndUnreadStatus(Long userId, Integer agencyId) {
        if (userId == null) {
            log.error("Could not count the number of notification when user is unknown!");
            return 0L;
        }

        BooleanBuilder whereClause = buildWhereClauseByUserIdAndAgencyIdAndUnreadStatus(userId, agencyId);

        QNotification qNotification = QNotification.notification;

        var query = new JPAQuery<>(entityManager);

        return query.from(qNotification)
                .where(whereClause)
                .fetchCount();
    }

    private BooleanBuilder buildWhereClauseByUserIdAndAgencyIdAndUnreadStatus(Long userId, Integer agencyId) {
        var filterDataVO = NotificationFilterDataVO.builder()
                .agencyId(agencyId)
                .userId(userId)
                .readStatus(ReadStatus.UNREAD.getValue())
                .build();

        return buildWhereClauseByFilter(filterDataVO);
    }

    @Transactional
    @Override
    public Map<String, Long> findTopIntersectionsByUserIdAndAgencyIdAndTypeIdAndUnreadCount(NotificationFilterDataVO filterDataVO,
                                                                                            Integer size) {
        if (size == null || size <= 0) {
            size = DashboardConstants.DEFAULT_NUMBER_OF_TOP_INTERSECTIONS_ON_DASHBOARD;
        }

        BooleanBuilder whereClause = buildWhereClauseByFilter(filterDataVO);

        QNotification qNotification = QNotification.notification;

        var query = new JPAQuery<>(entityManager, JPQLTemplates.DEFAULT);

        return query.from(qNotification)
                .where(whereClause)
                .groupBy(qNotification.intersectionId)
                .orderBy(qNotification.id.count().desc())
                .limit(size)
                .transform(GroupBy.groupBy(qNotification.intersectionId).as(qNotification.id.count()));
    }

    @Override
    public List<Notification> findPageByFilter(NotificationFilterDataVO filterDataVO,
                                               OrderSpecifier<String>[] orderSpecifiers,
                                               Integer page,
                                               Integer size) {
        page = (page == null) ? 0 : page;

        size = (size == null) ? JpaConstants.Size.DEFAULT_SIZE : size;
        size = size > JpaConstants.Size.MAX_SIZE ? JpaConstants.Size.MAX_SIZE : size;

        QNotification qNotification = QNotification.notification;

        BooleanBuilder whereClause = buildWhereClauseByFilter(filterDataVO);

        var query = new JPAQuery<>(entityManager);

        return query.select(qNotification)
                .from(qNotification)
                .where(whereClause)
                .limit(size)
                .offset((long) page * size)
                .orderBy(orderSpecifiers)
                .fetch();
    }

    @Override
    public long countTotalByFilter(NotificationFilterDataVO filterDataVO) {
        QNotification qNotification = QNotification.notification;

        BooleanBuilder whereClause = buildWhereClauseByFilter(filterDataVO);

        var query = new JPAQuery<>(entityManager);

        return query.from(qNotification)
                .where(whereClause)
                .fetchCount();
    }

    @Override
    public long cleanup(Date date, List<Long> userIds) {
        if (date == null) {
            return 0L;
        }

        QNotification qNotification = QNotification.notification;
        BooleanBuilder notificationClause = new BooleanBuilder();

        notificationClause.and(qNotification.lastModifiedAt.before(new Timestamp(date.getTime())));
        notificationClause.and(qNotification.readStatus.eq(ReadStatus.READ.getValue()));

        // Ignore release note notifications (Son Nguyen solution)
        notificationClause.and(qNotification.typeId.notIn(NotificationType.RELEASE_NOTE_NOTIFICATION.getId()));

        if (userIds != null && !userIds.isEmpty()) {
            notificationClause.and(qNotification.userId.in(userIds));
        }

        JPAQueryFactory queryFactory = new JPAQueryFactory(entityManager);

        List<Long> notiIds = queryFactory
                .select(qNotification.id)
                .from(qNotification)
                .where(notificationClause)
                .fetch();
        if (notiIds == null || notiIds.isEmpty()) {
            return 0L;
        }

        QNotificationMessage qNotificationMessage = QNotificationMessage.notificationMessage;
        BooleanBuilder notificationMessage = new BooleanBuilder();
        notificationMessage.and(qNotificationMessage.id.notificationId.in(notiIds));
        queryFactory.delete(qNotificationMessage).where(notificationMessage).execute();

        return queryFactory.delete(qNotification)
                .where(notificationClause)
                .execute();
    }

    private BooleanBuilder buildWhereClauseByFilter(NotificationFilterDataVO filterDataVO) {
        QNotification qNotification = QNotification.notification;
        BooleanBuilder booleanBuilder = new BooleanBuilder();

        // order predicates by cardinality (higher first)
        // refer to here for definition of cardinality:
        // https://en.wikipedia.org/wiki/Cardinality_(SQL_statements)

        boolean allowNullInt = filterDataVO.isAllowNullIntersection();

        List<Long> ids = filterDataVO.getIds();
        if (ListUtil.hasItem(ids)) {
            booleanBuilder.and(qNotification.id.in(ids));
        }

        List<String> excludeIntIds = filterDataVO.getExcludeIntIds();
        if (ListUtil.hasItem(excludeIntIds)) {
            BooleanExpression notIn = qNotification.intersectionId.notIn(excludeIntIds);
            if (allowNullInt) {
                booleanBuilder.and(notIn.or(qNotification.intersectionId.isNull()));
            } else {
                booleanBuilder.and(notIn);
            }
        }

        List<String> intIds = filterDataVO.getIntIds();
        if (ListUtil.hasItem(intIds)) {
            BooleanExpression in = qNotification.intersectionId.in(intIds);
            if (allowNullInt) {
                booleanBuilder.and(in.or(qNotification.intersectionId.isNull()));
            } else {
                booleanBuilder.and(in);
            }
        }

        String intersectionId = filterDataVO.getIntersectionId();
        if (StringUtils.hasText(intersectionId)) {
            booleanBuilder.and(qNotification.intersectionId.eq(intersectionId));
        }

        Long userId = filterDataVO.getUserId();
        if (userId != null) {
            booleanBuilder.and(qNotification.userId.eq(userId));
        }

        String intersection = filterDataVO.getIntersection();
        if (StringUtils.hasText(intersection)) {
            BooleanExpression intersectionBE = qNotification.intersectionId.likeIgnoreCase("%" + intersection + "%")
                    .or(qNotification.intersectionName.likeIgnoreCase("%" + intersection + "%"));

            booleanBuilder.and(intersectionBE);
        }

        Integer agencyId = filterDataVO.getAgencyId();
        if (agencyId != null) {
            // Retrieve all notifications that are:
            // 1. belong to the agency
            // 2. not belong to any agency (equal null)
            BooleanExpression agencyBE = qNotification.agencyId.isNull().or(qNotification.agencyId.eq(agencyId));
            booleanBuilder.and(agencyBE);
        }

        Timestamp createdAtFrom = filterDataVO.getCreatedAtFrom();
        Timestamp createdAtTo = filterDataVO.getCreatedAtTo();
        if (createdAtFrom != null && createdAtTo != null) {
            booleanBuilder.and(qNotification.createdAt.between(createdAtFrom, createdAtTo));
        }

        Timestamp alarmTimeUtcFrom = filterDataVO.getAlarmTimeUtcFrom();
        Timestamp alarmTimeUtcTo = filterDataVO.getAlarmTimeUtcTo();
        if (alarmTimeUtcFrom != null && alarmTimeUtcTo != null) {
            booleanBuilder.and(qNotification.alarmTimeUtc.between(alarmTimeUtcFrom, alarmTimeUtcTo));
        }

        String text = filterDataVO.getText();
        applyText(text, booleanBuilder);

        Long alarmCategoryId = filterDataVO.getAlarmCategoryId();
        if (alarmCategoryId != null) {
            booleanBuilder.and(qNotification.alarmCategoryId.eq(alarmCategoryId));
        }

        Long typeId = filterDataVO.getTypeId();
        if (typeId != null) {
            booleanBuilder.and(qNotification.typeId.eq(typeId));
        }

        List<Long> typeIds = filterDataVO.getTypeIds();
        if (ListUtil.hasItem(typeIds)) {
            booleanBuilder.and(qNotification.typeId.in(typeIds));
        }

        String readStatus = filterDataVO.getReadStatus();
        if (StringUtils.hasText(readStatus)) {
            booleanBuilder.and(qNotification.readStatus.eq(readStatus));
        }

        String flagStatus = filterDataVO.getFlagStatus();
        if (StringUtils.hasText(flagStatus)) {
            booleanBuilder.and(qNotification.flagStatus.eq(flagStatus));
        }

        return booleanBuilder;
    }

    private void applyText(String text, BooleanBuilder booleanBuilder) {
        if (!StringUtils.hasText(text)) {
            return;
        }

        QNotification qNotification = QNotification.notification;
        QNotificationMessage qNotificationMessage = QNotificationMessage.notificationMessage;

        BooleanExpression textBE = qNotificationMessage.name.likeIgnoreCase("%" + text + "%")
                .or(qNotificationMessage.description.likeIgnoreCase("%" + text + "%"));

        String currentLanguage = SecurityUtils.getCurrentUserLanguage();
        if (currentLanguage != null) {
            textBE.and(qNotificationMessage.id.language.eq(currentLanguage));
        }

        SQLQuery<Long> notificationIds = SQLExpressions.select(qNotificationMessage.id.notificationId)
                .from(qNotificationMessage)
                .where(textBE);

        booleanBuilder.and(qNotification.id.in(notificationIds));
    }

}
