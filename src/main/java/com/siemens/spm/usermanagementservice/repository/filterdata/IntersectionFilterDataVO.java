/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionFilterDataVO.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.repository.filterdata;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IntersectionFilterDataVO {

    private Integer agencyId;

    private Double[] bottomRight;

    private List<String> ids;

    private List<String> exclusionaryIds;

    private String status;

    private String text;

    private String name;

    private Double[] topLeft;

}
