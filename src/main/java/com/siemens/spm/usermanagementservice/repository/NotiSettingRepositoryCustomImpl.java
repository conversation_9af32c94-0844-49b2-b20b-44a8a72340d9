package com.siemens.spm.usermanagementservice.repository;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.dsl.StringPath;
import com.querydsl.jpa.impl.JPAQuery;
import com.siemens.spm.datahub.api.boundary.DataIntegrationService;
import com.siemens.spm.datahub.api.vo.response.AgencySchemaResponseVO;
import com.siemens.spm.usermanagementservice.domain.AlarmSummaryFrequency;
import com.siemens.spm.usermanagementservice.domain.NotiSetting;
import com.siemens.spm.usermanagementservice.domain.QNotiSetting;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.Collections;
import java.util.List;

@Slf4j
@Repository
public class NotiSettingRepositoryCustomImpl implements NotiSettingRepositoryCustom {

    @PersistenceContext
    private EntityManager entityManager;
    private final DataIntegrationService dataIntegrationService;

    public NotiSettingRepositoryCustomImpl(DataIntegrationService dataIntegrationService) {
        this.dataIntegrationService = dataIntegrationService;
    }

    @Override
    public List<NotiSetting> findForAlarmSummary(Integer agencyId) {
        Instant now = Instant.now();
        try {
            // First get agency schema to get timezoneId
            AgencySchemaResponseVO agencySchema = dataIntegrationService.getAgencySchema(agencyId);
            if (agencySchema == null || agencySchema.getTimezoneId() == null) {
                log.warn("No timezone found in agency schema for agency {}, using UTC", agencyId);
                return Collections.emptyList();
            }


            ZoneId zoneId = ZoneId.of(agencySchema.getTimezoneId());
            LocalDateTime agencyTime = LocalDateTime.ofInstant(now, zoneId);

            if (agencyTime.getHour() != 0) {
                return Collections.emptyList();
            }
            // Find notiSettings that need to send alarm summary
            QNotiSetting qNotiSetting = QNotiSetting.notiSetting;
            List<NotiSetting> notiSettings = new JPAQuery<>(entityManager)
                    .select(qNotiSetting)
                    .from(qNotiSetting)
                    .where(qNotiSetting.agencyUserSetting.agencyId.eq(agencyId)
                            .and(qNotiSetting.alarmSummaryEnabled.isTrue())
                            .and(buildDateCondition(agencyTime))
                    )
                    .fetch();

            // Need to update alarmSummaryTime before returning
            notiSettings.forEach(notiSetting -> notiSetting.setAlarmToDate(agencyTime.toLocalDate().minusDays(1)));

            return notiSettings;
        } catch (Exception e) {
            log.error("Error getting timezone information for agency {}: {}", agencyId, e.getMessage());
            return Collections.emptyList();
        }
    }

    private BooleanBuilder buildDateCondition(LocalDateTime agencyTime) {
        QNotiSetting qNotiSetting = QNotiSetting.notiSetting;
        StringPath alarmSummaryFrequency = qNotiSetting.alarmSummaryFrequency;

        BooleanBuilder builder = new BooleanBuilder();

        if (agencyTime.getDayOfWeek() == DayOfWeek.MONDAY) {
            builder.or(alarmSummaryFrequency.eq(AlarmSummaryFrequency.WEEKLY.getValue()));
        }

        if (agencyTime.getDayOfMonth() == 1) {
            builder.or(alarmSummaryFrequency.eq(AlarmSummaryFrequency.MONTHLY.getValue()));
        }

        return builder;
    }

    private ZoneId getZoneId(String zoneOffset) {
        if (zoneOffset == null) {
            return ZoneOffset.UTC;
        }
        try {
            return ZoneOffset.of(zoneOffset);
        } catch (Exception e) {
            log.error("Invalid zone offset: {}, using UTC instead", zoneOffset, e);
            return ZoneOffset.UTC;
        }
    }

}
