/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : UserRepositoryCustom.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.repository;

import java.util.Date;
import java.util.List;

import com.siemens.spm.usermanagementservice.domain.User;

/**
 * Spring Data JPA repository for the User entity.
 */
public interface UserRepositoryCustom {

    /**
     * Find all users who have user_key_expiration in a period of time
     *
     * @param dateFrom
     * @param dateTo
     * @return List<User>
     */
    List<User> findAllByUserKeyExpirationBetween(Date dateFrom, Date dateTo);

    List<Long> findAllUserIds();

}
