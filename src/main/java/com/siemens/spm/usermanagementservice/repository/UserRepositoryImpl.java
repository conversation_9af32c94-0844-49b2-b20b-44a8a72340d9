/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : UserRepositoryImpl.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.repository;

import java.util.Date;
import java.util.List;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.siemens.spm.usermanagementservice.domain.QUser;
import com.siemens.spm.usermanagementservice.domain.User;

public class UserRepositoryImpl implements UserRepositoryCustom {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public List<User> findAllByUserKeyExpirationBetween(Date dateFrom, Date dateTo) {
        QUser qUser = QUser.user;

        BooleanBuilder whereClause = new BooleanBuilder();

        JPAQuery<?> query = new JPAQuery<>(entityManager);

        return query.select(qUser)
                .from(qUser)
                .where(whereClause)
                .fetch();
    }

    @Override
    public List<Long> findAllUserIds() {
        QUser qUser = QUser.user;

        JPAQueryFactory queryFactory = new JPAQueryFactory(entityManager);
        return queryFactory.select(qUser.id)
                .from(qUser)
                .fetch();
    }

}
