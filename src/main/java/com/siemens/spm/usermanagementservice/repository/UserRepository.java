/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : UserRepository.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.repository;

import com.siemens.spm.usermanagementservice.domain.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Spring Data JPA repository for the User entity.
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long>, UserRepositoryCustom {

    Optional<User> findByIdAndEmail(Long id, String email);

    Optional<User> findByEmail(String email);

}
