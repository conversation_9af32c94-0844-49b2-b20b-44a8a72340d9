/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : CorridorRepository.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.repository;

import com.siemens.spm.common.agency.utils.AgencyPersistenceConstants;
import com.siemens.spm.usermanagementservice.domain.Corridor;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * Spring Data JPA repository for the Corridor entity.
 */
@Repository
public interface CorridorRepository extends JpaRepository<Corridor, String>, CorridorRepositoryCustom {

    boolean existsByName(String name);

    @Transactional(transactionManager = AgencyPersistenceConstants.AGENCY_TRANSACTION_MANAGER)
    void deleteByAgencyId(Integer agencyId);

}
