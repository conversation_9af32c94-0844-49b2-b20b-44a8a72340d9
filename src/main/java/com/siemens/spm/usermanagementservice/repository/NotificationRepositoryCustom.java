/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : NotificationRepositoryCustom.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.querydsl.core.types.OrderSpecifier;
import com.siemens.spm.usermanagementservice.domain.Notification;
import com.siemens.spm.usermanagementservice.repository.filterdata.NotificationFilterDataVO;

public interface NotificationRepositoryCustom {

    void markAllUnreadNotificationsAsReadByUserIdAndAgencyId(Long userId, Integer agencyId);

    void changeReadStatusOfNotificationsByIdIn(Long userId, List<Long> notificationIds, String newReadStatus);

    void changeFlagStatusOfNotificationsByIdIn(Long userId, List<Long> notificationIds, String newFlagStatus);

    List<Notification> findPageByUserIdAndAgencyIdAndLatestUnread(Long userId,
                                                                  Integer agencyId,
                                                                  Integer page,
                                                                  Integer size);

    List<Notification> findAllByFilter(NotificationFilterDataVO filterDataVO);

    Long countTotalByUserIdAndAgencyIdAndUnreadStatus(Long userId, Integer agencyId);

    Map<String, Long> findTopIntersectionsByUserIdAndAgencyIdAndTypeIdAndUnreadCount(NotificationFilterDataVO filterDataVO,
                                                                                     Integer size);

    List<Notification> findPageByFilter(NotificationFilterDataVO filterDataVO,
                                        OrderSpecifier<String>[] orderSpecifiers,
                                        Integer page,
                                        Integer size);

    long countTotalByFilter(NotificationFilterDataVO filterDataVO);

    /**
     * Delete all notifications(exclude release note) of users before specific date
     *
     * @param date    the date need to delete notifications
     * @param userIds If null or empty, clean up notifications of all users
     * @return number of effect row (notifications was deleted)
     */
    long cleanup(Date date, List<Long> userIds);

}
