/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : ChangeTypeEnum.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.common;

public enum ChangeTypeEnum {

    CREATE,
    UPDATE,
    DELETE;

    public static ChangeTypeEnum fromIgnoreCaseString(String ignoreCaseString) {
        if (ignoreCaseString == null) {
            throw new IllegalArgumentException("Change type cannot be null");
        }
        return ChangeTypeEnum.valueOf(ignoreCaseString.toUpperCase());
    }
}
