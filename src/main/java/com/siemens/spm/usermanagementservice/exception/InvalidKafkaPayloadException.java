/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : InvalidKafkaPayloadException.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.exception;

import org.springframework.kafka.KafkaException;

public class InvalidKafkaPayloadException extends KafkaException {

    private final String payload;

    public InvalidKafkaPayloadException(String message, String payload) {
        super(message);
        this.payload = payload;
    }

    public InvalidKafkaPayloadException(String message, Throwable cause, String payload) {
        super(message, cause);
        this.payload = payload;
    }
}
