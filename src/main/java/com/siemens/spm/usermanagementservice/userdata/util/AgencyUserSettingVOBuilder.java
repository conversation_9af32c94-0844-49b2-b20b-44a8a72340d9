package com.siemens.spm.usermanagementservice.userdata.util;

import com.siemens.spm.usermanagementservice.api.vo.AgencyUserSettingVO;
import com.siemens.spm.usermanagementservice.api.vo.DashboardSettingVO;
import com.siemens.spm.usermanagementservice.domain.AgencyUserSetting;
import com.siemens.spm.usermanagementservice.domain.DashboardSetting;
import com.siemens.spm.usermanagementservice.userdata.util.dashboard.DashboardSettingFactory;
import com.siemens.spm.usermanagementservice.userdata.util.dashboard.DashboardSettingVOBuilder;

/**
 * <AUTHOR> <PERSON> - <EMAIL>
 */
public final class AgencyUserSettingVOBuilder {

    private AgencyUserSettingVOBuilder() {
    }

    /**
     * Build {@link AgencyUserSettingVO} object data
     *
     * @param entity entity need to build vo object
     * @return {@link  AgencyUserSettingVO}
     * @throws IllegalArgumentException if entity or entity's id is {@code null}
     */
    public static AgencyUserSettingVO toVO(AgencyUserSetting entity) {
        if (entity == null || entity.getId() == null) {
            throw new IllegalArgumentException();
        }

        // Build dashboard setting
        DashboardSetting dashboardSetting = entity.getDashboardSetting();
        DashboardSettingVO dashboardSettingVO = dashboardSetting != null
                ? DashboardSettingVOBuilder.buildVO(dashboardSetting)
                : null;

        return AgencyUserSettingVO.builder()
                .userId(entity.getUserId())
                .agencyId(entity.getAgencyId())
                .dashboardSettingVO(dashboardSettingVO)
                .dashboardMetadataVO(DashboardSettingFactory.metadataVO())
                .build();
    }

    /**
     * Build {@link AgencyUserSettingVO} object data
     *
     * @param entity entity need to build vo object
     * @return {@link  AgencyUserSettingVO}
     * @throws IllegalArgumentException if entity or entity's id is {@code null}
     */
    public static AgencyUserSettingVO toVO(AgencyUserSetting entity, String agencyTimeZoneId) {
        if (entity == null || entity.getId() == null) {
            throw new IllegalArgumentException();
        }

        // Build dashboard setting
        DashboardSetting dashboardSetting = entity.getDashboardSetting();
        DashboardSettingVO dashboardSettingVO = dashboardSetting != null
                ? DashboardSettingVOBuilder.buildVO(dashboardSetting, agencyTimeZoneId)
                : null;

        return AgencyUserSettingVO.builder()
                .userId(entity.getUserId())
                .agencyId(entity.getAgencyId())
                .dashboardSettingVO(dashboardSettingVO)
                .dashboardMetadataVO(DashboardSettingFactory.metadataVO())
                .build();
    }
}
