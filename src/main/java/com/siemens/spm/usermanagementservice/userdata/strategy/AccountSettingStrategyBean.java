package com.siemens.spm.usermanagementservice.userdata.strategy;

import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.siemens.spm.common.security.SecurityUtils;
import com.siemens.spm.common.util.BeanFinder;
import com.siemens.spm.datahub.api.boundary.DataIntegrationService;
import com.siemens.spm.datahub.api.vo.response.AgencySchemaResponseVO;
import com.siemens.spm.datahub.exception.DataHubException;
import com.siemens.spm.spmstudiosdk.dto.StudioUserDto;
import com.siemens.spm.spmstudiosdk.exception.StudioException;
import com.siemens.spm.spmstudiosdk.service.StudioUserService;
import com.siemens.spm.usermanagementservice.api.constant.WidgetIds;
import com.siemens.spm.usermanagementservice.api.vo.AbstractWidgetVO;
import com.siemens.spm.usermanagementservice.api.vo.AgencyUserSettingMetadataVO;
import com.siemens.spm.usermanagementservice.api.vo.AgencyUserSettingVO;
import com.siemens.spm.usermanagementservice.api.vo.DashboardSettingVO;
import com.siemens.spm.usermanagementservice.api.vo.MapViewWidgetVO;
import com.siemens.spm.usermanagementservice.api.vo.NotiSettingVO;
import com.siemens.spm.usermanagementservice.api.vo.NotificationWidgetVO;
import com.siemens.spm.usermanagementservice.api.vo.OpenAlarmWidgetVO;
import com.siemens.spm.usermanagementservice.api.vo.SummaryReportWidgetVO;
import com.siemens.spm.usermanagementservice.api.vo.TopIntOpenAlarmWidgetVO;
import com.siemens.spm.usermanagementservice.api.vo.VolumeReportWidgetVO;
import com.siemens.spm.usermanagementservice.api.vo.request.UpdateNotiSettingRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.UpdateWidgetsSettingRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.AgencyUserSettingMetadataResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.AgencyUserSettingResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.AgencyUserSettingResultObject.AgencyUserSettingStatusCode;
import com.siemens.spm.usermanagementservice.api.vo.response.NotiSettingResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.NotiSettingUpdatedResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.WidgetsSettingUpdatedResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.WidgetsSettingUpdatedResultObject.WidgetSettingStatusCode;
import com.siemens.spm.usermanagementservice.domain.AgencySettings;
import com.siemens.spm.usermanagementservice.domain.AgencyUserSetting;
import com.siemens.spm.usermanagementservice.domain.AlarmSummaryFrequency;
import com.siemens.spm.usermanagementservice.domain.DashboardSetting;
import com.siemens.spm.usermanagementservice.domain.NotiSetting;
import com.siemens.spm.usermanagementservice.repository.AgencySettingsRepository;
import com.siemens.spm.usermanagementservice.repository.AgencyUserSettingRepository;
import com.siemens.spm.usermanagementservice.repository.DashboardSettingRepository;
import com.siemens.spm.usermanagementservice.repository.NotiSettingRepository;
import com.siemens.spm.usermanagementservice.userdata.util.AgencyUserSettingVOBuilder;
import com.siemens.spm.usermanagementservice.userdata.util.NotiSettingFactory;
import com.siemens.spm.usermanagementservice.userdata.util.NotiSettingVOBuilder;
import com.siemens.spm.usermanagementservice.userdata.util.dashboard.DashboardSettingFactory;
import com.siemens.spm.usermanagementservice.userdata.util.dashboard.DashboardSettingVOBuilder;
import com.siemens.spm.usermanagementservice.userdata.util.dashboard.DashboardValidatorUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> Nguyen - <EMAIL>
 */
@Slf4j
@Service
@Transactional
public class AccountSettingStrategyBean implements AccountSettingStrategy {

    @Autowired
    private AgencyUserSettingRepository agencyUserSettingRepository;

    @Autowired
    private AgencySettingsRepository agencySettingsRepository;

    @Autowired
    private DashboardSettingRepository dashboardSettingRepository;

    @Autowired
    private NotiSettingRepository notiSettingRepository;

    @Autowired
    private StudioUserService studioUserService;

    @Autowired
    private DataIntegrationService dataIntegrationService;

    private static final String STUDIO_API_ERROR_MSG = "Error when get user from Studio";

    @Override
    public AgencyUserSettingResultObject getUserSetting(Integer agencyId) throws DataHubException {
        if (agencyId == null) {
            return new AgencyUserSettingResultObject(AgencyUserSettingStatusCode.INVALID_AGENCY_ID);
        }

        // First get agency schema to get timezoneId
        AgencySchemaResponseVO agencySchema = dataIntegrationService.getAgencySchema(agencyId);
        if (agencySchema == null || agencySchema.getTimezoneId() == null) {
            log.warn("No timezone found in agency schema for agency {}", agencyId);
            return new AgencyUserSettingResultObject(null,
                    AgencyUserSettingResultObject.AgencyUserSettingStatusCode.AGENCY_TIMEZONE_NOT_FOUND);
        }

        String userEmail = SecurityUtils.getCurrentUserEmail();
        Optional<StudioUserDto> userDtoOptional = fetchStudioUser(userEmail);
        if (userDtoOptional.isEmpty()) {
            return new AgencyUserSettingResultObject(AgencyUserSettingStatusCode.UNKNOWN_ERROR);
        }

        Integer currentUserId = userDtoOptional.get().getId();

        // Init default setting if not found (for old users and users who have permission on all agencies)
        AgencyUserSetting agencyUserSetting = findOrInitDefaultAgencyUserSetting(agencyId, Long.valueOf(currentUserId));

        // Build VO object to return
        AgencyUserSettingVO agencyUserSettingVO = AgencyUserSettingVOBuilder.toVO(agencyUserSetting, agencySchema.getTimezoneId());

        return new AgencyUserSettingResultObject(agencyUserSettingVO);
    }

    @Override
    public AgencyUserSetting findOrInitDefaultAgencyUserSetting(Integer agencyId, Long userId) {
        if (agencyId == null || userId == null) {
            throw new IllegalArgumentException();
        }

        if (agencyUserSettingRepository.existsByAgencyIdAndUserId(agencyId, userId)) {
            log.debug("Find exist agency user setting, agencyId={}, userId={}", agencyId, userId);

            return agencyUserSettingRepository.findByAgencyIdAndUserId(agencyId, userId);
        }

        log.debug("Init default agency user setting, agencyId={}, userId={}", agencyId, userId);

        AgencySettings agencySettings = agencySettingsRepository.findById(agencyId).orElse(null);
        AgencyUserSetting agencyUserSetting = AgencyUserSetting.builder()
                .userId(userId)
                .agencyId(agencyId)
                .build();

        agencyUserSetting = agencyUserSettingRepository.save(agencyUserSetting);

        initAndSaveDefaultDashboardSetting(agencyUserSetting, agencySettings);
        initAndSaveDefaultEmailNotificationSetting(agencyUserSetting);

        return agencyUserSetting;
    }

    private void initAndSaveDefaultDashboardSetting(AgencyUserSetting agencyUserSetting,
                                                    AgencySettings agencySettings) {
        DashboardSetting dashboardSetting = DashboardSettingFactory.defaultEntity(agencySettings);
        dashboardSetting.setAgencyUserSetting(agencyUserSetting);
        dashboardSetting = dashboardSettingRepository.save(dashboardSetting);

        agencyUserSetting.setDashboardSetting(dashboardSetting);
    }

    private void initAndSaveDefaultEmailNotificationSetting(AgencyUserSetting agencyUserSetting) {
        NotiSetting notiSetting = NotiSettingFactory.defaultEntity();
        notiSetting.setAgencyUserSetting(agencyUserSetting);

        notiSetting = notiSettingRepository.save(notiSetting);

        agencyUserSetting.setNotiSetting(notiSetting);
    }

    @Transactional
    @Override
    public WidgetsSettingUpdatedResultObject updateWidgetsSetting(UpdateWidgetsSettingRequestVO requestVO,
                                                                  boolean updateAll) {
        if (requestVO == null) {
            throw new IllegalArgumentException("request can not be null");
        }

        // Detect & validate agencyId. Use provided agencyId if user is SIEMENS_ADMIN. Otherwise, resolve current
        // agency id from security context
        Integer agencyId = requestVO.getAgencyId();
        if (agencyId == null) {
            log.error("Invalid agency id: " + requestVO.getAgencyId());

            return new WidgetsSettingUpdatedResultObject(WidgetSettingStatusCode.INVALID_AGENCY_ID);
        }

        String userEmail = SecurityUtils.getCurrentUserEmail();
        Optional<StudioUserDto> userDtoOptional = fetchStudioUser(userEmail);
        if (userDtoOptional.isEmpty()) {
            return new WidgetsSettingUpdatedResultObject(WidgetSettingStatusCode.UNKNOWN_ERROR);
        }

        Long currentUserId = Long.valueOf(userDtoOptional.get().getId());

        // Init default setting if not found (for old users and users who have permission on all agencies)
        AgencyUserSetting agencyUserSetting = findOrInitDefaultAgencyUserSetting(agencyId, currentUserId);
        DashboardSetting dashboardSetting = agencyUserSetting.getDashboardSetting();

        // Do update
        WidgetsSettingUpdatedResultObject resultObject;
        if (updateAll) {
            resultObject = doUpdateAllWidgets(dashboardSetting, requestVO);
        } else {
            resultObject = doUpdateSpecificWidget(dashboardSetting, requestVO.getAbstractWidgetVO());
        }
        if (resultObject != null) {
            return resultObject;
        }

        // Do persist to DB
        dashboardSetting = dashboardSettingRepository.save(dashboardSetting);

        // Do build VO and return
        DashboardSettingVO dashboardSettingVO = DashboardSettingVOBuilder.buildVO(dashboardSetting);
        resultObject = new WidgetsSettingUpdatedResultObject(dashboardSettingVO);

        return resultObject;
    }

    @Override
    public AgencyUserSettingMetadataResultObject getUserSettingMetadata(Integer agencyId) {
        if (agencyId == null) {
            return new AgencyUserSettingMetadataResultObject(AgencyUserSettingStatusCode.INVALID_AGENCY_ID);
        }

        Optional<StudioUserDto> userDtoOptional;
        try {
            userDtoOptional = studioUserService.findByEmail(SecurityUtils.getCurrentUserEmail());
        } catch (StudioException e) {
            log.error(STUDIO_API_ERROR_MSG, e);
            return new AgencyUserSettingMetadataResultObject(AgencyUserSettingStatusCode.UNKNOWN_ERROR);
        }
        if (userDtoOptional.isEmpty()) {
            return new AgencyUserSettingMetadataResultObject(AgencyUserSettingStatusCode.USER_NOT_FOUND);
        }
        Long userId = Long.valueOf(userDtoOptional.get().getId());

        AgencyUserSettingMetadataVO settingMetadataVO = AgencyUserSettingMetadataVO.builder()
                .userId(userId)
                .agencyId(agencyId)
                .dashboardMetadataVO(DashboardSettingFactory.metadataVO())
                .build();

        return AgencyUserSettingMetadataResultObject.success(settingMetadataVO);
    }

    @Override
    public NotiSettingUpdatedResultObject updateNotiSetting(UpdateNotiSettingRequestVO requestVO) {
        // Currently, users have permission on all agencies can not update alarm summary setting.
        // This can change later for other noti settings
        Integer agencyId = requestVO.getAgencyId();

        boolean alarmSummaryEnabled = requestVO.isAlarmSummaryEnabled();
        AlarmSummaryFrequency alarmSummaryFrequency = AlarmSummaryFrequency
                .resolve(requestVO.getAlarmSummaryFrequency())
                .orElse(null);
        if (alarmSummaryEnabled && alarmSummaryFrequency == null) {
            return new NotiSettingUpdatedResultObject(null,
                    NotiSettingUpdatedResultObject.StatusCode.INVALID_ALARM_SUMMARY_SETTING);
        }

        Optional<StudioUserDto> userDtoOptional;
        try {
            userDtoOptional = studioUserService.findByEmail(SecurityUtils.getCurrentUserEmail());
        } catch (StudioException e) {
            log.error(STUDIO_API_ERROR_MSG, e);
            return new NotiSettingUpdatedResultObject(NotiSettingUpdatedResultObject.StatusCode.ERROR);
        }
        if (userDtoOptional.isEmpty()) {
            return new NotiSettingUpdatedResultObject(NotiSettingUpdatedResultObject.StatusCode.USER_NOT_FOUND);
        }
        Long userId = Long.valueOf(userDtoOptional.get().getId());

        AgencyUserSetting agencyUserSetting = findOrInitDefaultAgencyUserSetting(agencyId, userId);
        NotiSetting notiSetting = agencyUserSetting.getNotiSetting();
        if (notiSetting == null) {
            notiSetting = NotiSettingFactory.defaultEntity();
            agencyUserSetting.setNotiSetting(notiSetting);
            notiSetting.setAgencyUserSetting(agencyUserSetting);
        }

        if (!alarmSummaryEnabled) {
            notiSetting.setAlarmSummaryEnabled(false);
            notiSetting.setAlarmSummaryFrequency(null);
        } else {
            notiSetting.setAlarmSummaryEnabled(true);
            notiSetting.setAlarmSummaryFrequency(alarmSummaryFrequency.getValue());
        }

        notiSettingRepository.save(notiSetting);

        return new NotiSettingUpdatedResultObject(null, NotiSettingUpdatedResultObject.StatusCode.SUCCESS);
    }

    @Override
    public NotiSettingResultObject getNotiSetting(Integer agencyId) {
        if (agencyId == null) {
            return new NotiSettingResultObject(AgencyUserSettingStatusCode.INVALID_AGENCY_ID);
        }

        Optional<StudioUserDto> userDtoOptional;
        try {
            userDtoOptional = studioUserService.findByEmail(SecurityUtils.getCurrentUserEmail());
        } catch (StudioException e) {
            log.error(STUDIO_API_ERROR_MSG, e);
            return new NotiSettingResultObject(AgencyUserSettingStatusCode.UNKNOWN_ERROR);
        }
        if (userDtoOptional.isEmpty()) {
            return new NotiSettingResultObject(AgencyUserSettingStatusCode.USER_NOT_FOUND);
        }
        Long userId = Long.valueOf(userDtoOptional.get().getId());

        // Init default setting if not found (for old users and users who have permission on all agencies)
        AgencyUserSetting agencyUserSetting = findOrInitDefaultAgencyUserSetting(agencyId, userId);
        NotiSetting notiSetting = agencyUserSetting.getNotiSetting();
        if (notiSetting == null) {
            notiSetting = NotiSettingFactory.defaultEntity();
            notiSetting.setAgencyUserSetting(agencyUserSetting);
            notiSetting = notiSettingRepository.save(notiSetting);
        }

        NotiSettingVO notiSettingVO = NotiSettingVOBuilder.buildVO(notiSetting);

        return new NotiSettingResultObject(notiSettingVO, AgencyUserSettingStatusCode.SUCCESS);
    }

    /**
     * Do update a specific widget's setting to dashboard's setting
     *
     * @param dashboardSetting target dashboard's setting need to update
     * @param abstractWidgetVO data of widget's setting need to update
     * @return {@code null} if data is valid and updated to dashboard's setting. Otherwise, return
     * {@link  WidgetsSettingUpdatedResultObject} with error code.
     */
    private WidgetsSettingUpdatedResultObject doUpdateSpecificWidget(DashboardSetting dashboardSetting,
                                                                     AbstractWidgetVO abstractWidgetVO) {
        if (abstractWidgetVO == null) {
            return new WidgetsSettingUpdatedResultObject(WidgetSettingStatusCode.INVALID_WIDGET);
        }

        String widgetId = abstractWidgetVO.getWidgetId();
        if (widgetId == null) {
            return new WidgetsSettingUpdatedResultObject(WidgetSettingStatusCode.INVALID_WIDGET);
        }

        return switch (widgetId) {
            case WidgetIds.MAP_VIEW -> updateMapView(dashboardSetting, (MapViewWidgetVO) abstractWidgetVO);
            case WidgetIds.OPEN_ALARM -> updateOpenAlarm(dashboardSetting, (OpenAlarmWidgetVO) abstractWidgetVO);
            case WidgetIds.TOP_INT_OPEN_ALARM ->
                    updateTopIntOpenAlarm(dashboardSetting, (TopIntOpenAlarmWidgetVO) abstractWidgetVO);
            case WidgetIds.SUMMARY_REPORT ->
                    updateSummaryReport(dashboardSetting, (SummaryReportWidgetVO) abstractWidgetVO);
            case WidgetIds.VOLUME_REPORT ->
                    updateVolumeReport(dashboardSetting, (VolumeReportWidgetVO) abstractWidgetVO);
            case WidgetIds.NOTIFICATION ->
                    updateNotification(dashboardSetting, (NotificationWidgetVO) abstractWidgetVO);
            default -> new WidgetsSettingUpdatedResultObject(WidgetSettingStatusCode.INVALID_WIDGET);
        };
    }

    private WidgetsSettingUpdatedResultObject doUpdateAllWidgets(DashboardSetting dashboardSetting,
                                                                 UpdateWidgetsSettingRequestVO requestVO) {
        WidgetsSettingUpdatedResultObject resultObject;

        MapViewWidgetVO mapViewWidgetVO = requestVO.getMapViewWidgetVO();
        if ((resultObject = updateMapView(dashboardSetting, mapViewWidgetVO)) != null) {
            return resultObject;
        }

        OpenAlarmWidgetVO openAlarmWidgetVO = requestVO.getOpenAlarmWidgetVO();
        if ((resultObject = updateOpenAlarm(dashboardSetting, openAlarmWidgetVO)) != null) {
            return resultObject;
        }

        TopIntOpenAlarmWidgetVO topIntOpenAlarmWidgetVO = requestVO.getTopIntOpenAlarmWidgetVO();
        if ((resultObject = updateTopIntOpenAlarm(dashboardSetting, topIntOpenAlarmWidgetVO)) != null) {
            return resultObject;
        }

        SummaryReportWidgetVO summaryReportWidgetVO = requestVO.getSummaryReportWidgetVO();
        if ((resultObject = updateSummaryReport(dashboardSetting, summaryReportWidgetVO)) != null) {
            return resultObject;
        }

        VolumeReportWidgetVO volumeReportWidgetVO = requestVO.getVolumeReportWidgetVO();
        if ((resultObject = updateVolumeReport(dashboardSetting, volumeReportWidgetVO)) != null) {
            return resultObject;
        }

        NotificationWidgetVO notificationWidgetVO = requestVO.getNotificationWidgetVO();
        if ((resultObject = updateNotification(dashboardSetting, notificationWidgetVO)) != null) {
            return resultObject;
        }

        return null;
    }

    private WidgetsSettingUpdatedResultObject updateMapView(DashboardSetting dashboardSetting,
                                                            MapViewWidgetVO mapViewWidgetVO) {
        if (mapViewWidgetVO == null) {
            dashboardSetting.setMapViewWidgetJson(null);
            return null;
        }

        if (!DashboardValidatorUtil.isValidMapViewWidget(mapViewWidgetVO)) {
            return new WidgetsSettingUpdatedResultObject(WidgetSettingStatusCode.INVALID_MAPVIEW);
        }

        String mapViewWidgetJson;
        try {
            mapViewWidgetJson = BeanFinder.getDefaultObjectMapper().writeValueAsString(mapViewWidgetVO);
        } catch (JsonProcessingException e) {
            log.error("Error when convert MapViewWidgetVO to json", e);

            return new WidgetsSettingUpdatedResultObject(WidgetSettingStatusCode.INVALID_MAPVIEW);
        }

        dashboardSetting.setMapViewWidgetJson(mapViewWidgetJson);
        return null;
    }

    private WidgetsSettingUpdatedResultObject updateOpenAlarm(DashboardSetting dashboardSetting,
                                                              OpenAlarmWidgetVO openAlarmWidget) {
        if (openAlarmWidget == null) {
            dashboardSetting.setOpenAlarmWidgetJson(null);
            return null;
        }

        if (!DashboardValidatorUtil.isValidOpenAlarmWidget(openAlarmWidget)) {
            return new WidgetsSettingUpdatedResultObject(WidgetSettingStatusCode.INVALID_OPEN_ALARM);
        }

        String openAlarmWidgetJson;
        try {
            openAlarmWidgetJson = BeanFinder.getDefaultObjectMapper().writeValueAsString(openAlarmWidget);
        } catch (JsonProcessingException e) {
            log.error("Error when convert OpenAlarmWidgetVO to json", e);

            return new WidgetsSettingUpdatedResultObject(WidgetSettingStatusCode.INVALID_OPEN_ALARM);
        }

        dashboardSetting.setOpenAlarmWidgetJson(openAlarmWidgetJson);
        return null;
    }

    private WidgetsSettingUpdatedResultObject updateTopIntOpenAlarm(DashboardSetting dashboardSetting,
                                                                    TopIntOpenAlarmWidgetVO topIntOpenAlarmWidgetVO) {
        if (topIntOpenAlarmWidgetVO == null) {
            dashboardSetting.setTopIntOpenAlarmWidgetJson(null);
            return null;
        }

        if (!DashboardValidatorUtil.isValidTopIntOpenAlarmWidget(topIntOpenAlarmWidgetVO)) {
            return new WidgetsSettingUpdatedResultObject(WidgetSettingStatusCode.INVALID_TOP_INT_OPEN_ALARM);
        }

        String topIntOpenAlarmWidgetJson;
        try {
            topIntOpenAlarmWidgetJson = BeanFinder.getDefaultObjectMapper().writeValueAsString(topIntOpenAlarmWidgetVO);
        } catch (JsonProcessingException e) {
            log.error("Error when convert TopIntOpenAlarmWidgetVO to json", e);

            return new WidgetsSettingUpdatedResultObject(WidgetSettingStatusCode.INVALID_TOP_INT_OPEN_ALARM);
        }

        dashboardSetting.setTopIntOpenAlarmWidgetJson(topIntOpenAlarmWidgetJson);
        return null;
    }

    private WidgetsSettingUpdatedResultObject updateNotification(DashboardSetting dashboardSetting,
                                                                 NotificationWidgetVO notificationWidgetVO) {
        if (notificationWidgetVO == null) {
            dashboardSetting.setNotificationWidgetJson(null);
            return null;
        }

        if (!DashboardValidatorUtil.isValidNotificationWidget(notificationWidgetVO)) {
            return new WidgetsSettingUpdatedResultObject(WidgetSettingStatusCode.INVALID_NOTIFICATION);
        }

        String notificationWidgetJson;
        try {
            notificationWidgetJson = BeanFinder.getDefaultObjectMapper().writeValueAsString(notificationWidgetVO);
        } catch (JsonProcessingException e) {
            log.error("Error when convert NotificationWidgetVO to json", e);

            return new WidgetsSettingUpdatedResultObject(WidgetSettingStatusCode.INVALID_NOTIFICATION);
        }

        dashboardSetting.setNotificationWidgetJson(notificationWidgetJson);
        return null;
    }

    private WidgetsSettingUpdatedResultObject updateVolumeReport(DashboardSetting dashboardSetting,
                                                                 VolumeReportWidgetVO volumeReportWidgetVO) {
        if (volumeReportWidgetVO == null) {
            dashboardSetting.setVolumeReportWidgetJson(null);
            return null;
        }

        if (!DashboardValidatorUtil.isValidVolumeReportWidget(volumeReportWidgetVO)) {
            return new WidgetsSettingUpdatedResultObject(WidgetSettingStatusCode.INVALID_VOLUME_REPORT);
        }

        String volumeReportWidgetVOJson;
        try {
            volumeReportWidgetVOJson = BeanFinder.getDefaultObjectMapper().writeValueAsString(volumeReportWidgetVO);
        } catch (JsonProcessingException e) {
            log.error("Error when convert VolumeReportWidgetVO to json", e);

            return new WidgetsSettingUpdatedResultObject(WidgetSettingStatusCode.INVALID_VOLUME_REPORT);
        }

        dashboardSetting.setVolumeReportWidgetJson(volumeReportWidgetVOJson);
        return null;
    }

    private WidgetsSettingUpdatedResultObject updateSummaryReport(DashboardSetting dashboardSetting,
                                                                  SummaryReportWidgetVO summaryReportWidgetVO) {
        if (summaryReportWidgetVO == null) {
            dashboardSetting.setSummaryReportWidgetJson(null);
            return null;
        }

        if (!DashboardValidatorUtil.isValidSummaryReportWidget(summaryReportWidgetVO)) {
            return new WidgetsSettingUpdatedResultObject(WidgetSettingStatusCode.INVALID_SUMMARY_REPORT);
        }

        String summaryReportWidgetJson;
        try {
            summaryReportWidgetJson = BeanFinder.getDefaultObjectMapper().writeValueAsString(summaryReportWidgetVO);
        } catch (JsonProcessingException e) {
            log.error("Error when convert SummaryReportWidgetVO to json", e);

            return new WidgetsSettingUpdatedResultObject(WidgetSettingStatusCode.INVALID_SUMMARY_REPORT);
        }

        dashboardSetting.setSummaryReportWidgetJson(summaryReportWidgetJson);
        return null;
    }

    private Optional<StudioUserDto> fetchStudioUser(String email) {
        // TODO: Implement caching mechanism to improve performance
        try {
            return studioUserService.findByEmail(email);
        } catch (StudioException e) {
            return Optional.empty();
        }

    }

}
