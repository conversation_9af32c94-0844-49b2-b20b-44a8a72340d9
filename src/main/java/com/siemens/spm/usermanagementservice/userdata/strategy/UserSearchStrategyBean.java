/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : UserSearchStrategyBean.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.userdata.strategy;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.siemens.spm.common.shared.vo.SimpleResultObject;
import com.siemens.spm.spmstudiosdk.constant.AgencyConstant;
import com.siemens.spm.spmstudiosdk.dto.StudioUserDto;
import com.siemens.spm.spmstudiosdk.dto.StudioUserSearchRequestDto;
import com.siemens.spm.spmstudiosdk.exception.StudioException;
import com.siemens.spm.spmstudiosdk.service.StudioUserService;
import com.siemens.spm.usermanagementservice.api.vo.UserForAlarmSummaryListVO;
import com.siemens.spm.usermanagementservice.api.vo.UserForAlarmSummaryVO;
import com.siemens.spm.usermanagementservice.api.vo.UserSimpleVO;
import com.siemens.spm.usermanagementservice.api.vo.response.UserSimpleListResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.UsersForAlarmSummaryResultObject;
import com.siemens.spm.usermanagementservice.domain.AgencyUserSetting;
import com.siemens.spm.usermanagementservice.domain.AlarmSummaryFrequency;
import com.siemens.spm.usermanagementservice.domain.NotiSetting;
import com.siemens.spm.usermanagementservice.repository.NotiSettingRepository;
import com.siemens.spm.usermanagementservice.repository.UserRepository;
import com.siemens.spm.usermanagementservice.userdata.util.UserVOBuilder;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@Transactional
public class UserSearchStrategyBean implements UserSearchStrategy {

    @Autowired
    private StudioUserService studioUserService;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private NotiSettingRepository notiSettingRepository;

    private static final String STUDIO_API_ERROR_MESSAGE = "Error when call Studio";


    @Override
    public UsersForAlarmSummaryResultObject getUsersForAlarmSummaryInternal(Integer agencyId) {
        List<NotiSetting> notiSettings = notiSettingRepository.findForAlarmSummary(agencyId);

        List<Long> userIds = notiSettings.stream()
                .map(NotiSetting::getAgencyUserSetting)
                .map(AgencyUserSetting::getUserId)
                .toList();

        StudioUserSearchRequestDto searchRequestDto = StudioUserSearchRequestDto.builder()
                .agencyId(agencyId)
                .userIds(userIds)
                .isEnabled(true)
                .build();

        List<StudioUserDto> studioUserDtoList;
        try {
            studioUserDtoList = studioUserService.searchUsers(searchRequestDto).getFirst();
        } catch (StudioException e) {
            log.error(STUDIO_API_ERROR_MESSAGE, e);

            studioUserDtoList = List.of();
        }

        Map<Long, StudioUserDto> userMap = studioUserDtoList.stream()
                .collect(Collectors.toMap(userDto -> (long) userDto.getId(), Function.identity()));

        List<UserForAlarmSummaryVO> userForAlarmSummaryVOList = notiSettings.stream()
                .filter(notiSetting -> userMap.containsKey(notiSetting.getAgencyUserSetting().getUserId()))
                .map(notiSetting -> UserForAlarmSummaryVO.builder()
                        .userInternalVO(UserVOBuilder.buildUserInternalVO(
                                userMap.get(notiSetting.getAgencyUserSetting().getUserId())))
                        .alarmTimeFrom(getAlarmFromTime(notiSetting))
                        .alarmTimeTo(notiSetting.getAlarmToDate().atTime(23, 59, 59))
                        .build())
                .toList();

        return new UsersForAlarmSummaryResultObject(new UserForAlarmSummaryListVO(userForAlarmSummaryVOList),
                SimpleResultObject.SimpleStatusCode.SUCCESS);
    }

    @Override
    public UserSimpleListResultObject getAllSimpleUsers(Integer agencyId) {
        List<StudioUserDto> studioUserDtoList;
        if (agencyId == null || agencyId == AgencyConstant.SYSTEM_AGENCY_ID) {
            try {
                studioUserDtoList = studioUserService.findAll();
            } catch (StudioException e) {
                log.error(STUDIO_API_ERROR_MESSAGE, e);

                return new UserSimpleListResultObject(UserSimpleListResultObject.UserSimpleListStatusCode.ERROR);
            }
        } else {
            try {
                studioUserDtoList = studioUserService.findAllByAgencyId(agencyId);
            } catch (StudioException e) {
                log.error(STUDIO_API_ERROR_MESSAGE, e);

                return new UserSimpleListResultObject(UserSimpleListResultObject.UserSimpleListStatusCode.ERROR);
            }
        }

        ArrayList<UserSimpleVO> userSimpleVOArrayList = studioUserDtoList.stream()
                .map(studioUserDto -> UserSimpleVO.builder()
                        .id(Long.valueOf(studioUserDto.getId()))
                        .email(studioUserDto.getEmail())
                        .firstName(studioUserDto.getFirstName())
                        .lastName(studioUserDto.getLastName())
                        .build()
                )
                .collect(Collectors.toCollection(ArrayList::new));
        return new UserSimpleListResultObject(userSimpleVOArrayList,
                UserSimpleListResultObject.UserSimpleListStatusCode.SUCCESS);
    }

    private LocalDateTime getAlarmFromTime(NotiSetting notiSetting) {
        Optional<AlarmSummaryFrequency> frequencyOptional = AlarmSummaryFrequency.resolve(
                notiSetting.getAlarmSummaryFrequency());
        if (frequencyOptional.isEmpty()) {
            return null;
        }

        AlarmSummaryFrequency frequency = frequencyOptional.get();
        return switch (frequency) {
            case DAILY -> notiSetting.getAlarmToDate().atStartOfDay();
            case WEEKLY -> notiSetting.getAlarmToDate().minusWeeks(1).atStartOfDay();
            case MONTHLY -> notiSetting.getAlarmToDate().minusMonths(1).atStartOfDay();
        };
    }

}
