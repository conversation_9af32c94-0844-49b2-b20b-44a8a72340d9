package com.siemens.spm.usermanagementservice.userdata.util;

import com.siemens.spm.usermanagementservice.domain.NotiSetting;

public class NotiSettingFactory {

    private NotiSettingFactory() {
    }

    public static NotiSetting defaultEntity() {
        // Default alarm summary is disabled (then the interval and sent time should be null)
        return NotiSetting.builder()
                .alarmSummaryEnabled(false)
                .alarmSummaryFrequency(null)
                .build();
    }

}
