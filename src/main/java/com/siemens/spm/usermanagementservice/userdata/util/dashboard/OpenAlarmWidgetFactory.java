package com.siemens.spm.usermanagementservice.userdata.util.dashboard;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.siemens.spm.common.util.BeanFinder;
import com.siemens.spm.usermanagementservice.api.constant.DashboardValidation;
import com.siemens.spm.usermanagementservice.api.vo.OpenAlarmWidgetVO;
import lombok.extern.slf4j.Slf4j;

/**
 * Factory used to build data for open alarm widget.
 *
 * <AUTHOR> Nguyen - <EMAIL>
 * @see OpenAlarmWidgetVO
 */
@Slf4j
public final class OpenAlarmWidgetFactory {

    private OpenAlarmWidgetFactory() {
    }

    public static OpenAlarmWidgetVO defaultVO() {
        return OpenAlarmWidgetVO.builder()
                .intersectionOption(DashboardValidation.OpenAlarm.DEFAULT_INTERSECTION_OPTION)
                .build();
    }

    public static String defaultJson() {
        try {
            return BeanFinder.getDefaultObjectMapper().writeValueAsString(defaultVO());
        } catch (JsonProcessingException e) {
            log.error("Error when convert default open alarm widget to json", e);

            return null;
        }
    }

}
