package com.siemens.spm.usermanagementservice.userdata.util.dashboard;

import java.sql.Timestamp;
import java.time.Duration;
import java.util.List;

import org.springframework.util.StringUtils;

import com.siemens.spm.common.util.ListUtil;
import com.siemens.spm.usermanagementservice.api.constant.DashboardValidation.MapView;
import com.siemens.spm.usermanagementservice.api.constant.IntersectionOption;
import com.siemens.spm.usermanagementservice.api.constant.PeriodType;
import com.siemens.spm.usermanagementservice.api.vo.DisplaySettingsVO;
import com.siemens.spm.usermanagementservice.api.vo.LocationVO;
import com.siemens.spm.usermanagementservice.api.vo.MapViewWidgetVO;
import com.siemens.spm.usermanagementservice.api.vo.NotificationWidgetVO;
import com.siemens.spm.usermanagementservice.api.vo.OpenAlarmWidgetVO;
import com.siemens.spm.usermanagementservice.api.vo.SummaryReportWidgetVO;
import com.siemens.spm.usermanagementservice.api.vo.TopIntOpenAlarmWidgetVO;
import com.siemens.spm.usermanagementservice.api.vo.VolumeReportWidgetVO;

/**
 * Used to validate data of dashboard(widgets, ...)
 *
 * <AUTHOR> Nguyen - <EMAIL>
 */
public final class DashboardValidatorUtil {

    private DashboardValidatorUtil() {
    }

    public static boolean isValidMapViewWidget(MapViewWidgetVO mapViewWidgetVO) {
        if (mapViewWidgetVO == null || !StringUtils.hasText(mapViewWidgetVO.getPosition())) {
            return false;
        }
        if (!DisplaySettingsVO.SHOWN_METRIC_DISPLAY.contains(mapViewWidgetVO.getDefaultMetric())) {
            return false;
        }
        PeriodType periodType = mapViewWidgetVO.getPeriodType();
        if (!isValidPeriodType(periodType, mapViewWidgetVO.getFromTime(), mapViewWidgetVO.getToTime())) {
            return false;
        }

        Float zoomLevel = mapViewWidgetVO.getZoomLevel();
        if (zoomLevel < MapView.MIN_ZOOM_LEVEL || zoomLevel > MapView.MAX_ZOOM_LEVEL) {
            return false;
        }

        LocationVO centerPoint = mapViewWidgetVO.getCenterPoint();
        if (centerPoint.getLatitude() == null || centerPoint.getLongitude() == null) {
            // TODO: Validation for latitude and longitude
            return false;
        }

        Integer normalMapRadius = mapViewWidgetVO.getNormalMapRadius();
        Integer heatMapRadius = mapViewWidgetVO.getHeatMapRadius();
        if (isInvalidRadius(normalMapRadius) || isInvalidRadius(heatMapRadius)) {
            return false;
        }

        Integer heatMapBlur = mapViewWidgetVO.getHeatMapBlur();
        return heatMapBlur >= MapView.MIN_BLUR && heatMapBlur <= MapView.MAX_BLUR;
    }

    public static boolean isValidOpenAlarmWidget(OpenAlarmWidgetVO openAlarmWidgetVO) {
        if (openAlarmWidgetVO == null || !StringUtils.hasText(openAlarmWidgetVO.getPosition())) {
            return false;
        }

        return isValidIntersectionOption(openAlarmWidgetVO.getIntersectionOption(),
                openAlarmWidgetVO.getIntersectionIds());
    }

    public static boolean isValidTopIntOpenAlarmWidget(TopIntOpenAlarmWidgetVO topIntOpenAlarmWidgetVO) {
        if (topIntOpenAlarmWidgetVO == null || !StringUtils.hasText(topIntOpenAlarmWidgetVO.getPosition())) {
            return false;
        }

        return isValidIntersectionOption(topIntOpenAlarmWidgetVO.getIntersectionOption(),
                topIntOpenAlarmWidgetVO.getIntersectionIds());
    }

    private static boolean isInvalidRadius(Integer radius) {
        return radius == null || radius < MapView.MIN_RADIUS || radius > MapView.MAX_RADIUS;
    }

    public static boolean isValidSummaryReportWidget(SummaryReportWidgetVO summaryReportWidgetVO) {
        if (summaryReportWidgetVO == null || !StringUtils.hasText(summaryReportWidgetVO.getPosition())) {
            return false;
        }

        PeriodType periodType = summaryReportWidgetVO.getPeriodType();
        return isValidPeriodType(periodType, summaryReportWidgetVO.getFromTime(), summaryReportWidgetVO.getToTime());

        // TODO: Validate each metric is valid or not
    }

    public static boolean isValidVolumeReportWidget(VolumeReportWidgetVO volumeReportWidgetVO) {
        if (volumeReportWidgetVO == null || !StringUtils.hasText(volumeReportWidgetVO.getPosition())) {
            return false;
        }

        PeriodType periodType = volumeReportWidgetVO.getPeriodType();
        if (!isValidPeriodType(periodType, volumeReportWidgetVO.getFromTime(), volumeReportWidgetVO.getToTime())) {
            return false;
        }

        return isValidIntersectionOption(volumeReportWidgetVO.getIntersectionOption(),
                volumeReportWidgetVO.getIntersectionIds());
    }

    public static boolean isValidNotificationWidget(NotificationWidgetVO notificationWidgetVO) {
        if (notificationWidgetVO == null || !StringUtils.hasText(notificationWidgetVO.getPosition())) {
            return false;
        }

        PeriodType periodType = notificationWidgetVO.getPeriodType();
        return isValidPeriodType(periodType, notificationWidgetVO.getFromTime(), notificationWidgetVO.getToTime());
    }

    private static boolean isValidPeriodType(PeriodType periodType, Timestamp fromTime, Timestamp toTime) {
        if (periodType == null) {
            return false;
        } else if (periodType == PeriodType.CUSTOM) {
            return fromTime != null && toTime != null && toTime.after(fromTime)
                    && toTime.getTime() - fromTime.getTime() <= Duration.ofDays(1).toMillis();
        }

        return true;
    }

    private static boolean isValidIntersectionOption(IntersectionOption intersectionOption,
                                                     List<String> intersectionIds) {
        if (intersectionOption == null) {
            return false;
        }

        return intersectionOption == IntersectionOption.INCLUDE_ALL || ListUtil.hasItem(intersectionIds);
    }

}
