package com.siemens.spm.usermanagementservice.userdata.util.dashboard;

import com.siemens.spm.usermanagementservice.api.vo.DashboardMetadataVO;
import com.siemens.spm.usermanagementservice.domain.AgencySettings;
import com.siemens.spm.usermanagementservice.domain.DashboardSetting;

/**
 * <AUTHOR> Ng<PERSON>en - <EMAIL>
 */
public final class DashboardSettingFactory {

    private DashboardSettingFactory() {
    }

    public static DashboardSetting defaultEntity(AgencySettings agencySettings) {
        return DashboardSetting.builder()
                .mapViewWidgetJson(MapViewWidgetFactory.defaultJson(agencySettings))
                .openAlarmWidgetJson(OpenAlarmWidgetFactory.defaultJson())
                .topIntOpenAlarmWidgetJson(TopIntOpenAlarmWidgetFactory.defaultJson())
                .build();
    }

    public static DashboardMetadataVO metadataVO() {
        return DashboardMetadataVO.builder()
                .summaryReportWidgetMetadataVO(SummaryReportWidgetFactory.metadataVO())
                .notificationWidgetMetadataVO(NotificationWidgetFactory.metadataVO())
                .build();
    }

}
