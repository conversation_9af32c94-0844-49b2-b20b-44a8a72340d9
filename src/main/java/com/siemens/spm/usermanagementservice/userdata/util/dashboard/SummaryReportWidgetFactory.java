package com.siemens.spm.usermanagementservice.userdata.util.dashboard;

import java.util.List;

import com.siemens.spm.common.shared.domaintype.alarm.trafficmetric.TrafficMetric;
import com.siemens.spm.common.util.BeanFinder;
import com.siemens.spm.usermanagementservice.api.constant.PeriodType;
import com.siemens.spm.usermanagementservice.api.vo.SummaryReportWidgetMetadataVO;
import com.siemens.spm.usermanagementservice.api.vo.SummaryReportWidgetMetricVO;
import com.siemens.spm.usermanagementservice.api.vo.SummaryReportWidgetVO;
import com.siemens.spm.usermanagementservice.api.vo.WarningConditionVO;

/**
 * Factory used to build data for summary report widget.
 *
 * <AUTHOR> Nguyen - <EMAIL>
 * @see SummaryReportWidgetVO
 */
public final class SummaryReportWidgetFactory {

    private SummaryReportWidgetFactory() {
    }

    public static final List<TrafficMetric> SUPPORTED_METRICS = List.of(
            TrafficMetric.AOR_PERCENT,
            TrafficMetric.AOG_PERCENT,
            TrafficMetric.DETECTOR_ACTUATION,
            TrafficMetric.MAX_OUT_PERCENT,
            TrafficMetric.GAP_OUT_PERCENT,
            TrafficMetric.FORCE_OFF_PERCENT,
            TrafficMetric.AVG_PED_DELAY,
            TrafficMetric.PED_DELAY_ACTIVATION,
            TrafficMetric.TRANSITION_PERCENT,
            TrafficMetric.AVG_GOR_PERCENT,
            TrafficMetric.AVG_ROR5_PERCENT,
            TrafficMetric.SPLIT_FAILURE,
            TrafficMetric.POOR_COORDINATION,
            TrafficMetric.EXCESS_CAPACITY,
            TrafficMetric.AVG_APP_DELAY,
            TrafficMetric.AVG_QUEUE_LENGTH,
            TrafficMetric.PREEMPTION_PRIORITY,
            TrafficMetric.RED_LIGHT_VIOLATION_COUNT
    );

    static {
        SUPPORTED_METRICS.forEach(
                trafficMetric -> trafficMetric.acceptTranslator(BeanFinder.getDefaultMessageService()));
    }

    public static SummaryReportWidgetVO defaultVO() {
        List<SummaryReportWidgetMetricVO> metricVOList = SUPPORTED_METRICS.stream()
                .map(metric -> SummaryReportWidgetMetricVO.builder()
                        .metricId(metric.getId())
                        // Default warning condition should be none
                        .warningConditionVO(new WarningConditionVO())
                        .enabled(null)
                        .build())
                .toList();

        return SummaryReportWidgetVO.builder()
                .periodType(PeriodType.LAST_24_HOURS)
                .metricVOList(metricVOList)
                .build();
    }

    public static SummaryReportWidgetMetadataVO metadataVO() {
        return SummaryReportWidgetMetadataVO.builder()
                .metrics(SUPPORTED_METRICS)
                .build();
    }

}
