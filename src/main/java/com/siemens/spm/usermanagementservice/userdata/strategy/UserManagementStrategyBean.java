/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : UserManagementStrategy.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.userdata.strategy;

import java.util.Date;
import java.util.List;

import com.siemens.spm.common.agency.supports.AgencyAware;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.siemens.spm.common.util.DateTimeUtils;
import com.siemens.spm.usermanagementservice.api.vo.Expiration;
import com.siemens.spm.usermanagementservice.config.UserConfig;
import com.siemens.spm.usermanagementservice.userdata.strategy.expirationscanner.ExpirationStrategy;
import com.siemens.spm.usermanagementservice.userdata.strategy.housekeeping.HouseKeepingStrategy;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@Transactional
public class UserManagementStrategyBean implements UserManagementStrategy {

    @Autowired
    @Qualifier(ExpirationStrategy.USER_KEY_EXPIRATION_BEAN)
    private ExpirationStrategy userKeyExpirationStrategy;

    @Autowired
    private HouseKeepingStrategy houseKeepingStrategy;

    @Autowired
    private UserConfig userConfig;

    @Override
    @AgencyAware(agencyId = "[0]")
    public void scanExpirationDataInternalAsync(Integer agencyId, List<Expiration> expList) {
        if (expList == null) {
            log.debug("No expiration type is defined!");
            return;
        }

        long currentMillis = System.currentTimeMillis();
        Date currentDate = new Date(currentMillis);
        Date toDate;

        for (Expiration type : expList) {
            switch (type) {
            case USER_KEY -> {
                toDate = DateTimeUtils.plusDays(currentDate, userConfig.getUserKeyExpiryNotificationInDays());
                userKeyExpirationStrategy.scanExpiration(currentDate, toDate);
            }
            case HOUSE_KEEPING -> houseKeepingStrategy.doHouseKeeping();
            default -> log.warn("The expiration type {} is not supported!", type);
            }
        }
    }

}
