/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : UserSearchStrategy.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.userdata.strategy;

import com.siemens.spm.usermanagementservice.api.vo.response.UserSimpleListResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.UsersForAlarmSummaryResultObject;

public interface UserSearchStrategy {

    /**
     * Find all active users who set their notification preferences to receive alarm record summary for the current
     * hour. The result also include the alarm time range.
     *
     * @param agencyId id of agency need to get users for alarm summary
     * @return {@link UsersForAlarmSummaryResultObject}
     */
    UsersForAlarmSummaryResultObject getUsersForAlarmSummaryInternal(Integer agencyId);

    UserSimpleListResultObject getAllSimpleUsers(Integer agencyId);

}
