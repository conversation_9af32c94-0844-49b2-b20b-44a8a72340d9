package com.siemens.spm.usermanagementservice.userdata.strategy;

import com.siemens.spm.datahub.exception.DataHubException;
import com.siemens.spm.usermanagementservice.api.vo.request.UpdateNotiSettingRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.UpdateWidgetsSettingRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.AgencyUserSettingMetadataResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.AgencyUserSettingResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.NotiSettingResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.NotiSettingUpdatedResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.WidgetsSettingUpdatedResultObject;
import com.siemens.spm.usermanagementservice.domain.AgencyUserSetting;

/**
 * <AUTHOR> <PERSON> - <EMAIL>
 */
public interface AccountSettingStrategy {

    /**
     * @param agencyId id of current agency for siemens admin
     * @return setting data of current user in current agency. Use provided agencyId if current user is siemens admin
     */
    AgencyUserSettingResultObject getUserSetting(Integer agencyId) throws DataHubException;

    /**
     * Find user's setting on specific agency if it is present. Otherwise, create new one with default value. The
     * default value should depend on agency setting
     *
     * @param agencyId id of agency which user belong to
     * @param userId   id of user need to create setting
     * @return If the setting already exist, just return the existed one. Otherwise, return the new one
     * @apiNote If the setting already exist, just return the existed one
     */
    AgencyUserSetting findOrInitDefaultAgencyUserSetting(Integer agencyId, Long userId);

    /**
     * Update widget's setting of current user on specific agency
     *
     * @param requestVO {@link UpdateWidgetsSettingRequestVO} requestVO
     * @param updateAll which indicate should only update a specific widget or all widgets
     * @return {@link WidgetsSettingUpdatedResultObject}
     */
    WidgetsSettingUpdatedResultObject updateWidgetsSetting(UpdateWidgetsSettingRequestVO requestVO, boolean updateAll);

    /**
     * Get setting's metadata of current user on specific agency
     *
     * @param agencyId id of current agency. Only need for user who have permission on all agencies
     * @return {@link AgencyUserSettingMetadataResultObject}
     */
    AgencyUserSettingMetadataResultObject getUserSettingMetadata(Integer agencyId);

    /**
     * Get setting's metadata of current user on specific agency
     *
     * @param requestVO {@link UpdateNotiSettingRequestVO} requestVO
     * @return {@link NotiSettingUpdatedResultObject}
     */
    NotiSettingUpdatedResultObject updateNotiSetting(UpdateNotiSettingRequestVO requestVO);

    /**
     * Get notification setting (about email/system's noti) of current user on specific agency
     *
     * @param agencyId agencyId id of current agency. Only need for user who have permission on all agencies
     * @return {@link NotiSettingResultObject}
     */
    NotiSettingResultObject getNotiSetting(Integer agencyId);

}
