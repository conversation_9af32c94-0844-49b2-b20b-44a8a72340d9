/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AccountStrategy.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.userdata.strategy;

import com.siemens.spm.common.constant.CommonConstants;
import com.siemens.spm.common.message.MessageService;
import com.siemens.spm.common.security.SecurityUtils;
import com.siemens.spm.common.shared.domaintype.UserStatus;
import com.siemens.spm.common.util.ListUtil;
import com.siemens.spm.spmstudiosdk.dto.*;
import com.siemens.spm.spmstudiosdk.exception.StudioException;
import com.siemens.spm.spmstudiosdk.service.StudioAgencyService;
import com.siemens.spm.spmstudiosdk.service.StudioUserService;
import com.siemens.spm.usermanagementservice.agencydata.strategy.AgencyLicenseStrategy;
import com.siemens.spm.usermanagementservice.api.vo.AgencyUserAccessVO;
import com.siemens.spm.usermanagementservice.api.vo.AuthorityVO;
import com.siemens.spm.usermanagementservice.api.vo.UserVO;
import com.siemens.spm.usermanagementservice.api.vo.request.LanguageRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.UserKeyRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.AgencyUserAccessResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.ChangeLanguageResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.ChangeLanguageResultObject.ChangeLanguageResultStatusCode;
import com.siemens.spm.usermanagementservice.api.vo.response.UserKeyResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.UserResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.UserResultObject.UserStatusCode;
import com.siemens.spm.usermanagementservice.domain.User;
import com.siemens.spm.usermanagementservice.repository.UserRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> Nguyen
 */
@Slf4j
@Service
@Transactional
public class AccountStrategyBean {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private StudioUserService studioUserService;

    @Autowired
    private StudioAgencyService studioAgencyService;

    @Autowired
    private AgencyLicenseStrategy agencyLicenseStrategy;

    //This is the list of roles that Insight Front-end are handling
    private final List<StudioRoleType> INSIGHT_ROLES = Arrays.asList(
            StudioRoleType.SystemAdmin,
            StudioRoleType.InsightsAgencyAdmin,
            StudioRoleType.InsightsAgencyOperator
    );

    public UserResultObject getCurrentUser() {
        String email = SecurityUtils.getCurrentUserEmail();

        Optional<StudioUserDto> userDtoOptional;
        try {
            userDtoOptional = studioUserService.findByEmail(email);
        } catch (StudioException e) {
            log.error("Error when getting user by email {} from Studio", email, e);
            return new UserResultObject(null, UserStatusCode.ERROR);
        }
        if (userDtoOptional.isEmpty()) {
            log.debug("Not found user with email {} from Studio", email);
            return new UserResultObject(null, UserStatusCode.USER_NOT_FOUND);
        }

        StudioUserDto studioUserDto = userDtoOptional.get();

        UserVO userVO = retrieveUserProfile(studioUserDto);
        return new UserResultObject(userVO);
    }

    public UserKeyResultObject createUserKey(UserKeyRequestVO requestVO) {
        //        Long userId = SecurityUtils.getCurrentUserId();
        //        assert userId != null;
        //
        //        Optional<User> optUser = userRepository.findById(userId);
        //        if (optUser.isEmpty()) {
        //            return new UserKeyResultObject(null, UserKeyStatusCode.USER_NOT_FOUND);
        //        }
        //
        //        User user = optUser.get();
        //
        //        // Generate user key
        //        String userKey = RandomUtil.generateUUID();
        //        String cipherText = SymmetricAesEncryption.encode(userKey, userConfig.getSpmSymmetricKey());
        //
        //        // Persist
        //        user.setUserKey(cipherText);
        //        user.setUserKeyExpiration(userConfig.getUserKeyExpirationDateFromNow());
        //        userRepository.save(user);
        //
        //        UserKeyVO userKeyVO = UserKeyVO.builder()
        //                .userKey(userKey)
        //                .build();
        //
        //        return new UserKeyResultObject(userKeyVO, UserKeyStatusCode.CREATED);
        return null;
    }

    public UserKeyResultObject getUserKey(UserKeyRequestVO requestVO) {
        //        Long userId = SecurityUtils.getCurrentUserId();
        //        assert userId != null;
        //
        //        Optional<User> optUser = userRepository.findById(userId);
        //        if (optUser.isEmpty()) {
        //            return new UserKeyResultObject(null, UserKeyStatusCode.USER_NOT_FOUND);
        //        }
        //
        //        User user = optUser.get();
        //
        //        Date currentDate = new Date(System.currentTimeMillis());
        //        if (user.getUserKeyExpiration() == null || currentDate.after(user.getUserKeyExpiration())) {
        //            log.warn("The user_key of the user {} expired", user.getEmail());
        //
        //            return new UserKeyResultObject(null, UserKeyStatusCode.USER_KEY_EXPIRED);
        //        }
        //
        //        if (user.getUserKey() == null) {
        //            log.warn("The user {} do not have user key", user.getEmail());
        //
        //            return new UserKeyResultObject(null, UserKeyStatusCode.USER_KEY_NOT_EXIST);
        //        }
        //
        //        UserKeyVO userKeyVO = UserKeyVO.builder()
        //                .userKey(SymmetricAesEncryption.decode(user.getUserKey(), userConfig.getSpmSymmetricKey()))
        //                .userKeyExpiration(user.getUserKeyExpiration())
        //                .build();
        //
        //        return new UserKeyResultObject(userKeyVO, UserKeyStatusCode.CREATED);
        return null;
    }

    public ChangeLanguageResultObject updateLanguage(LanguageRequestVO requestVO) {
        String language = requestVO.getLanguage();

        if (!MessageService.isSupportedLanguage(language)) {
            log.error("Language {} is not supported", language);

            return new ChangeLanguageResultObject(null, ChangeLanguageResultStatusCode.NOT_SUPPORT_LANGUAGE);
        }

        Optional<User> optUser = userRepository.findByEmail(SecurityUtils.getCurrentUserEmail());

        optUser.ifPresent(user -> user.setLanguage(language));

        SecurityUtils.setCurrentUserLanguage(language);

        return new ChangeLanguageResultObject(null, ChangeLanguageResultStatusCode.SUCCESS);
    }

    @Transactional
    public AgencyUserAccessResultObject accessAgency(Integer agencyId) {
        String email = SecurityUtils.getCurrentUserEmail();

        Optional<StudioUserAssignmentDto> userAssignmentDtoOptional;
        try {
            userAssignmentDtoOptional = studioUserService.findUserAssignmentByEmail(email);
        } catch (StudioException e) {
            log.error("Error when getting user assignment by email {}", email, e);
            return new AgencyUserAccessResultObject(AgencyUserAccessResultObject.StatusCode.ERROR);
        }
        if (userAssignmentDtoOptional.isEmpty()) {
            log.debug("User {} not found from Studio", email);
            return new AgencyUserAccessResultObject(AgencyUserAccessResultObject.StatusCode.USER_NOT_FOUND);
        }

        StudioUserAssignmentDto userAssignmentDto = userAssignmentDtoOptional.get();
        List<StudioAgencyRoleDto> studioAgencyRoleDtoList = userAssignmentDto.getAgencyRoles();
        if (ListUtil.hasNoItem(studioAgencyRoleDtoList)) {
            return new AgencyUserAccessResultObject(AgencyUserAccessResultObject.StatusCode.NO_AGENCY_ACCESS);
        }

        StudioAgencyRoleDto agencyRoleDto = getAgencyRoleByAgencyId(agencyId, studioAgencyRoleDtoList);
        if (agencyRoleDto == null) {
            return new AgencyUserAccessResultObject(null, AgencyUserAccessResultObject.StatusCode.NO_AGENCY_ACCESS);
        }

        agencyId = agencyRoleDto.getAgencyId();

        StudioAgencyDto agencyDto;
        try {
            agencyDto = studioAgencyService.getAgencyById(agencyId).orElse(null);
        } catch (StudioException e) {
            log.error("Error when getting agency by id {}", agencyId, e);
            agencyDto = null;
        }

        AuthorityVO authorityVO = AuthorityVO.builder()
                .agencyId(agencyRoleDto.getAgencyId())
                .agencyName(agencyDto != null ? agencyDto.getAgencyName() : CommonConstants.NOT_AVAILABLE)
                .roleName(String.valueOf(agencyRoleDto.getRoleType()))
                .licenses(agencyLicenseStrategy.getLicenseIds(agencyId))
                .build();

        // Get or init current user
        AgencyUserAccessVO agencyUserAccessVO = AgencyUserAccessVO.builder()
                .userVO(retrieveUserProfile(userAssignmentDto))
                .authority(authorityVO)
                .build();

        return new AgencyUserAccessResultObject(agencyUserAccessVO, AgencyUserAccessResultObject.StatusCode.SUCCESS);
    }

    private StudioAgencyRoleDto getAgencyRoleByAgencyId(Integer agencyId,
                                                        List<StudioAgencyRoleDto> studioAgencyRoleDtoList) {
        if (ListUtil.hasNoItem(studioAgencyRoleDtoList)) {
            return null;
        }

        if (agencyId == null) {
            // FIXME: Currently, just ignore the YUNEX agency(hard coded id==1)
            //  Find a solution to handle the YUNEX agency and clean up this code (ex: user constant for agency id, etc.)
            return studioAgencyRoleDtoList.stream()
                    .filter(studioAgencyRoleDto -> studioAgencyRoleDto.getAgencyId() != 1)
                    .min(Comparator.comparing(StudioAgencyRoleDto::getAgencyId))
                    .orElse(null);
        }

        return studioAgencyRoleDtoList.stream()
                .filter(role -> agencyId.equals(role.getAgencyId()))
                .filter(role -> INSIGHT_ROLES.contains(role.getRoleType()))
                .min(Comparator.comparingInt(role -> role.getRoleType().ordinal()))
                .orElse(null);
    }

    private UserVO retrieveUserProfile(StudioUserDto studioUserDto) {
        return UserVO.builder()
                .userId(Long.valueOf(studioUserDto.getId()))
                .firstName(studioUserDto.getFirstName())
                .lastName(studioUserDto.getLastName())
                .email(studioUserDto.getEmail())
                .phone(studioUserDto.getPhoneNumber())
                .language("en")
                .status(Boolean.TRUE.equals(studioUserDto.getIsEnabled()) ?
                        UserStatus.ACTIVE.name() :
                        UserStatus.INACTIVE.name())
                .build();
    }

}
