/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AccountFacade.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.userdata.boundary;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.siemens.spm.usermanagementservice.api.boundary.AccountService;
import com.siemens.spm.usermanagementservice.api.vo.request.LanguageRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.UserKeyRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.AgencyUserAccessResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.ChangeLanguageResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.UserKeyResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.UserResultObject;
import com.siemens.spm.usermanagementservice.userdata.strategy.AccountStrategyBean;

@Service
@Transactional
public class AccountFacadeBean implements AccountService {

    @Autowired
    private AccountStrategyBean accountStrategy;

    @Override
    public UserResultObject getCurrentUser() {
        return accountStrategy.getCurrentUser();
    }

    @Override
    public UserKeyResultObject createUserKey(UserKeyRequestVO currentPasswordVO) {
        return accountStrategy.createUserKey(currentPasswordVO);
    }

    @Override
    public UserKeyResultObject getUserKey(UserKeyRequestVO currentPasswordVO) {
        return accountStrategy.getUserKey(currentPasswordVO);
    }

    @Override
    public ChangeLanguageResultObject updateLanguage(LanguageRequestVO langRequestVO) {
        return accountStrategy.updateLanguage(langRequestVO);
    }

    @Override
    public AgencyUserAccessResultObject accessAgency(Integer agencyId) {
        return accountStrategy.accessAgency(agencyId);
    }

}
