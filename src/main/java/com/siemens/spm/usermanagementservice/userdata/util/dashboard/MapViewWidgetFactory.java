package com.siemens.spm.usermanagementservice.userdata.util.dashboard;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.siemens.spm.common.shared.domaintype.MetricConstants;
import com.siemens.spm.common.util.BeanFinder;
import com.siemens.spm.usermanagementservice.api.constant.DashboardValidation;
import com.siemens.spm.usermanagementservice.api.vo.LocationVO;
import com.siemens.spm.usermanagementservice.api.vo.MapViewWidgetVO;
import com.siemens.spm.usermanagementservice.domain.AgencySettings;
import lombok.extern.slf4j.Slf4j;

/**
 * Factory used to build data for map view widget.
 *
 * <AUTHOR> Ng<PERSON>en - <EMAIL>
 * @see MapViewWidgetVO
 */
@Slf4j
public class MapViewWidgetFactory {

    private MapViewWidgetFactory() {
    }

    public static MapViewWidgetVO defaultVO(AgencySettings agencySettings) {
        LocationVO centerPoint = DashboardValidation.MapView.DEFAULT_CENTER_POINT;
        float zoomLevel = DashboardValidation.MapView.DEFAULT_ZOOM_LEVEL;

        if (agencySettings != null
                && agencySettings.getCentralIntersectionLatitude() != null
                && agencySettings.getCentralIntersectionLongitude() != null) {
            centerPoint = new LocationVO(agencySettings.getCentralIntersectionLatitude(),
                    agencySettings.getCentralIntersectionLongitude());
            zoomLevel = DashboardValidation.MapView.CITY_ZOOM_LEVEL;
        }

        return MapViewWidgetVO.builder()
                .periodType(DashboardValidation.MapView.DEFAULT_PERIOD_TYPE)
                .zoomLevel(zoomLevel)
                .centerPoint(centerPoint)
                .defaultMetric(MetricConstants.OPEN_ALARM_NOTI_COUNT_ID)
                .normalMapRadius(DashboardValidation.MapView.DEFAULT_RADIUS)
                .heatMapRadius(DashboardValidation.MapView.DEFAULT_RADIUS)
                .heatMapBlur(DashboardValidation.MapView.DEFAULT_BLUR)
                .build();
    }

    public static String defaultJson(AgencySettings agencySettings) {
        try {
            return BeanFinder.getDefaultObjectMapper().writeValueAsString(defaultVO(agencySettings));
        } catch (JsonProcessingException e) {
            log.error("Error when parse default map view widget json", e);

            return null;
        }
    }

}
