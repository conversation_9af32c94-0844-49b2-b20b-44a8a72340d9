package com.siemens.spm.usermanagementservice.userdata.util.dashboard;

import com.siemens.spm.usermanagementservice.api.vo.DashboardSettingVO;
import com.siemens.spm.usermanagementservice.domain.DashboardSetting;

/**
 * <AUTHOR> - <EMAIL>
 */
public final class DashboardSettingVOBuilder {

    private DashboardSettingVOBuilder() {
    }

    /**
     * Build {@link DashboardSettingVO} object
     *
     * @param dashboardSetting entity need to build vo object
     * @return {@link DashboardSettingVO}
     * @throws IllegalArgumentException if argument is null
     */
    public static DashboardSettingVO buildVO(DashboardSetting dashboardSetting) {
        if (dashboardSetting == null) {
            throw new IllegalArgumentException();
        }

        // Call sync before access VO object
        dashboardSetting.syncObjectsFromJsons();

        return DashboardSettingVO.builder()
                .mapViewWidgetVO(dashboardSetting.getMapViewWidgetVO())
                .openAlarmWidgetVO(dashboardSetting.getOpenAlarmWidgetVO())
                .topIntOpenAlarmWidgetVO(dashboardSetting.getTopIntOpenAlarmWidgetVO())
                .summaryReportWidgetVO(dashboardSetting.getSummaryReportWidgetVO())
                .volumeReportWidgetVO(dashboardSetting.getVolumeReportWidgetVO())
                .notificationWidgetVO(dashboardSetting.getNotificationWidgetVO())
                .build();
    }

/**
     * Build {@link DashboardSettingVO} object
     *
     * @param dashboardSetting entity need to build vo object
     * @return {@link DashboardSettingVO}
     * @throws IllegalArgumentException if argument is null
     */
    public static DashboardSettingVO buildVO(DashboardSetting dashboardSetting, String agencyTimeZoneId) {
        if (dashboardSetting == null) {
            throw new IllegalArgumentException();
        }

        // Call sync before access VO object
        dashboardSetting.syncObjectsFromJsons(agencyTimeZoneId);

        return DashboardSettingVO.builder()
                .mapViewWidgetVO(dashboardSetting.getMapViewWidgetVO())
                .openAlarmWidgetVO(dashboardSetting.getOpenAlarmWidgetVO())
                .topIntOpenAlarmWidgetVO(dashboardSetting.getTopIntOpenAlarmWidgetVO())
                .summaryReportWidgetVO(dashboardSetting.getSummaryReportWidgetVO())
                .volumeReportWidgetVO(dashboardSetting.getVolumeReportWidgetVO())
                .notificationWidgetVO(dashboardSetting.getNotificationWidgetVO())
                .build();
    }

}
