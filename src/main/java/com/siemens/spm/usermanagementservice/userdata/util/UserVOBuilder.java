/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : UserVOBuilder.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.userdata.util;

import com.siemens.spm.common.shared.domaintype.UserStatus;
import com.siemens.spm.common.util.NameUtil;
import com.siemens.spm.spmstudiosdk.dto.StudioUserDto;
import com.siemens.spm.usermanagementservice.api.vo.UserInternalVO;

public final class UserVOBuilder {

    private UserVOBuilder() {
    }

    public static UserInternalVO buildUserInternalVO(StudioUserDto studioUserDto) {
        return UserInternalVO.builder()
                .id(Long.valueOf(studioUserDto.getId()))
                .email(studioUserDto.getEmail())
                .name(NameUtil.usFullName(studioUserDto.getFirstName(), studioUserDto.getLastName()))
                .phone(studioUserDto.getPhoneNumber())
                .status(Boolean.TRUE.equals(studioUserDto.getIsEnabled()) ?
                        UserStatus.ACTIVE.name() :
                        UserStatus.INACTIVE.name()
                )
                .build();
    }

}
