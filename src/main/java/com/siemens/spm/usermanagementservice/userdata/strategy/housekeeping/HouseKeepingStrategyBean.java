package com.siemens.spm.usermanagementservice.userdata.strategy.housekeeping;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.siemens.spm.common.util.DateTimeUtils;
import com.siemens.spm.usermanagementservice.config.UserConfig;
import com.siemens.spm.usermanagementservice.repository.UserRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional
public class HouseKeepingStrategyBean implements HouseKeepingStrategy {

    private static final int USER_BATCH_SIZE = 10;

    @Autowired
    private UserConfig userConfig;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private NotificationHouseKeeping notificationHouseKeeping;

    @Override
    public void doHouseKeeping() {
        log.info("Start housekeeping");
        cleanupNotification();
        log.info("End housekeeping");
    }

    /**
     * Clean old data from notification table
     */
    private void cleanupNotification() {
        Date date = DateTimeUtils.minusDays(DateTimeUtils.getCurrentDate(), (int) userConfig.getNotificationLifeTime());
        List<Long> userIds = userRepository.findAllUserIds();

        if (userIds == null || userIds.isEmpty()) {
            // clean all notifications, that were created before the {date}, for all users
            notificationHouseKeeping.cleanup(date, null);
            return;
        }

        // cleanup notification for batch of user
        int numOfProcessed = 0;
        while (numOfProcessed < userIds.size()) {
            int batchSize = (userIds.size() < (numOfProcessed + USER_BATCH_SIZE)) ? userIds.size() % USER_BATCH_SIZE
                    : USER_BATCH_SIZE;
            List<Long> batch = userIds.subList(numOfProcessed, numOfProcessed + batchSize);

            notificationHouseKeeping.cleanup(date, batch);
            numOfProcessed += USER_BATCH_SIZE;
        }
    }
}
