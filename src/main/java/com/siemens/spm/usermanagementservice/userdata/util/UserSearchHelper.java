/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : UserSearchHelper.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.userdata.util;

import java.util.Arrays;
import java.util.List;

import com.querydsl.core.types.OrderSpecifier;
import com.siemens.spm.common.util.OrderUtil;
import com.siemens.spm.usermanagementservice.domain.QUser;
import com.siemens.spm.usermanagementservice.domain.User;

/**
 * Provide the helper methods for searching users
 */
public final class UserSearchHelper {

    private static final List<String> SORTABLE_COLUMNS = Arrays.asList(
            User.ColumnName.NAME,
            User.ColumnName.EMAIL
    );

    private UserSearchHelper() {
    }

    @SuppressWarnings({ "unchecked" })
    public static OrderSpecifier<String>[] createOrderBy(String[] orderByColumns) {
        if (orderByColumns == null || orderByColumns.length == 0) {
            return new OrderSpecifier[0];
        }

        String entityName = QUser.user.getMetadata().getName();
        return OrderUtil.createOrderBy(entityName, orderByColumns, SORTABLE_COLUMNS);
    }

}
