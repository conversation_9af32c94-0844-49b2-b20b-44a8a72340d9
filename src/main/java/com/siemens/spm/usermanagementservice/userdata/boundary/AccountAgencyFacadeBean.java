package com.siemens.spm.usermanagementservice.userdata.boundary;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.siemens.spm.datahub.exception.DataHubException;
import com.siemens.spm.usermanagementservice.api.boundary.AccountAgencyService;
import com.siemens.spm.usermanagementservice.api.vo.request.UpdateNotiSettingRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.UpdateWidgetsSettingRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.AgencyUserSettingMetadataResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.AgencyUserSettingResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.NotiSettingResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.NotiSettingUpdatedResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.WidgetsSettingUpdatedResultObject;
import com.siemens.spm.usermanagementservice.userdata.strategy.AccountSettingStrategy;

@Service
public class AccountAgencyFacadeBean implements AccountAgencyService {

    @Autowired
    private AccountSettingStrategy accountSettingStrategy;

    @Override
    public AgencyUserSettingResultObject getUserSetting(Integer agencyId) throws DataHubException {
        return accountSettingStrategy.getUserSetting(agencyId);
    }

    @Override
    public WidgetsSettingUpdatedResultObject updateAllWidgetsSetting(UpdateWidgetsSettingRequestVO requestVO) {
        return accountSettingStrategy.updateWidgetsSetting(requestVO, true);
    }

    @Override
    public WidgetsSettingUpdatedResultObject updateSpecificWidgetSetting(UpdateWidgetsSettingRequestVO requestVO) {
        return accountSettingStrategy.updateWidgetsSetting(requestVO, false);
    }

    @Override
    public AgencyUserSettingMetadataResultObject getUserSettingMetadata(Integer agencyId) {
        return accountSettingStrategy.getUserSettingMetadata(agencyId);
    }

    @Override
    public NotiSettingUpdatedResultObject updateNotiSetting(UpdateNotiSettingRequestVO requestVO) {
        return accountSettingStrategy.updateNotiSetting(requestVO);
    }

    @Override
    public NotiSettingResultObject getNotiSetting(Integer agencyId) {
        return accountSettingStrategy.getNotiSetting(agencyId);
    }

}
