package com.siemens.spm.usermanagementservice.userdata.util.dashboard;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.siemens.spm.common.shared.domaintype.notification.NotificationType;
import com.siemens.spm.common.shared.vo.NotificationTypeVO;
import com.siemens.spm.usermanagementservice.api.constant.PeriodType;
import com.siemens.spm.usermanagementservice.api.vo.NotificationTypeWidgetVO;
import com.siemens.spm.usermanagementservice.api.vo.NotificationWidgetMetadataVO;
import com.siemens.spm.usermanagementservice.api.vo.NotificationWidgetVO;

/**
 * Factory used to build data for notification widget.
 *
 * <AUTHOR> Nguyen - <EMAIL>
 * @see NotificationWidgetVO
 */
public final class NotificationWidgetFactory {

    private NotificationWidgetFactory() {
    }

    public static NotificationWidgetVO defaultVO() {
        List<Long> notificationTypes = Arrays.stream(NotificationType.values())
                .filter(notificationType -> notificationType != NotificationType.OTHER)
                .map(NotificationType::getId)
                .toList();

        return NotificationWidgetVO.builder()
                .periodType(PeriodType.LAST_24_HOURS)
                .notificationTypes(notificationTypes)
                .build();
    }

    public static NotificationWidgetMetadataVO metadataVO() {
        List<NotificationTypeWidgetVO> notificationTypeWidgetVOList = new ArrayList<>();
        for (NotificationType type : NotificationType.values()) {
            NotificationTypeVO typeVO = new NotificationTypeVO(type.getId(), type.getTranslatedName());
            NotificationTypeWidgetVO notificationTypeWidgetVO = new NotificationTypeWidgetVO(typeVO, true);
            if (type == NotificationType.OTHER) {
                // Default all types except OTHER type
                notificationTypeWidgetVO.setEnabled(false);
            }
            notificationTypeWidgetVOList.add(notificationTypeWidgetVO);
        }

        return NotificationWidgetMetadataVO.builder()
                .notificationTypes(notificationTypeWidgetVOList)
                .build();
    }

}
