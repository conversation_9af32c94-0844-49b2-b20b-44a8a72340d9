package com.siemens.spm.usermanagementservice.userdata.strategy.housekeeping;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.siemens.spm.usermanagementservice.repository.NotificationRepository;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional(propagation = Propagation.REQUIRES_NEW)
public class NotificationHouseKeepingStrategyBean implements NotificationHouseKeeping {

    @Autowired
    private NotificationRepository notificationRepository;

    @Override
    public void cleanup(Date date, List<Long> userIds) {
        long totalDeleted;

        if (date == null) {
            log.error("Could not cleanup notification!");
            return;
        }

        log.debug("Clean notifications for users {}", userIds);

        totalDeleted = notificationRepository.cleanup(date, userIds);
        log.debug("{} notifications have been cleaned up from database.", totalDeleted);
    }

}
