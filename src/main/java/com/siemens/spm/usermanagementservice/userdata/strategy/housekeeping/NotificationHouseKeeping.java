package com.siemens.spm.usermanagementservice.userdata.strategy.housekeeping;

import java.util.Date;
import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public interface NotificationHouseKeeping {

    /**
     * Cleanup notification for a batch of user
     * 
     * @param date    Cleanup the notifications, which have already read, were
     *                created before {date}
     * @param userIds The batch of user
     */
    void cleanup(Date date, List<Long> userIds);
}
