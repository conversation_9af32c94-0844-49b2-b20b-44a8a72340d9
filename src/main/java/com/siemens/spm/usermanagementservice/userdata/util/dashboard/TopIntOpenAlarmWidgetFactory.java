package com.siemens.spm.usermanagementservice.userdata.util.dashboard;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.siemens.spm.common.util.BeanFinder;
import com.siemens.spm.usermanagementservice.api.constant.DashboardValidation;
import com.siemens.spm.usermanagementservice.api.vo.TopIntOpenAlarmWidgetVO;
import lombok.extern.slf4j.Slf4j;

/**
 * Factory used to build data for top intersection open alarm widget.
 *
 * <AUTHOR> Nguyen - <EMAIL>
 * @see TopIntOpenAlarmWidgetVO
 */
@Slf4j
public final class TopIntOpenAlarmWidgetFactory {

    private TopIntOpenAlarmWidgetFactory() {
    }

    public static TopIntOpenAlarmWidgetVO defaultVO() {
        return TopIntOpenAlarmWidgetVO.builder()
                .intersectionOption(DashboardValidation.TopIntOpenAlarm.DEFAULT_INTERSECTION_OPTION)
                .build();
    }

    public static String defaultJson() {
        try {
            return BeanFinder.getDefaultObjectMapper().writeValueAsString(defaultVO());
        } catch (JsonProcessingException e) {
            log.error("Error when parsing default top intersection open alarm widget VO to JSON", e);

            return null;
        }
    }

}
