package com.siemens.spm.usermanagementservice.userdata.util.dashboard;

import com.siemens.spm.usermanagementservice.api.constant.IntersectionOption;
import com.siemens.spm.usermanagementservice.api.constant.PeriodType;
import com.siemens.spm.usermanagementservice.api.vo.VolumeReportWidgetVO;

/**
 * Factory used to build data for volume report widget.
 *
 * <AUTHOR> Nguyen - <EMAIL>
 * @see VolumeReportWidgetVO
 */
public final class VolumeReportWidgetFactory {

    private VolumeReportWidgetFactory() {
    }

    public static VolumeReportWidgetVO defaultVO() {
        return VolumeReportWidgetVO.builder()
                .periodType(PeriodType.LAST_24_HOURS)
                .intersectionOption(IntersectionOption.INCLUDE_ALL)
                .build();
    }

}
