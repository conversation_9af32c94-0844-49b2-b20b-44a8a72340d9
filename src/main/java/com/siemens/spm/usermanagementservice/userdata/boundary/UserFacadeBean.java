/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : UserFacade.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.userdata.boundary;

import com.siemens.spm.common.agency.master.AgencySchemaReadService;
import com.siemens.spm.common.shared.vo.SimpleResultObject;
import com.siemens.spm.common.shared.vo.SimpleResultObject.SimpleStatusCode;
import com.siemens.spm.usermanagementservice.api.boundary.UserService;
import com.siemens.spm.usermanagementservice.api.vo.Expiration;
import com.siemens.spm.usermanagementservice.api.vo.response.UserSimpleListResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.UsersForAlarmSummaryResultObject;
import com.siemens.spm.usermanagementservice.userdata.strategy.UserManagementStrategy;
import com.siemens.spm.usermanagementservice.userdata.strategy.UserSearchStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional
public class UserFacadeBean implements UserService {

    @Autowired
    private UserManagementStrategy userStrategy;

    @Autowired
    private UserSearchStrategy searchStrategy;

    @Autowired
    private AgencySchemaReadService agencySchemaReadService;

    /**
     * {@inheritDoc}
     */
    @Override
    public SimpleResultObject scanExpirationDataInternal(List<Expiration> expList) {
        agencySchemaReadService.fetchActiveAgencyIds()
                .forEach(agencyId -> userStrategy.scanExpirationDataInternalAsync(agencyId, expList));
        return new SimpleResultObject(SimpleStatusCode.SUCCESS);
    }

    @Override
    public UsersForAlarmSummaryResultObject getUsersForAlarmSummaryInternal(Integer agencyId) {
        return searchStrategy.getUsersForAlarmSummaryInternal(agencyId);
    }

    @Override
    public UserSimpleListResultObject getAllSimpleUsers(Integer agencyId) {
        return searchStrategy.getAllSimpleUsers(agencyId);
    }

}
