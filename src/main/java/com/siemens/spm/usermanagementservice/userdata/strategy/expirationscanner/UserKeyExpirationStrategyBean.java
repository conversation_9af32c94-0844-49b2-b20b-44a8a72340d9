package com.siemens.spm.usermanagementservice.userdata.strategy.expirationscanner;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.siemens.spm.common.shared.domaintype.ActionTarget;
import com.siemens.spm.common.shared.domaintype.ActionType;
import com.siemens.spm.common.shared.domaintype.NotificationActionData;
import com.siemens.spm.common.shared.domaintype.notification.NotificationType;
import com.siemens.spm.common.shared.vo.ActionVO;
import com.siemens.spm.common.shared.vo.NotificationMessageRequestVO;
import com.siemens.spm.common.util.DateTimeUtils;
import com.siemens.spm.common.util.NotificationUtil;
import com.siemens.spm.usermanagementservice.api.boundary.NotificationService;
import com.siemens.spm.usermanagementservice.api.vo.UserVO;
import com.siemens.spm.usermanagementservice.api.vo.request.NotificationsCreateRequestVO;
import com.siemens.spm.usermanagementservice.domain.User;
import com.siemens.spm.usermanagementservice.mail.UserMailStrategy;
import com.siemens.spm.usermanagementservice.repository.UserRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@Qualifier(ExpirationStrategy.USER_KEY_EXPIRATION_BEAN)
@Transactional
public class UserKeyExpirationStrategyBean implements ExpirationStrategy {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserMailStrategy userMailStrategy;

    @Autowired
    private NotificationService notificationService;

    @Override
    public void scanExpiration(Date from, Date to) {
        List<User> users = userRepository.findAllByUserKeyExpirationBetween(from, to);
        if (users == null || users.isEmpty()) {
            log.debug("No users was found!");
            return;
        }

        for (User user : users) {
            Date expirationUserKey = user.getUserKeyExpiration();
            long remainingDays = DateTimeUtils.diff(expirationUserKey, from);

            UserVO userVO = UserVO.builder()
                    .userId(user.getId())
                    .email(user.getEmail())
                    .build();

            // send email
            userMailStrategy.sendUserKeyExpirationNotification(userVO, (int) remainingDays);

            // send notification
            notificationService.createNotificationsForUsersInternal(createNotification(userVO, remainingDays));
        }
    }

    private NotificationsCreateRequestVO createNotification(UserVO userVO, long remainingDays) {
        String[] contentArgs = new String[] { String.valueOf(remainingDays) };
        List<NotificationMessageRequestVO> notificationMessageVOs = NotificationUtil.createNotificationMessage(
                "user-key-expiration.title", null, "user-key-expiration.notification", contentArgs);

        ActionVO actionVO = ActionVO.builder()
                .data(NotificationActionData.USER_KEY_TAB.getText())
                .target(ActionTarget.USER_PANEL)
                .type(ActionType.OPEN_SIDE_PANEL)
                .build();

        List<Long> userIds = new ArrayList<>();
        userIds.add(userVO.getUserId());

        return NotificationsCreateRequestVO.builder()
                .action(actionVO)
                .notificationMessages(notificationMessageVOs)
                .typeId(NotificationType.OTHER.getId())
                .userIds(userIds.stream().map(Long::intValue).toList())
                .build();
    }
}
