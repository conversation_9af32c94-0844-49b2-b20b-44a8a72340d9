package com.siemens.spm.usermanagementservice.userdata.util;

import jakarta.validation.constraints.NotNull;

import com.siemens.spm.usermanagementservice.api.vo.NotiSettingVO;
import com.siemens.spm.usermanagementservice.domain.NotiSetting;

public class NotiSettingVOBuilder {

    private NotiSettingVOBuilder() {
    }

    public static NotiSettingVO buildVO(@NotNull NotiSetting notiSetting) {
        return NotiSettingVO.builder()
                .alarmSummaryEnabled(notiSetting.isAlarmSummaryEnabled())
                .alarmSummaryFrequency(notiSetting.getAlarmSummaryFrequency())
                .build();
    }

}
