/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AvatarUtil.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.userdata.util;

import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.Date;

import javax.imageio.ImageIO;

import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

public final class AvatarUtil {

    private AvatarUtil() {
    }

    // in bytes (4M)
    private static final Long ALLOWED_MAX_SIZE = 4194304L;

    private static final String[] ALLOWED_FILE_TYPE = { "png", "jpg", "jpeg" };

    // in pixels
    private static final Integer ALLOWED_HEIGHT_MIN = 160;
    private static final Integer ALLOWED_HEIGHT_MAX = 1000;
    private static final Integer ALLOWED_WIDTH_MIN = 160;
    private static final Integer ALLOWED_WIDTH_MAX = 1000;

    private static final String AVATAR_NAME_PREFIX = "avatar_";

    public static boolean isAllowSize(MultipartFile avatar) {
        return avatar == null || avatar.getSize() <= ALLOWED_MAX_SIZE;
    }

    public static boolean isAllowFileType(MultipartFile avatar) {
        if (avatar == null) {
            return false;
        }

        String fileName = avatar.getOriginalFilename();
        if (fileName == null || !fileName.contains(".")) {
            return false;
        }
        String[] fileNameParts = fileName.split("\\.");
        // fileNameParts is always has at least 1 element
        String extension = fileNameParts[fileNameParts.length - 1];
        return Arrays.asList(ALLOWED_FILE_TYPE).contains(extension);

    }

    public static boolean isAllowDimensions(MultipartFile avatar) {
        try {
            byte[] image = avatar.getBytes();

            InputStream in = new ByteArrayInputStream(image);
            BufferedImage originalImage = ImageIO.read(in);

            int height = originalImage.getHeight();
            int width = originalImage.getWidth();

            if (ALLOWED_HEIGHT_MIN <= height && height <= ALLOWED_HEIGHT_MAX
                    && ALLOWED_WIDTH_MIN <= width && width <= ALLOWED_WIDTH_MAX) {
                return true;
            }
        } catch (Exception e) {
            return false;
        }

        return false;
    }

    public static BufferedImage cropSquareAvatar(MultipartFile avatar) throws IOException {

        if (avatar == null)
            return null;

        byte[] image = avatar.getBytes();

        InputStream in = new ByteArrayInputStream(image);
        BufferedImage originalImage = ImageIO.read(in);

        int height = originalImage.getHeight();
        int width = originalImage.getWidth();

        if (height == width) {
            return originalImage;
        }

        int squareSize = (Math.min(height, width));

        int xc = width / 2;
        int yc = height / 2;

        return originalImage.getSubimage(
                xc - (squareSize / 2),
                yc - (squareSize / 2),
                squareSize,
                squareSize);
    }

    public static String generateAvatarFileName(MultipartFile multiPart, String prefix) {
        if (multiPart == null || !StringUtils.hasText(prefix)) {
            return null;
        }

        String fileType = ALLOWED_FILE_TYPE[0];

        // Get current file type if it is valid
        String originalFileName = multiPart.getOriginalFilename();

        if (originalFileName != null && originalFileName.contains("\\.")) {
            String[] fileTypes = originalFileName.split("\\.");

            if (fileTypes.length > 1) {
                String currFileType = originalFileName.split("\\.")[1];

                if (Arrays.asList(ALLOWED_FILE_TYPE).contains(currFileType)) {
                    fileType = currFileType;
                }
            }
        }

        return AVATAR_NAME_PREFIX + prefix + "_" + new Date().getTime() + "." + fileType;
    }
}
