/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : DataHubSynchronizer.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.datahubsync.strategy;

import com.siemens.spm.common.agency.utils.AgencyPersistenceConstants;
import com.siemens.spm.common.util.LocationUtil;
import com.siemens.spm.datahub.api.boundary.DataIntegrationService;
import com.siemens.spm.datahub.api.vo.DataHubIntersectionVO;
import com.siemens.spm.datahub.api.vo.DataHubLocationVO;
import com.siemens.spm.datahub.api.vo.response.DataHubIntersectionResponseVO;
import com.siemens.spm.datahub.exception.DataHubException;
import com.siemens.spm.perflog.config.SpmPerfLogConfig;
import com.siemens.spm.perflogcrawler.api.intercom.PerfLogTaskController;
import com.siemens.spm.perflogcrawler.api.vo.PerfLogCrawlTask;
import com.siemens.spm.usermanagementservice.api.vo.LocationVO;
import com.siemens.spm.usermanagementservice.domain.AgencySettings;
import com.siemens.spm.usermanagementservice.domain.Intersection;
import com.siemens.spm.usermanagementservice.repository.AgencySettingsRepository;
import com.siemens.spm.usermanagementservice.repository.IntersectionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class DataHubSynchronizer {

    private final SpmPerfLogConfig perflogConfig;

    private final AgencySettingsRepository agencySettingsRepo;

    private final IntersectionRepository intersectionRepository;

    private final DataIntegrationService dataIntegrationStrategy;

    private final RedisTemplate<String, PerfLogCrawlTask> redisTemplate;

    @Transactional(transactionManager = AgencyPersistenceConstants.AGENCY_TRANSACTION_MANAGER)
    public void syncIntersectionsByAgency(Integer agencyId) {
        log.debug("Starting to synchronize data for agency {}...", agencyId);

        if (agencyId == null) {
            return;
        }

        int page = 0;
        int size = 50;
        int totalPage = Integer.MAX_VALUE;
        List<DataHubIntersectionVO> intersectionVOList = new ArrayList<>();

        while (page < totalPage) {
            DataHubIntersectionResponseVO intersectionResponseVO;

            try {
                intersectionResponseVO = dataIntegrationStrategy.getIntersections(agencyId, page, size);
            } catch (DataHubException e) {
                log.error("Error when getting intersections of Agency, agencyId={}", agencyId, e);
                return;
            }
            if (intersectionResponseVO == null) {
                log.error("Unexpected behaviour, get null response while sync for agency {}", agencyId);
                break;
            }

            // Merge data for the current page
            List<DataHubIntersectionVO> intersectionPage = intersectionResponseVO.getItems();
            if (intersectionPage != null) {
                intersectionVOList.addAll(intersectionPage);
            }

            totalPage = intersectionResponseVO.getTotalPages();

            page++;
        }

        // 6. Update agency boundary
        updateAgencyIntersectionLocations(agencyId, intersectionVOList);

        log.debug("Data synchronization for agency {} has completed!", agencyId);
    }

    @Transactional(transactionManager = AgencyPersistenceConstants.AGENCY_TRANSACTION_MANAGER)
    public void initCache(Integer agencyId, String intUUID, LocalDateTime fromTime, LocalDateTime toTime) {
        Optional<Intersection> intersectionOptional = intersectionRepository.findById(intUUID);

        if (intersectionOptional.isPresent()) {
            List<PerfLogCrawlTask> taskList = createPerfLogCrawlTaskList(agencyId, intersectionOptional.get(),
                    fromTime, toTime);
            for (PerfLogCrawlTask task : taskList) {
                postPerfLogCrawlTask(task);
            }
        }
    }

    /**
     * Update intersection's location for an agency
     *
     * @param agencyId  agencyId need to update
     * @param dhIntList List of {@code DataHubIntersectionVO}
     */
    private void updateAgencyIntersectionLocations(Integer agencyId, List<DataHubIntersectionVO> dhIntList) {
        // NOTE: Currently, we're not handling the case that an intersection is removed from an agency

        log.info("Updating agency intersection locations for agency {}", agencyId);
        if (dhIntList == null || dhIntList.isEmpty()) {
            return;
        }

        Optional<AgencySettings> agencySettingsOpt = agencySettingsRepo.findById(agencyId);
        AgencySettings agencySettings;
        // Return an existed agency setting
        // Create new agency setting basing on default setting
        agencySettings = agencySettingsOpt.orElseGet(() -> AgencySettings.getDefault(agencyId));

        // Initial set of longitude and latitude
        List<LocationVO> intersectionLocations = new ArrayList<>();
        for (DataHubIntersectionVO dhInt : dhIntList) {
            DataHubLocationVO intLocation = dhInt.getLocation();
            // Ignore unassigned location (0, 0)
            if (intLocation.getLatitude() != 0 && intLocation.getLongitude() != 0) {
                intersectionLocations.add(new LocationVO(intLocation.getLatitude(), intLocation.getLongitude()));
            }
        }

        List<Pair<Double, Double>> locationPairs = intersectionLocations
                .stream()
                .distinct()
                .map(locationVO -> Pair.of(locationVO.getLatitude(), locationVO.getLongitude()))
                .toList();
        if (!locationPairs.isEmpty()) {
            Pair<Double, Double> centralPointPair = LocationUtil.findNearestCenterLocation(locationPairs);
            if (!Objects.isNull(centralPointPair)) {
                agencySettings.setCentralIntersectionLatitude(centralPointPair.getFirst());
                agencySettings.setCentralIntersectionLongitude(centralPointPair.getSecond());
            } else {
                log.warn("Cannot find central point for agency {}. Intersections' locations might not be assigned",
                        agencyId);
            }
        }

        // save the agency setting
        agencySettingsRepo.save(agencySettings);
    }

    /**
     * Post a task for crawling PerfLog
     *
     * @param task {@code PerfLogCrawlTask} to post.
     */
    void postPerfLogCrawlTask(PerfLogCrawlTask task) {
        try {
            PerfLogTaskController.invokeEnqueueCrawlTask(redisTemplate, task);
        } catch (Exception e) {
            log.error("Error when queuing PerfLogTask", e);
        }
    }

    List<PerfLogCrawlTask> createPerfLogCrawlTaskList(Integer agencyId,
                                                      Intersection intersection,
                                                      LocalDateTime fromTime,
                                                      LocalDateTime toTime) {
        LocalDateTime roundedFromTime = fromTime.withMinute(0).withSecond(0).withNano(0);
        LocalDateTime roundedToTime = toTime.withMinute(0).withSecond(0).withNano(0);
        int extToTimeHour = toTime.getMinute() > 0 || toTime.getSecond() > 0 || toTime.getNano() > 0 ? 1 : 0;
        if (extToTimeHour > 0) {
            roundedToTime = roundedToTime.plusHours(extToTimeHour);
        }

        ArrayList<PerfLogCrawlTask> taskList = new ArrayList<>();

        LocalDateTime tmpFromTime = roundedFromTime;
        LocalDateTime tmpToTime = tmpFromTime.plusHours(perflogConfig.getCrawlIntervalHours());
        while (tmpFromTime.isBefore(roundedToTime)) {
            taskList.add(createPerfLogCrawlTask(agencyId, intersection, tmpFromTime, tmpToTime));
            tmpFromTime = tmpToTime;
            tmpToTime = tmpFromTime.plusHours(perflogConfig.getCrawlIntervalHours());
        }

        return taskList;
    }

    PerfLogCrawlTask createPerfLogCrawlTask(Integer agencyId,
                                            Intersection intersection,
                                            LocalDateTime fromTime,
                                            LocalDateTime toTime) {
        PerfLogCrawlTask task = new PerfLogCrawlTask();
        task.setAgencyId(agencyId);
        task.setIntUUID(intersection.getId());
        task.setIntName(intersection.getName());
        task.setFromTime(fromTime);
        task.setToTime(toTime);
        task.setCreatedTimestamp(System.currentTimeMillis());
        return task;
    }

}
