/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : DataHubSynchronizerWrapper.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.datahubsync.strategy;

import com.siemens.spm.common.agency.supports.AgencyAware;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * Wrapper object to separate @Async, @AgencyAware and @Transactional for DataHubSynchronizer
 */
@Component
@RequiredArgsConstructor
public class DataHubSynchronizerWrapper {

    private final DataHubSynchronizer dataHubSynchronizer;

    @Async
    @AgencyAware(agencyId = "[0]")
    public void sync(Integer agencyId) {
        dataHubSynchronizer.syncIntersectionsByAgency(agencyId);
    }
}
