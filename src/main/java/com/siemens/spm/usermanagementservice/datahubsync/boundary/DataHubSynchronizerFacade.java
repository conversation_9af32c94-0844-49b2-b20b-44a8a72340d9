/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : DataHubSynchronizerFacade.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.datahubsync.boundary;

import java.time.LocalDateTime;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.siemens.spm.usermanagementservice.api.boundary.DataHubSynchronizerService;
import com.siemens.spm.usermanagementservice.datahubsync.strategy.DataHubSynchronizerStrategy;

@Service
@Transactional
public class DataHubSynchronizerFacade implements DataHubSynchronizerService {

    @Autowired
    private DataHubSynchronizerStrategy synchronizerStrategy;
    
    @Override
    public void syncIntersections() {
        synchronizerStrategy.syncIntersections();
    }

    @Override
    public void initCache(Integer agencyId, String intUUID, LocalDateTime fromTime, LocalDateTime toTime) {
        synchronizerStrategy.initCache(agencyId, intUUID, fromTime, toTime);
    }

}
