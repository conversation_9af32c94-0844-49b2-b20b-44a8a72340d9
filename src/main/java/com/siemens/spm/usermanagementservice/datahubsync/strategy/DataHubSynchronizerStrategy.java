/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : DataHubSynchronizerStrategy.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.datahubsync.strategy;

import com.siemens.spm.spmstudiosdk.constant.AgencyConstant;
import com.siemens.spm.spmstudiosdk.dto.StudioAgencyDto;
import com.siemens.spm.spmstudiosdk.exception.StudioException;
import com.siemens.spm.spmstudiosdk.service.StudioAgencyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
public class DataHubSynchronizerStrategy {

    @Autowired
    private DataHubSynchronizer dataHubSynchronizer;

    @Autowired
    private DataHubSynchronizerWrapper wrapper;

    @Autowired
    private StudioAgencyService studioAgencyService;

    /**
     * This method is used to sync all intersections which belong to Agencies defined in SPM
     */
    public void syncIntersections() {
        log.info("Starting to synchronize Intersections ");
        List<StudioAgencyDto> agencyDtoList;
        try {
            agencyDtoList = studioAgencyService.getAllActiveAgencies();
        } catch (StudioException e) {
            log.error("Error while getting all active agencies from Studio, Cannot Sync Intersection", e);
            return;
        }

        if (agencyDtoList == null || agencyDtoList.isEmpty()) {
            log.info("No active agencies found in Studio, Do not Sync Intersection");
            return;
        }

        for (StudioAgencyDto agencyDto : agencyDtoList) {
            log.info("Starting to synchronize data for agency {}...", agencyDto.getAgencyNo());
            Integer agencyId = agencyDto.getAgencyNo();
            // Ignore System Agency
            if (agencyId != null && agencyId != AgencyConstant.SYSTEM_AGENCY_ID) {
                wrapper.sync(agencyId);
            }
        }
    }

    public void initCache(Integer agencyId, String intUUID, LocalDateTime fromTime, LocalDateTime toTime) {
        dataHubSynchronizer.initCache(agencyId, intUUID, fromTime, toTime);
    }

}
