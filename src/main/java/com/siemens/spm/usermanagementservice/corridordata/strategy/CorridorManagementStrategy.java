/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : CorridorManagementStrategy.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.corridordata.strategy;

import com.siemens.spm.datahub.exception.DataHubIntersectionsException;
import com.siemens.spm.usermanagementservice.api.vo.request.CorridorCreateRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.CorridorStatusUpdateRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.CorridorUpdateRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.CorridorResultObject;

public interface CorridorManagementStrategy {

    /**
     * Create corridor
     *
     * @param createRequest
     * @return CorridorResultObject
     */
    CorridorResultObject createCorridor(CorridorCreateRequestVO createRequest) throws DataHubIntersectionsException;

    /**
     * Update corridor
     *
     * @param updateRequest
     * @return CorridorResultObject
     */
    CorridorResultObject updateCorridor(String corridorId, CorridorUpdateRequestVO updateRequest)
            throws DataHubIntersectionsException;

    /**
     * Update corridor
     *
     * @param corridorId
     * @return CorridorResultObject
     */
    CorridorResultObject getCorridorDetail(String corridorId);

    /**
     * Update corridor status
     *
     * @param requestVO
     * @return CorridorResultObject
     */
    CorridorResultObject updateCorridorStatus(CorridorStatusUpdateRequestVO requestVO);

}
