/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : CorridorSyncStrategyBean.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.corridordata.strategy;

import com.siemens.spm.common.shared.domaintype.CorridorStatus;
import com.siemens.spm.usermanagementservice.domain.Corridor;
import com.siemens.spm.usermanagementservice.repository.CorridorIntersectionRepository;
import com.siemens.spm.usermanagementservice.repository.CorridorRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class CorridorSyncStrategyBean implements CorridorSyncStrategy {

    private final CorridorRepository corridorRepository;

    private final CorridorIntersectionRepository corridorIntersectionRepository;

    /**
     * Disable corridor which has UNAVAILABLE intersections
     *
     * @param inactiveIntersectionId List of UNAVAILABLE intersections' id
     * @return List of updated corridors
     */
    @Override
    public List<Corridor> syncCorridorStatusByIntersectionInactiveIds(Set<String> inactiveIntersectionId) {

        List<Corridor> updatedCorridor = corridorIntersectionRepository.findAllByIntersectionIdIn(
                        inactiveIntersectionId)
                .parallelStream()
                .map(
                        relation -> {
                            Corridor corridor = relation.getCorridor();

                            corridor.setStatus(CorridorStatus.INACTIVE.name());

                            return corridor;
                        }
                ).collect(Collectors.toList());

        return corridorRepository.saveAll(updatedCorridor);
    }
}
