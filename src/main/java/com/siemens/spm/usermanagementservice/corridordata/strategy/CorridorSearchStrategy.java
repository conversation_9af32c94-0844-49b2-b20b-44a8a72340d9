/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : CorridorSearchStrategy.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.corridordata.strategy;

import com.siemens.spm.usermanagementservice.api.vo.request.CorridorListRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.CorridorSearchRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.IntersectionAvailableSearchRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.CorridorInternalSearchResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.CorridorListResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionInternalSearchResultObject;

public interface CorridorSearchStrategy {

    /**
     * Get corridors
     *
     * @param corridorListRequestVO
     * @return CorridorListResultObject
     */
    CorridorListResultObject getCorridors(CorridorListRequestVO corridorListRequestVO);

    /**
     * Search available intersections
     *
     * @param requestVO
     * @return IntersectionInternalSearchResultObject
     */
    IntersectionInternalSearchResultObject searchAvailableIntersections(IntersectionAvailableSearchRequestVO requestVO);

    /**
     * Get all corridors by filter for other service(s)
     *
     * @param searchRequest criteria for searching
     * @return CorridorInternalSearchResultObject
     */
    CorridorInternalSearchResultObject getAllCorridorsByFilterInternal(CorridorSearchRequestVO searchRequest);

}
