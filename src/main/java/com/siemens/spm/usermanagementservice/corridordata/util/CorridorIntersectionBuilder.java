/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : CorridorIntersectionBuilder.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.corridordata.util;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import com.siemens.spm.usermanagementservice.api.vo.request.CorridorIntersectionCreateRequestVO;
import com.siemens.spm.usermanagementservice.domain.Corridor;
import com.siemens.spm.usermanagementservice.domain.CorridorIntersection;
import com.siemens.spm.usermanagementservice.domain.Intersection;

public final class CorridorIntersectionBuilder {

    private CorridorIntersectionBuilder() {
    }

    public static List<CorridorIntersection> buildCorridorIntersections(
            List<CorridorIntersectionCreateRequestVO> corridorIntersectionVOS,
            String corridorId) {
        return corridorIntersectionVOS
                .stream()
                .map(corridorDetailVO -> buildCorridorIntersection(corridorDetailVO, corridorId))
                .toList();
    }

    public static CorridorIntersection buildCorridorIntersection(
            CorridorIntersectionCreateRequestVO corridorIntersectionVO,
            String corridorId) {
        return CorridorIntersection.builder()
                .id(UUID.randomUUID().toString())
                .number(corridorIntersectionVO.getNumber())
                .distance(corridorIntersectionVO.getDistance())
                .speed(corridorIntersectionVO.getSpeed())
                .upstream(corridorIntersectionVO.getUpstream())
                .downstream(corridorIntersectionVO.getDownstream())
                .corridor(
                        Corridor.builder()
                                .id(corridorId)
                                .build()
                )
                .intersection(
                        Intersection.builder()
                                .id(corridorIntersectionVO.getIntersectionId())
                                .build()
                )
                .build();
    }

}
