/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : CorridorBuilder.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.corridordata.util;

import java.util.Comparator;
import java.util.List;
import java.util.UUID;

import com.siemens.spm.common.shared.domaintype.CorridorStatus;
import com.siemens.spm.usermanagementservice.api.vo.CorridorInternalVO;
import com.siemens.spm.usermanagementservice.api.vo.CorridorIntersectionInternalVO;
import com.siemens.spm.usermanagementservice.api.vo.request.CorridorCreateRequestVO;
import com.siemens.spm.usermanagementservice.domain.Corridor;
import com.siemens.spm.usermanagementservice.intersectiondata.util.IntersectionVOBuilder;

public final class CorridorBuilder {

    private CorridorBuilder() {
    }

    public static Corridor buildCorridor(CorridorCreateRequestVO vo) {
        return Corridor.builder()
                .id(UUID.randomUUID().toString())
                .name(vo.getName())
                .status(CorridorStatus.ACTIVE.name())
                .speed(vo.getSpeed())
                .globalUpstreamPhase(vo.getGlobalUpstreamPhase())
                .globalDownstreamPhase(vo.getGlobalDownstreamPhase())
                .agencyId(vo.getAgencyId())
                .build();
    }

    public static CorridorInternalVO buildCorridorInternalVO(Corridor corridor) {
        List<CorridorIntersectionInternalVO> corridorIntersections = corridor.getCorridorIntersections()
                .stream()
                .map(corridorIntersection -> CorridorIntersectionInternalVO.builder()
                        .id(corridorIntersection.getId())
                        .number(corridorIntersection.getNumber())
                        .distance(corridorIntersection.getDistance())
                        .speed(corridorIntersection.getSpeed())
                        .upstream(corridorIntersection.getUpstream())
                        .downstream(corridorIntersection.getDownstream())
                        .intersection(IntersectionVOBuilder.buildIntersectionInternalVO(
                                corridorIntersection.getIntersection()))
                        .build())
                .sorted(Comparator.comparing(CorridorIntersectionInternalVO::getNumber))
                .toList();

        return CorridorInternalVO.builder()
                .id(corridor.getId())
                .name(corridor.getName())
                .status(corridor.getStatus())
                .speed(corridor.getSpeed())
                .globalUpstreamPhase(corridor.getGlobalUpstreamPhase())
                .globalDownstreamPhase(corridor.getGlobalDownstreamPhase())
                .corridorIntersections(corridorIntersections)
                .build();
    }

}
