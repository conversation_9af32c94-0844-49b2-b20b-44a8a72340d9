/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : CorridorSearchHelper.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.corridordata.util;

import java.util.Arrays;
import java.util.List;

import com.querydsl.core.types.OrderSpecifier;
import com.siemens.spm.common.audit.AuditableEntity;
import com.siemens.spm.common.util.OrderUtil;
import com.siemens.spm.usermanagementservice.domain.Corridor;
import com.siemens.spm.usermanagementservice.domain.QCorridor;

public final class CorridorSearchHelper {

    private static final List<String> SORTABLE_COLUMNS = Arrays.asList(
            Corridor.ColumnName.NAME,
            Corridor.ColumnName.STATUS,
            AuditableEntity.ColumnName.LAST_MODIFIED_AT);

    private CorridorSearchHelper() {
    }

    @SuppressWarnings({ "unchecked" })
    public static OrderSpecifier<String>[] createOrderBy(String[] orderByColumns) {
        String entityName = QCorridor.corridor.getMetadata().getName();

        if (orderByColumns == null || orderByColumns.length == 0) {
            String[] defaultOrderByColumns = new String[] {
                    String.format("%s:descend", AuditableEntity.ColumnName.LAST_MODIFIED_AT) };
            return OrderUtil.createOrderBy(entityName, defaultOrderByColumns, SORTABLE_COLUMNS);
        }

        return OrderUtil.createOrderBy(entityName, orderByColumns, SORTABLE_COLUMNS);
    }

}
