/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : CorridorSearchStrategyBean.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.corridordata.strategy;

import com.querydsl.core.types.OrderSpecifier;
import com.siemens.spm.common.shared.domaintype.IntersectionStatus;
import com.siemens.spm.common.shared.exception.InvalidSortColumnException;
import com.siemens.spm.common.shared.exception.InvalidSortOrderException;
import com.siemens.spm.common.util.ListUtil;
import com.siemens.spm.spmstudiosdk.dto.StudioUserDto;
import com.siemens.spm.spmstudiosdk.exception.StudioException;
import com.siemens.spm.spmstudiosdk.service.StudioUserService;
import com.siemens.spm.usermanagementservice.api.vo.CorridorInternalListVO;
import com.siemens.spm.usermanagementservice.api.vo.CorridorInternalVO;
import com.siemens.spm.usermanagementservice.api.vo.CorridorListVO;
import com.siemens.spm.usermanagementservice.api.vo.CorridorOwnerVO;
import com.siemens.spm.usermanagementservice.api.vo.CorridorVO;
import com.siemens.spm.usermanagementservice.api.vo.request.CorridorListRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.CorridorSearchRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.IntersectionAvailableSearchRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.IntersectionSearchRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.CorridorInternalSearchResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.CorridorListResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.CorridorListResultObject.CorridorsStatusCode;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionInternalSearchResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionInternalSearchResultObject.StatusCode;
import com.siemens.spm.usermanagementservice.corridordata.util.CorridorBuilder;
import com.siemens.spm.usermanagementservice.corridordata.util.CorridorSearchHelper;
import com.siemens.spm.usermanagementservice.corridordata.util.CorridorVOBuilder;
import com.siemens.spm.usermanagementservice.domain.Corridor;
import com.siemens.spm.usermanagementservice.domain.CorridorIntersection;
import com.siemens.spm.usermanagementservice.domain.Intersection;
import com.siemens.spm.usermanagementservice.intersectiondata.strategy.IntersectionSearchStrategyBean;
import com.siemens.spm.usermanagementservice.repository.CorridorRepository;
import com.siemens.spm.usermanagementservice.repository.filterdata.CorridorFilterDataVO;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional
public class CorridorSearchStrategyBean implements CorridorSearchStrategy {

    @Autowired
    private CorridorRepository corridorRepository;

    @Autowired
    private IntersectionSearchStrategyBean intersectionStrategy;

    @Autowired
    private StudioUserService studioUserService;

    /**
     * {@inheritDoc}
     */
    @Override
    public CorridorListResultObject getCorridors(CorridorListRequestVO corridorListRequestVO) {
        Integer agencyId = corridorListRequestVO.getAgencyId();
        CorridorFilterDataVO filterDataVO = CorridorFilterDataVO.builder()
                .agencyId(agencyId)
                .text(corridorListRequestVO.getText())
                .status(corridorListRequestVO.getStatus())
                .shouldPaginate(corridorListRequestVO.isShouldPaginate())
                .build();

        List<Corridor> corridors;
        Long totalCount;
        try {
            OrderSpecifier<String>[] orderSpecifiers = CorridorSearchHelper
                    .createOrderBy(corridorListRequestVO.getOrderByColumns());

            corridors = corridorRepository.findPageByFilter(filterDataVO, orderSpecifiers,
                    corridorListRequestVO.getPage(), corridorListRequestVO.getSize());
            totalCount = corridorRepository.countTotalByFilter(filterDataVO);
        } catch (InvalidSortColumnException e) {
            log.error("Invalid sort column", e);
            return new CorridorListResultObject(null, CorridorsStatusCode.INVALID_SORT_COLUMN);
        } catch (InvalidSortOrderException e) {
            log.error("Invalid sort order", e);
            return new CorridorListResultObject(null, CorridorsStatusCode.INVALID_SORT_ORDER);
        }
        Map<String, StudioUserDto> studioUserDtoMap;
        try {
            studioUserDtoMap = studioUserService.findAllByAgencyId(agencyId)
                    .stream()
                    .collect(Collectors.toMap(StudioUserDto::getEmail, Function.identity(), (a, b) -> {
                        log.warn("Duplicate user from Studio" + a);
                        return a;
                    }));
        } catch (StudioException e) {
            log.error("Error while getting studio user services", e);
            studioUserDtoMap = Map.of();
        }

        List<CorridorVO> corridorVOList = CorridorVOBuilder.buildCorridorVOList(corridors);
        this.updateOwnerData(corridorVOList, studioUserDtoMap);

        CorridorListVO corridorListVO = CorridorListVO.builder()
                .corridors(corridorVOList)
                .totalCount(totalCount)
                .build();
        return new CorridorListResultObject(corridorListVO, CorridorsStatusCode.SUCCESS);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public IntersectionInternalSearchResultObject searchAvailableIntersections(@Valid IntersectionAvailableSearchRequestVO requestVO) {
        /*knt:2024-11-25
         * Get all selectable intersections for the corridor
         * - status = AVAILABLE
         * - exclude "selected" intersections (with any status) of specified corridor.
         */

        Integer agencyId = requestVO.getAgencyId();
        String corridorId = requestVO.getCorridorId();

        // Corridor not found
        Corridor corridor = null;
        if (StringUtils.hasText(corridorId)) {
            Optional<Corridor> optionalCorridor = corridorRepository.findById(corridorId);
            if (optionalCorridor.isEmpty()) {
                log.error("Corridor not found for corridorId: {}", corridorId);
                return new IntersectionInternalSearchResultObject(null, StatusCode.CORRIDOR_NOT_FOUND);
            }
            corridor = optionalCorridor.get();
        }

        List<String> intersectionIds = null;
        if (Objects.nonNull(corridor)) {
            // Corridor not belong to agency
            if (!corridor.getAgencyId().equals(agencyId)) {
                log.error("Corridor {} not belong to agency {}", corridorId, agencyId);
                return new IntersectionInternalSearchResultObject(null, StatusCode.CORRIDOR_NOT_BELONG_TO_AGENCY);
            }

            // Get list intersection ids from corridor
            List<CorridorIntersection> corridorIntersections = corridor.getCorridorIntersections();
            if (!CollectionUtils.isEmpty(corridorIntersections)) {
                intersectionIds = corridorIntersections.stream()
                        .map(CorridorIntersection::getIntersection)
                        .map(Intersection::getId)
                        .distinct()
                        .toList();
            }
        }

        IntersectionSearchRequestVO searchRequestVO = IntersectionSearchRequestVO.builder()
                .agencyId(agencyId)
                .exclusionaryIds(intersectionIds)
                .status(IntersectionStatus.AVAILABLE.name())
                .text(requestVO.getText())
                .orderByColumns(requestVO.getOrderByColumns())
                .shouldPaginate(true)
                .page(requestVO.getPage())
                .size(requestVO.getSize())
                .build();

        return intersectionStrategy.searchIntersectionsInternal(searchRequestVO);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CorridorInternalSearchResultObject getAllCorridorsByFilterInternal(CorridorSearchRequestVO searchRequest) {
        CorridorFilterDataVO corridorFilterDataVO = CorridorFilterDataVO.builder()
                .agencyId(searchRequest.getAgencyId())
                .ids(searchRequest.getCorridorIds())
                .excludeIds(searchRequest.getExcludeCorridorIds())
                .text(searchRequest.getText())
                .status(searchRequest.getStatus())
                .build();

        List<Corridor> corridors = corridorRepository.findPageByFilter(corridorFilterDataVO, null,
                searchRequest.getPage(), searchRequest.getSize());
        Long totalCount = corridorRepository.countTotalByFilter(corridorFilterDataVO);

        List<CorridorInternalVO> corridorInternalVOList = corridors.stream()
                .map(CorridorBuilder::buildCorridorInternalVO)
                .toList();

        CorridorInternalListVO responseData = CorridorInternalListVO.builder()
                .totalCount(totalCount)
                .corridors(corridorInternalVOList)
                .build();

        return new CorridorInternalSearchResultObject(responseData,
                CorridorInternalSearchResultObject.StatusCode.SUCCESS);
    }

    private void updateOwnerData(List<CorridorVO> corridorVOList, Map<String, StudioUserDto> studioUserDtoMap) {
        if (ListUtil.hasNoItem(corridorVOList)) {
            return;
        }

        for (CorridorVO corridorVO : corridorVOList) {
            String ownerEmail = corridorVO.getCreatedBy();
            StudioUserDto ownerStudioUserDto = studioUserDtoMap.get(ownerEmail);
            if (ownerStudioUserDto != null) {
                corridorVO.setOwnerId(Long.valueOf(ownerStudioUserDto.getId()));
                corridorVO.setOwner(buildCorridorOwnerVO(ownerStudioUserDto));
            }

            String lastModifierEmail = corridorVO.getLastModifiedBy();
            StudioUserDto lastModifierStudioUserDto = studioUserDtoMap.get(lastModifierEmail);
            if (lastModifierStudioUserDto != null) {
                corridorVO.setLastModifierId(Long.valueOf(lastModifierStudioUserDto.getId()));
                corridorVO.setLastModifier(buildCorridorOwnerVO(lastModifierStudioUserDto));
            }
        }
    }

    private CorridorOwnerVO buildCorridorOwnerVO(StudioUserDto studioUserDto) {
        return CorridorOwnerVO.builder()
                .id(Long.valueOf(studioUserDto.getId()))
                .email(studioUserDto.getEmail())
                .name(studioUserDto.getFirstName() + " " + studioUserDto.getLastName())
                .build();
    }

}
