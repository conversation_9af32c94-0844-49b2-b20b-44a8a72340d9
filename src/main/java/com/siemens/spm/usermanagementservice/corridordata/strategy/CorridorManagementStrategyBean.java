/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : CorridorManagementStrategyBean.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.corridordata.strategy;

import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import com.siemens.spm.common.shared.domaintype.IntersectionStatus;

import com.siemens.spm.datahub.api.boundary.DataIntegrationService;
import com.siemens.spm.datahub.api.vo.DataHubIntersectionVO;
import com.siemens.spm.datahub.api.vo.response.DataHubIntersectionResponseVO;
import com.siemens.spm.datahub.exception.DataHubIntersectionsException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.siemens.spm.common.constant.MessageConstants;
import com.siemens.spm.common.shared.domaintype.CorridorStatus;
import com.siemens.spm.common.util.ListUtil;
import com.siemens.spm.spmstudiosdk.dto.StudioAgencyDto;
import com.siemens.spm.spmstudiosdk.exception.StudioException;
import com.siemens.spm.spmstudiosdk.service.StudioAgencyService;
import com.siemens.spm.usermanagementservice.api.vo.CorridorVO;
import com.siemens.spm.usermanagementservice.api.vo.request.CorridorCreateRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.CorridorIntersectionCreateRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.CorridorStatusUpdateRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.CorridorUpdateRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.CorridorResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.CorridorResultObject.CorridorStatusCode;
import com.siemens.spm.usermanagementservice.corridordata.util.CorridorBuilder;
import com.siemens.spm.usermanagementservice.corridordata.util.CorridorIntersectionBuilder;
import com.siemens.spm.usermanagementservice.corridordata.util.CorridorVOBuilder;
import com.siemens.spm.usermanagementservice.domain.Corridor;
import com.siemens.spm.usermanagementservice.domain.CorridorIntersection;
import com.siemens.spm.usermanagementservice.domain.Intersection;
import com.siemens.spm.usermanagementservice.repository.CorridorIntersectionRepository;
import com.siemens.spm.usermanagementservice.repository.CorridorRepository;
import com.siemens.spm.usermanagementservice.repository.IntersectionRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;

@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class CorridorManagementStrategyBean implements CorridorManagementStrategy {

    private final IntersectionRepository intersectionRepository;

    private final CorridorRepository corridorRepository;

    private final CorridorIntersectionRepository corridorIntersectionRepository;

    private final StudioAgencyService studioAgencyService;

    private final DataIntegrationService dataIntegrationService;

    private static final int MINIMUM_CORRIDOR_INTERSECTION = 2;

    @Override
    public CorridorResultObject createCorridor(CorridorCreateRequestVO createRequest)
            throws DataHubIntersectionsException {
        String corridorName = createRequest.getName();
        Integer agencyId = createRequest.getAgencyId();
        List<CorridorIntersectionCreateRequestVO> corridorIntersectionCreateRequestVOS = createRequest.getCorridorIntersections();

        Optional<CorridorResultObject> resultObject = validateCreateRequest(corridorName, agencyId,
                corridorIntersectionCreateRequestVOS);
        if (resultObject.isPresent()) {
            log.error("Invalid create corridor request");

            return resultObject.get();
        }

        try {
            Optional<StudioAgencyDto> agencyDtoOptional = studioAgencyService.getAgencyById(agencyId);
            if (agencyDtoOptional.isEmpty()) {
                log.warn("Not found agency (id = {}) from Studio", agencyId);
                return new CorridorResultObject(null, CorridorStatusCode.AGENCY_NOT_FOUND);
            }
        } catch (StudioException e) {
            log.error("Error while getting agency from Studio", e);
            return new CorridorResultObject(null, CorridorStatusCode.ERROR);
        }

        // Save corridor
        Corridor corridor = CorridorBuilder.buildCorridor(createRequest);
        corridor = corridorRepository.save(corridor);

        // Save corridor intersections
        List<CorridorIntersection> corridorIntersections = this.createCorridorIntersections(
                corridorIntersectionCreateRequestVOS,
                corridor.getId()
        );
        corridor.setCorridorIntersections(corridorIntersections);

        CorridorVO corridorVO = CorridorVOBuilder.buildCorridorVO(corridor);

        return new CorridorResultObject(corridorVO, CorridorStatusCode.CREATED);
    }

    @Override
    public CorridorResultObject updateCorridor(String corridorId, CorridorUpdateRequestVO updateRequest)
            throws DataHubIntersectionsException {
        // Not found corridor
        Optional<Corridor> optionalCorridor = this.getCorridor(corridorId);
        if (optionalCorridor.isEmpty()) {
            log.error(String.format(MessageConstants.CORRIDOR_X_NOT_FOUND, corridorId));

            return new CorridorResultObject(null, CorridorStatusCode.CORRIDOR_NOT_FOUND);
        }
        Corridor corridor = optionalCorridor.get();

        String updateRequestCorridorName = updateRequest.getName();
        Double speed = updateRequest.getSpeed();
        Integer globalUpstreamPhase = updateRequest.getGlobalUpstreamPhase();
        Integer globalDownstreamPhase = updateRequest.getGlobalDownstreamPhase();

        String corridorName = corridor.getName();
        Integer agencyId = corridor.getAgencyId();
        List<CorridorIntersectionCreateRequestVO> corridorIntersectionVOS = updateRequest.getCorridorIntersections();

        Optional<CorridorResultObject> resultObject =
                validateUpdateRequest(updateRequest, corridorName, agencyId);

        if (resultObject.isPresent()) {
            log.error("Invalid update corridor request");
            return resultObject.get();
        }

        // Update corridor
        corridor.setName(updateRequestCorridorName);
        corridor.setSpeed(speed);
        corridor.setGlobalUpstreamPhase(globalUpstreamPhase);
        corridor.setGlobalDownstreamPhase(globalDownstreamPhase);
        corridorRepository.save(corridor);

        // Delete all corridor intersections by corridor
        corridorIntersectionRepository.deleteAllByCorridor(corridor);
        // Insert new corridor intersections
        List<CorridorIntersection> createdCorridorIntersections = this.createCorridorIntersections(
                corridorIntersectionVOS,
                corridorId
        );
        corridor.setCorridorIntersections(createdCorridorIntersections);

        CorridorVO corridorVO = CorridorVOBuilder.buildCorridorVO(corridor);
        return new CorridorResultObject(corridorVO, CorridorStatusCode.SUCCESS);
    }

    @Override
    public CorridorResultObject getCorridorDetail(String corridorId) {
        Optional<Corridor> optionalCorridor = this.getCorridor(corridorId);
        if (optionalCorridor.isEmpty()) {
            log.error(String.format(MessageConstants.CORRIDOR_X_NOT_FOUND, corridorId));
            return new CorridorResultObject(null, CorridorStatusCode.CORRIDOR_NOT_FOUND);
        }
        Corridor corridor = optionalCorridor.get();
        CorridorVO corridorVO = CorridorVOBuilder.buildCorridorVO(corridor);

        return new CorridorResultObject(corridorVO, CorridorStatusCode.SUCCESS);
    }

    @Override
    public CorridorResultObject updateCorridorStatus(CorridorStatusUpdateRequestVO requestVO) {
        String newStatus = requestVO.getStatus();
        List<String> requestIds = requestVO.getIds();

        if (!EnumUtils.isValidEnum(CorridorStatus.class, newStatus)) {
            log.error("Corridor status invalid");
            return new CorridorResultObject(null, CorridorStatusCode.CORRIDOR_STATUS_INVALID);
        }

        if (ListUtil.hasItem(requestIds)) {
            List<String> ids = requestIds.stream()
                    .distinct()
                    .toList();
            List<Corridor> corridors = corridorRepository.findAllById(ids);

            List<String> foundIds = corridors.stream()
                    .map(Corridor::getId)
                    .toList();

            if (ids.size() != foundIds.size()) {
                String notFoundIds = this.getNotFoundIds(ids, foundIds);
                if (!notFoundIds.isBlank()) {
                    log.error(String.format(MessageConstants.CORRIDOR_X_NOT_FOUND, notFoundIds));
                    return new CorridorResultObject(null, CorridorStatusCode.CORRIDOR_NOT_FOUND);
                }
            }

            // Validate disabled intersection
            if (CorridorStatus.ACTIVE.name().equals(newStatus)) {
                var isDisabledIntersectionExisted = corridors.stream()
                        .map(Corridor::getCorridorIntersections)
                        .flatMap(Collection::stream)
                        .map(CorridorIntersection::getIntersection)
                        .anyMatch(intersection -> IntersectionStatus.UNAVAILABLE.getInsight().equals(intersection.getStatus()));

                if (isDisabledIntersectionExisted) {
                    return new CorridorResultObject(null, CorridorStatusCode.CORRIDOR_CONTAINS_DISABLED_INTERSECTIONS);
                }
            }

            corridors.forEach(corridor -> corridor.setStatus(newStatus));
            corridorRepository.saveAll(corridors);
        }

        return new CorridorResultObject(null, CorridorStatusCode.NO_CONTENT);
    }

    private Optional<CorridorResultObject> validateCreateRequest(String corridorName,
                                                                 Integer agencyId,
                                                                 List<CorridorIntersectionCreateRequestVO> corridorIntersectionVOS)
            throws DataHubIntersectionsException {
        if (this.existCorridorName(corridorName)) {
            log.error("Corridor name existed ({})", corridorName);
            return Optional.of(new CorridorResultObject(null, CorridorStatusCode.CORRIDOR_NAME_DUPLICATED));
        }

        return validateCreateCorridorIntersectionRequest(agencyId, corridorIntersectionVOS);
    }

    private Optional<CorridorResultObject> validateUpdateRequest(CorridorUpdateRequestVO updateRequest,
                                                                 String corridorName,
                                                                 Integer agencyId)
            throws DataHubIntersectionsException {
        String updateRequestCorridorName = updateRequest.getName();
        Double speed = updateRequest.getSpeed();
        Integer globalUpstreamPhase = updateRequest.getGlobalUpstreamPhase();
        Integer globalDownstreamPhase = updateRequest.getGlobalDownstreamPhase();
        List<CorridorIntersectionCreateRequestVO> corridorIntersectionVOS = updateRequest.getCorridorIntersections();

        if (!StringUtils.hasText(updateRequestCorridorName)) {
            return Optional.of(new CorridorResultObject(null, CorridorStatusCode.NAME_NOT_NULL));
        }
        if (speed == null) {
            return Optional.of(new CorridorResultObject(null, CorridorStatusCode.SPEED_NOT_NULL));
        }
        if (globalUpstreamPhase == null) {
            log.error("Global upstream phase can not be null");
            return Optional.of(new CorridorResultObject(null, CorridorStatusCode.GLOBAL_UPSTREAM_NOT_NULL));
        }
        if (globalDownstreamPhase == null) {
            return Optional.of(new CorridorResultObject(null, CorridorStatusCode.GLOBAL_DOWNSTREAM_NOT_NULL));
        }
        if (globalUpstreamPhase < 1 || globalUpstreamPhase > 16 ||
                globalDownstreamPhase < 1 || globalDownstreamPhase > 16) {
            return Optional.of(new CorridorResultObject(null, CorridorStatusCode.INVALID_STREAM_PHASE_NUMBER));
        }

        // Duplicate corridor name
        if (!updateRequestCorridorName.equals(corridorName) && existCorridorName(updateRequestCorridorName)) {
            return Optional.of(new CorridorResultObject(null, CorridorStatusCode.CORRIDOR_NAME_DUPLICATED));
        }

        return validateCreateCorridorIntersectionRequest(agencyId, corridorIntersectionVOS);
    }

    private Optional<CorridorResultObject> validateCorridorIntersectionVOS(
            List<CorridorIntersectionCreateRequestVO> corridorIntersectionVOS) {
        if (ListUtil.hasNoItem(corridorIntersectionVOS)
                || corridorIntersectionVOS.size() < MINIMUM_CORRIDOR_INTERSECTION) {
            return Optional.of(new CorridorResultObject(null, CorridorStatusCode.INVALID_CORRIDOR_INTERSECTIONS_SIZE));
        }

        CorridorIntersectionCreateRequestVO lastCorridorIntersectionVO = corridorIntersectionVOS
                .stream()
                .max(Comparator.comparing(CorridorIntersectionCreateRequestVO::getNumber))
                .orElse(null);

        Objects.requireNonNull(lastCorridorIntersectionVO);

        if (lastCorridorIntersectionVO.getDistance().intValue() != 0) {
            return Optional.of(
                    new CorridorResultObject(null, CorridorStatusCode.INVALID_LAST_CORRIDOR_INTERSECTION_DISTANCE));
        }

        return Optional.empty();
    }

    private Optional<Corridor> getCorridor(String corridorId) {
        return corridorRepository.findById(corridorId);
    }

    public boolean existCorridorName(String corridorName) {
        return corridorRepository.existsByName(corridorName);
    }

    private Optional<CorridorResultObject> validateCreateCorridorIntersectionRequest(
            Integer agencyId,
            List<CorridorIntersectionCreateRequestVO> corridorIntersectionVOS
    ) throws DataHubIntersectionsException {
        Optional<CorridorResultObject> resultObject = this.validateCorridorIntersectionVOS(corridorIntersectionVOS);
        if (resultObject.isPresent()) {
            log.error("Invalid corridor intersections vos");
            return resultObject;
        }

        List<String> intersectionIds = corridorIntersectionVOS
                .stream()
                .map(CorridorIntersectionCreateRequestVO::getIntersectionId)
                .distinct()
                .toList();
        List<Intersection> intersections = intersectionRepository.findAllByIdIn(intersectionIds);

        // Intersections not found
        if (intersections.size() != intersectionIds.size()) {

            List<String> foundIntersectionIds = intersections.stream()
                    .map(Intersection::getId)
                    .toList();

            String notFoundIntersectionIds = this.getNotFoundIds(intersectionIds, foundIntersectionIds);

            if (!notFoundIntersectionIds.isBlank()) {
                return Optional.of(new CorridorResultObject(null, CorridorStatusCode.INTERSECTION_NOT_FOUND));
            }
        }

        // Contains disabled intersection
        boolean isDisabledIntersectionExisted = intersections.stream()
                .anyMatch(c -> IntersectionStatus.UNAVAILABLE.name().equals(c.getStatus()));

        if (isDisabledIntersectionExisted) {
            return Optional.of(new CorridorResultObject(null, CorridorStatusCode.CORRIDOR_CONTAINS_DISABLED_INTERSECTIONS));
        }

        // Intersections not belong to agency

        DataHubIntersectionResponseVO response = dataIntegrationService.getAllIntersections(agencyId);

        List<String> foundIntersectionIdsOfAgency = response.getItems().stream()
                .map(DataHubIntersectionVO::getId)
                .toList();

        String intersectionIdsNotBelongToAgency = this.getNotFoundIds(intersectionIds, foundIntersectionIdsOfAgency);

        if (!intersectionIdsNotBelongToAgency.isBlank()) {
            log.error("Intersections {} not belong to agency {}", intersectionIdsNotBelongToAgency, agencyId);
            return Optional.of(new CorridorResultObject(null, CorridorStatusCode.INTERSECTION_NOT_BELONG_TO_AGENCY));
        }

        return Optional.empty();
    }

    private String getNotFoundIds(List<String> requestIds, List<String> foundIds) {
        return requestIds.stream()
                .filter(id -> !foundIds.contains(id))
                .collect(Collectors.joining(","));
    }

    private List<CorridorIntersection> createCorridorIntersections(
            List<CorridorIntersectionCreateRequestVO> corridorIntersectionCreateRequestVOS,
            String corridorId) {
        List<CorridorIntersection> corridorIntersections = CorridorIntersectionBuilder.buildCorridorIntersections(
                corridorIntersectionCreateRequestVOS,
                corridorId
        );
        return corridorIntersectionRepository.saveAll(corridorIntersections);
    }

}
