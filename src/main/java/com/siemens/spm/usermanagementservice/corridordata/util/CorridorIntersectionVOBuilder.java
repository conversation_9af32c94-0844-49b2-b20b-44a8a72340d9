/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : CorridorIntersectionVOBuilder.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.corridordata.util;

import java.util.Comparator;
import java.util.List;

import com.siemens.spm.usermanagementservice.api.vo.CorridorIntersectionVO;
import com.siemens.spm.usermanagementservice.domain.CorridorIntersection;
import com.siemens.spm.usermanagementservice.intersectiondata.util.IntersectionVOBuilder;

public class CorridorIntersectionVOBuilder {

    private CorridorIntersectionVOBuilder() {
    }

    public static List<CorridorIntersectionVO> buildCorridorIntersectionVOS(List<CorridorIntersection> corridorIntersections) {
        return corridorIntersections.stream()
                .map(CorridorIntersectionVOBuilder::buildCorridorIntersectionVO)
                .sorted(Comparator.comparing(CorridorIntersectionVO::getNumber))
                .toList();
    }

    public static CorridorIntersectionVO buildCorridorIntersectionVO(CorridorIntersection corridorIntersection) {
        return CorridorIntersectionVO.builder()
                .id(corridorIntersection.getId())
                .number(corridorIntersection.getNumber())
                .speed(corridorIntersection.getSpeed())
                .distance(corridorIntersection.getDistance())
                .upstream(corridorIntersection.getUpstream())
                .downstream(corridorIntersection.getDownstream())
                .intersection(IntersectionVOBuilder.buildIntersectionSearchVO(corridorIntersection.getIntersection()))
                .build();
    }

}
