/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : CorridorVOBuilder.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.corridordata.util;

import java.util.Collections;
import java.util.List;

import com.siemens.spm.common.util.ListUtil;
import com.siemens.spm.usermanagementservice.api.vo.CorridorIntersectionVO;
import com.siemens.spm.usermanagementservice.api.vo.CorridorVO;
import com.siemens.spm.usermanagementservice.domain.Corridor;
import com.siemens.spm.usermanagementservice.domain.CorridorIntersection;

public final class CorridorVOBuilder {

    private CorridorVOBuilder() {
    }

    public static List<CorridorVO> buildCorridorVOList(List<Corridor> corridors) {
        return corridors.stream()
                .map(CorridorVOBuilder::buildCorridorVO)
                .toList();
    }

    public static CorridorVO buildCorridorVO(Corridor corridor) {
        CorridorVO corridorVO = CorridorVO.builder()
                .id(corridor.getId())
                .name(corridor.getName())
                .status(corridor.getStatus())
                .agencyId(corridor.getAgencyId())
                .speed(corridor.getSpeed())
                .globalUpstreamPhase(corridor.getGlobalUpstreamPhase())
                .globalDownstreamPhase(corridor.getGlobalDownstreamPhase())
                .createdBy(corridor.getCreatedBy())
                .createdAt(corridor.getCreatedAt())
                .lastModifiedBy(corridor.getLastModifiedBy())
                .lastModifiedAt(corridor.getLastModifiedAt())
                .build();

        List<CorridorIntersection> corridorIntersections = corridor.getCorridorIntersections();
        if (ListUtil.hasItem(corridorIntersections)) {
            List<CorridorIntersectionVO> corridorIntersectionVOS = CorridorIntersectionVOBuilder
                    .buildCorridorIntersectionVOS(corridorIntersections);
            corridorVO.setCorridorIntersectionVOS(corridorIntersectionVOS);
            corridorVO.setNumberOfCorridorIntersection(corridorIntersectionVOS.size());
        } else {
            corridorVO.setCorridorIntersectionVOS(Collections.emptyList());
            corridorVO.setNumberOfCorridorIntersection(0);
        }
        return corridorVO;
    }

}
