/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : CorridorSyncStrategy.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.corridordata.strategy;

import com.siemens.spm.usermanagementservice.domain.Corridor;

import java.util.List;
import java.util.Set;

public interface CorridorSyncStrategy {

    List<Corridor> syncCorridorStatusByIntersectionInactiveIds(Set<String> inactiveIntersectionId);
}
