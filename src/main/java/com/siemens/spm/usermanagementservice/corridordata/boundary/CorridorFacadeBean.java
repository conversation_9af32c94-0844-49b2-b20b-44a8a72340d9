/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : CorridorFacadeBean.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.corridordata.boundary;

import com.siemens.spm.datahub.exception.DataHubIntersectionsException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.siemens.spm.usermanagementservice.api.boundary.CorridorService;
import com.siemens.spm.usermanagementservice.api.vo.request.CorridorCreateRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.CorridorListRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.CorridorSearchRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.CorridorStatusUpdateRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.CorridorUpdateRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.IntersectionAvailableSearchRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.CorridorInternalSearchResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.CorridorListResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.CorridorResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionInternalSearchResultObject;
import com.siemens.spm.usermanagementservice.corridordata.strategy.CorridorManagementStrategy;
import com.siemens.spm.usermanagementservice.corridordata.strategy.CorridorSearchStrategy;

@Service
public class CorridorFacadeBean implements CorridorService {

    @Autowired
    private CorridorManagementStrategy corridorManagementStrategy;

    @Autowired
    private CorridorSearchStrategy corridorSearchStrategy;

    /**
     * {@inheritDoc}
     */
    @Override
    public CorridorResultObject createCorridor(CorridorCreateRequestVO corridorVO) {
        try {
            return corridorManagementStrategy.createCorridor(corridorVO);
        } catch (DataHubIntersectionsException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CorridorResultObject updateCorridor(String corridorId, CorridorUpdateRequestVO corridorVO) {
        try {
            return corridorManagementStrategy.updateCorridor(corridorId, corridorVO);
        } catch (DataHubIntersectionsException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public CorridorResultObject getCorridorDetail(String corridorId) {
        return corridorManagementStrategy.getCorridorDetail(corridorId);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CorridorListResultObject getCorridors(CorridorListRequestVO corridorListRequestVO) {
        return corridorSearchStrategy.getCorridors(corridorListRequestVO);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CorridorResultObject updateCorridorStatus(CorridorStatusUpdateRequestVO requestVO) {
        return corridorManagementStrategy.updateCorridorStatus(requestVO);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public IntersectionInternalSearchResultObject searchAvailableIntersections(IntersectionAvailableSearchRequestVO requestVO) {
        return corridorSearchStrategy.searchAvailableIntersections(requestVO);
    }

    @Override
    public CorridorInternalSearchResultObject getAllCorridorsByFilterInternal(CorridorSearchRequestVO searchRequest) {
        return corridorSearchStrategy.getAllCorridorsByFilterInternal(searchRequest);
    }

}
