package com.siemens.spm.usermanagementservice.dashboarddata.strategy.intercom;

import com.siemens.spm.perflogcrawler.api.vo.IntersectionPerflogInternalVO;
import com.siemens.spm.perflogcrawler.api.vo.request.IntersectionPerflogSearchRequest;

import java.util.Map;

public interface IntersectionPerflogStrategy {

    /**
     * Search intersection perflog data
     *
     * @param searchRequest {@link IntersectionPerflogSearchRequest}
     * @return {@code Collections.emptyMap()} if have no data.
     * Other wise, return {@code Map<String, IntersectionPerflogInternalVO>}
     */
    Map<String, IntersectionPerflogInternalVO> searchIntersectionPerflog(IntersectionPerflogSearchRequest searchRequest);

}
