package com.siemens.spm.usermanagementservice.dashboarddata.strategy;

import com.siemens.spm.analysis.api.controller.InternalAnalysisController;
import com.siemens.spm.analysis.api.intercom.SummaryStatisticInterComController;
import com.siemens.spm.analysis.api.vo.AnalysisStatisticsVO;
import com.siemens.spm.analysis.api.vo.IntersectionStatisticsVO;
import com.siemens.spm.analysis.api.vo.request.AnalysisStatisticsRequestVO;
import com.siemens.spm.analysis.api.vo.request.summaryreport.SummaryStatisticRequestVO;
import com.siemens.spm.analysis.api.vo.response.AnalysisStatisticsResultObject;
import com.siemens.spm.analysis.api.vo.response.summaryreport.SummaryStatisticResultObject;
import com.siemens.spm.analysis.api.vo.summaryreport.StatsTypeVO;
import com.siemens.spm.analysis.api.vo.summaryreport.SummaryStatisticVO;
import com.siemens.spm.common.constant.CommonConstants;
import com.siemens.spm.common.constant.IntersectionOption;
import com.siemens.spm.common.constant.NotificationConstants;
import com.siemens.spm.common.message.MessageService;
import com.siemens.spm.common.security.SecurityUtils;
import com.siemens.spm.common.shared.domaintype.ActionTarget;
import com.siemens.spm.common.shared.domaintype.ActionType;
import com.siemens.spm.common.shared.domaintype.IntersectionStatus;
import com.siemens.spm.common.shared.domaintype.MetricConstants;
import com.siemens.spm.common.shared.domaintype.alarm.condition.Operator;
import com.siemens.spm.common.shared.domaintype.analysis.AnalysisType;
import com.siemens.spm.common.shared.domaintype.notification.NotificationType;
import com.siemens.spm.common.shared.vo.ActionVO;
import com.siemens.spm.common.shared.vo.AlarmNotificationActionDataVO;
import com.siemens.spm.common.shared.vo.AlarmRecordsActionDataVO;
import com.siemens.spm.common.shared.vo.AnalysisActionDataVO;
import com.siemens.spm.common.shared.vo.StatisticDataVO;
import com.siemens.spm.common.util.DateTimeConverter;
import com.siemens.spm.common.util.ListUtil;
import com.siemens.spm.datahub.api.boundary.DataIntegrationService;
import com.siemens.spm.datahub.api.vo.DataHubIntersectionSearchRequestVO;
import com.siemens.spm.datahub.api.vo.DataHubIntersectionVO;
import com.siemens.spm.datahub.api.vo.response.AgencySchemaResponseVO;
import com.siemens.spm.datahub.api.vo.response.DataHubIntersectionResponseVO;
import com.siemens.spm.datahub.exception.DataHubException;
import com.siemens.spm.perflogcrawler.api.intercom.PerfLogController;
import com.siemens.spm.perflogcrawler.api.vo.request.PerfLogGetRequestVO;
import com.siemens.spm.perflogcrawler.api.vo.response.IntersectionVolumeCountResultObject;
import com.siemens.spm.rule.api.intercom.AlarmRecordInterComController;
import com.siemens.spm.rule.api.vo.request.AlarmRecordCountForEachIntersectionRequestVO;
import com.siemens.spm.rule.api.vo.response.AlarmRecordCountForEachIntersectionResponseVO;
import com.siemens.spm.spmstudiosdk.dto.StudioUserDto;
import com.siemens.spm.spmstudiosdk.exception.StudioException;
import com.siemens.spm.spmstudiosdk.service.StudioUserService;
import com.siemens.spm.usermanagementservice.api.vo.ElementForDashboardChartVO;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionForDashboardMapVO;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionForDashboardTopRankVO;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionListVolumeCountVO;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionVolumeCountVO;
import com.siemens.spm.usermanagementservice.api.vo.NotificationTypeDataVO;
import com.siemens.spm.usermanagementservice.api.vo.SummaryIntersectionStatisticVO;
import com.siemens.spm.usermanagementservice.api.vo.SummaryStatisticForDashboardVO;
import com.siemens.spm.usermanagementservice.api.vo.request.IntersectionForDashboardMapSearchRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.ElementListForDashboardChartResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionListForDashboardMapResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionListForDashboardTopRankResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionListVolumeCountResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.NotificationListForUserNotificationObject;
import com.siemens.spm.usermanagementservice.api.vo.response.SummaryStatisticForDashboardResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.SummaryStatisticForDashboardResultObject.SummaryStatisticForDashboardStatusCode;
import com.siemens.spm.usermanagementservice.config.SpmConfig;
import com.siemens.spm.usermanagementservice.dashboarddata.strategy.util.FilterSummaryStatisticHelper;
import com.siemens.spm.usermanagementservice.dashboarddata.strategy.util.SortSummaryStatisticHelper;
import com.siemens.spm.usermanagementservice.domain.Notification;
import com.siemens.spm.usermanagementservice.intersectiondata.util.IntersectionVolumeCountVOBuilder;
import com.siemens.spm.usermanagementservice.repository.NotificationRepository;
import com.siemens.spm.usermanagementservice.repository.filterdata.IntersectionFilterDataVO;
import com.siemens.spm.usermanagementservice.repository.filterdata.NotificationFilterDataVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.util.Pair;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.HttpClientErrorException;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.siemens.spm.usermanagementservice.api.vo.response.IntersectionListVolumeCountResultObject.StatusCode.AGENCY_TIMEZONE_NOT_FOUND;

@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class DashboardStrategyBean implements DashboardStrategy {

    private final SpmConfig spmConfig;
    private final NotificationRepository notificationRepository;
    private final MessageService translator;
    private final StudioUserService studioUserService;
    private final DataIntegrationService dataIntegrationService;

    private static final String STUDIO_USER_ERROR_MSG = "Error when get current user";
    private static final String STUDIO_USER_NOT_FOUND_MSG = "Cannot find current user with email={} in Studio";

    /**
     * {@inheritDoc}
     */
    @Override
    public IntersectionListForDashboardMapResultObject getIntersectionsForMap(
            IntersectionForDashboardMapSearchRequestVO requestVO,
            String[] orderByColumns,
            Integer page,
            Integer size) throws DataHubException {

        try {
            // First get agency schema to get timezoneId
            AgencySchemaResponseVO agencySchema = getAgencyInfo(requestVO.getAgencyId());
            if (agencySchema == null || agencySchema.getTimezoneId() == null) {
                log.warn("No timezone found in agency schema for agency {}", requestVO.getAgencyId());
                return new IntersectionListForDashboardMapResultObject(null,
                        IntersectionListForDashboardMapResultObject.StatusCode.AGENCY_TIMEZONE_NOT_FOUND);
            }

            Integer agencyId = requestVO.getAgencyId();
            Double[] bottomRight = requestVO.getBottomRight();
            Double[] topLeft = requestVO.getTopLeft();

            DataHubIntersectionSearchRequestVO searchRequestVO = DataHubIntersectionSearchRequestVO.builder()
                    .agencyId(agencyId)
                    .bottomRight(bottomRight)
                    .topLeft(topLeft)
                    .orderByColumns(orderByColumns)
                    .status(IntersectionStatus.AVAILABLE.getDataHub())
                    .shouldPaginate(false)
                    .build();

            DataHubIntersectionResponseVO response = getIntersectionsByFilter(searchRequestVO);
            List<DataHubIntersectionVO> intersections = response.getItems();
            Long totalCount = response.getTotalItems();

            Map<String, Long> intersectionIdToAlarmRecordCountMap = getAlarmRecordCountForEachIntersection(agencyId,
                    intersections);

            AnalysisStatisticsVO analysisStatisticsVO = getAnalysisStatistics(agencyId, intersections,
                    DateTimeConverter.toTimestamp(requestVO.getFromLocalDateTime(), agencySchema.getTimezoneId()),
                    DateTimeConverter.toTimestamp(requestVO.getToLocalDateTime(), agencySchema.getTimezoneId()));

            Timestamp updatedTime = analysisStatisticsVO != null ? analysisStatisticsVO.getUpdatedTime() : null;

            LocalDateTime alarmTimeFrom = null;
            LocalDateTime alarmTimeTo = null;

            try {
                Pair<LocalDateTime, LocalDateTime> fromAndToTimeForSearchingAlarmRecordsPair = getFromAndToTimeForSearchingAlarmRecords(
                        agencyId);

                alarmTimeFrom = fromAndToTimeForSearchingAlarmRecordsPair.getFirst();
                alarmTimeTo = fromAndToTimeForSearchingAlarmRecordsPair.getSecond();
            } catch (Exception e) {
                log.error("Cannot get from and to time for searching alarm records!", e);
            }

            var intersectionForDashboardMapVOs = createIntersectionForDashboardMapVOs(
                    agencySchema.getTimezoneId(),
                    agencyId,
                    intersections,
                    analysisStatisticsVO,
                    intersectionIdToAlarmRecordCountMap,
                    alarmTimeFrom,
                    alarmTimeTo);

            var responseData = IntersectionListForDashboardMapResultObject.ResponseData.builder()
                    .intersections(intersectionForDashboardMapVOs)
                    .totalCount(totalCount)
                    .localUpdatedTime(DateTimeConverter.toLocalDateTime(updatedTime, agencySchema.getTimezoneId()))
                    .updatedTime(updatedTime)
                    .build();

            return new IntersectionListForDashboardMapResultObject(responseData,
                    IntersectionListForDashboardMapResultObject.StatusCode.SUCCESS);
        } catch (DataHubException e) {
            throw new RuntimeException(e);
        }
    }

    private Pair<LocalDateTime, LocalDateTime> getFromAndToTimeForSearchingAlarmRecords(Integer agencyId) {
        var nowUtc = LocalDateTime.now();

        LocalDateTime agencyTime = getAgencyTime(nowUtc, agencyId);
        LocalDateTime currentDateAtAgency = agencyTime.truncatedTo(ChronoUnit.DAYS);

        LocalDateTime fromTime = currentDateAtAgency.minusDays(7);
        LocalDateTime toTime = currentDateAtAgency.plusDays(1).minusSeconds(1);

        return Pair.of(fromTime, toTime);
    }

    private LocalDateTime getAgencyTime(LocalDateTime nowUtc, Integer agencyId) {
        if (agencyId == null) {
            log.warn("Missing agency settings or agency ID, using UTC time");
            return nowUtc;
        }

        try {
            // First get agency schema to get timezoneId
            AgencySchemaResponseVO agencySchema = getAgencyInfo(agencyId);
            if (agencySchema == null || agencySchema.getTimezoneId() == null) {
                log.warn("No timezone found in agency schema for agency {}, using UTC", agencyId);
                return nowUtc;
            }

            ZoneId zoneId = ZoneId.of(agencySchema.getTimezoneId());
            return nowUtc.atZone(ZoneOffset.UTC)
                    .withZoneSameInstant(zoneId)
                    .toLocalDateTime();
        } catch (Exception e) {
            log.error("Error getting timezone information for agency {}: {}", agencyId, e.getMessage());
            return nowUtc;
        }
    }

    private Map<String, Long> getAlarmRecordCountForEachIntersection(Integer agencyId,
                                                                     List<DataHubIntersectionVO> intersections) {
        if (agencyId == null || intersections == null || intersections.isEmpty()) {
            return new HashMap<>();
        }

        List<String> intersectionIds = intersections.stream()
                .map(DataHubIntersectionVO::getId)
                .distinct()
                .toList();

        LocalDateTime nowUtc = LocalDateTime.now();
        LocalDateTime sevenDaysBeforeNowUtc = nowUtc.minusDays(7);

        String endpoint = spmConfig.getRuleServiceEndpoint();
        var requestVO = AlarmRecordCountForEachIntersectionRequestVO.builder()
                .agencyId(agencyId)
                .alarmTimeUtcFrom(Timestamp.valueOf(sevenDaysBeforeNowUtc))
                .alarmTimeUtcTo(Timestamp.valueOf(nowUtc))
                .intersectionIds(intersectionIds)
                .build();

        ResponseEntity<AlarmRecordCountForEachIntersectionResponseVO> response;

        try {
            response = AlarmRecordInterComController.invokeCountRecordsForEachIntersection(endpoint, requestVO);
        } catch (Exception e) {
            log.error("Error when invokeCountRecordsForEachIntersection", e);
            return new HashMap<>();
        }

        Map<String, Long> intersectionIdToAlarmRecordCountMap = null;

        AlarmRecordCountForEachIntersectionResponseVO responseBody = response.getBody();
        if (response.getStatusCode() == HttpStatus.OK && responseBody != null) {
            intersectionIdToAlarmRecordCountMap = responseBody.getData().getIntersectionIdToAlarmRecordCountMap();
        }

        if (intersectionIdToAlarmRecordCountMap == null) {
            intersectionIdToAlarmRecordCountMap = new HashMap<>();
        }

        return intersectionIdToAlarmRecordCountMap;
    }

    private AnalysisStatisticsVO getAnalysisStatistics(Integer agencyId,
                                                       List<DataHubIntersectionVO> intersections,
                                                       Timestamp fromTime,
                                                       Timestamp toTime) {
        var emptyAnalysisStatisticsVO = AnalysisStatisticsVO.builder()
                .intStatisticsVOList(new ArrayList<>())
                .updatedTime(null)
                .build();

        if (agencyId == null || intersections == null || intersections.isEmpty()) {
            return emptyAnalysisStatisticsVO;
        }

        List<String> intersectionIds = intersections.stream()
                .map(DataHubIntersectionVO::getId)
                .distinct()
                .toList();

        String endpoint = spmConfig.getAnalysisServiceEndpoint();

        var requestVO = new AnalysisStatisticsRequestVO(intersectionIds, fromTime, toTime);

        ResponseEntity<AnalysisStatisticsResultObject> response;
        try {
            response = InternalAnalysisController.invokeGetAnalysisStatistics(endpoint, agencyId, requestVO);
        } catch (Exception e) {
            log.error("Error when invoke get analysis statistics", e);
            return emptyAnalysisStatisticsVO;
        }

        AnalysisStatisticsVO analysisStatisticsVO = null;
        if (response.getStatusCode() == HttpStatus.OK) {
            AnalysisStatisticsResultObject resultObject = response.getBody();
            if (resultObject != null) {
                analysisStatisticsVO = resultObject.getData();
            }
        }
        if (analysisStatisticsVO == null) {
            analysisStatisticsVO = emptyAnalysisStatisticsVO;
        }

        return analysisStatisticsVO;
    }

    private List<IntersectionForDashboardMapVO> createIntersectionForDashboardMapVOs(String agencyTimezoneId,
                                                                                     Integer agencyId,
                                                                                     List<DataHubIntersectionVO> intersections,
                                                                                     AnalysisStatisticsVO analysisStatisticsVO,
                                                                                     Map<String, Long> intersectionIdToAlarmRecordCountMap,
                                                                                     LocalDateTime alarmTimeFrom,
                                                                                     LocalDateTime alarmTimeTo) {
        // retrieve number of all open alarm notifications
        String email = SecurityUtils.getCurrentUserEmail();
        Optional<StudioUserDto> userDtoOptional;
        try {
            userDtoOptional = studioUserService.findByEmail(email);
        } catch (StudioException e) {
            log.error(STUDIO_USER_ERROR_MSG, e);
            return new ArrayList<>();
        }
        if (userDtoOptional.isEmpty()) {
            log.warn(STUDIO_USER_NOT_FOUND_MSG, email);
            return new ArrayList<>();
        }

        Long userId = Long.valueOf(userDtoOptional.get().getId());

        Long notificationType = NotificationType.ALARM_NOTIFICATION.getId();
        String notificationReadStatus = NotificationConstants.ReadStatus.UNREAD.getValue();

        var notificationFilterDataVO = NotificationFilterDataVO.builder()
                .agencyId(agencyId)
                .readStatus(notificationReadStatus)
                .typeId(notificationType)
                .userId(userId)
                .build();

        // Create map of IntersectionForDashboardMapVO with number of open alarm
        // notifications
        var intersectionIdToIntersectionVOMap = new HashMap<String, IntersectionForDashboardMapVO>();
        for (DataHubIntersectionVO intersection : intersections) {
            IntersectionForDashboardMapVO intersectionForDashboardMapVO = createIntersectionForDashboardMapVO(
                    agencyId, intersection, alarmTimeFrom, alarmTimeTo);

            String intersectionId = intersection.getId();

            notificationFilterDataVO.setIntersectionId(intersectionId);
            long openAlarmCount = notificationRepository.countTotalByFilter(notificationFilterDataVO);
            intersectionForDashboardMapVO.setOpenAlarmCount(openAlarmCount);

            Long alarmRecordCount = intersectionIdToAlarmRecordCountMap.get(intersectionId);
            intersectionForDashboardMapVO.setAlarmRecordCount(alarmRecordCount != null ? alarmRecordCount : 0L);

            intersectionIdToIntersectionVOMap.put(intersection.getId(), intersectionForDashboardMapVO);
        }

        // Fill analysis statistics (AoR percent, average pedestrian delay) to
        // IntersectionForDashboardMapVO
        if (analysisStatisticsVO != null) {
            for (IntersectionStatisticsVO intStatisticsVO : analysisStatisticsVO.getIntStatisticsVOList()) {
                IntersectionForDashboardMapVO intForDashboardMapVO = intersectionIdToIntersectionVOMap
                        .get(intStatisticsVO.getIntUUID());
                if (null == intForDashboardMapVO) {
                    // Should not happen here
                    log.error("Orphan intStatisticsVO intUUID={}", intStatisticsVO.getIntUUID());
                    continue;
                }

                intForDashboardMapVO.setAorPercent(intStatisticsVO.getAorPercent());
                intForDashboardMapVO.setAogPercent(intStatisticsVO.getAogPercent());
                intForDashboardMapVO.setVehicleVolume(intStatisticsVO.getVehicleVolume());
                intForDashboardMapVO.setAvgPedDelay(intStatisticsVO.getAvgPedDelay());
                intForDashboardMapVO.setLocalUpdatedTime(DateTimeConverter.toLocalDateTime(
                        intStatisticsVO.getUpdatedTime(), agencyTimezoneId));
                intForDashboardMapVO.setUpdatedTime(intStatisticsVO.getUpdatedTime());
                intForDashboardMapVO.setCoordHealthTrans(intStatisticsVO.getCoordHealthTrans());
                intForDashboardMapVO.setSplitFailure(intStatisticsVO.getSplitFailure());
                intForDashboardMapVO.setAvgAppDelay(intStatisticsVO.getAvgAppDelay());
                intForDashboardMapVO.setRlvCount(intStatisticsVO.getRlvCount());
                intForDashboardMapVO.setPreemption(intStatisticsVO.getPpRequests());
                intForDashboardMapVO.setAvgQueueLength(intStatisticsVO.getAvgQueueLength());
            }
        }

        return new ArrayList<>(intersectionIdToIntersectionVOMap.values());
    }

    /**
     * Create IntersectionForDashboardMapVO and initialize basic fields.</br> NOTE: aorPercent and alarmNumber are not
     * initialized here
     *
     * @param agencyId     Id of agency
     * @param intersection intersection
     * @return IntersectionForDashboardMapVO
     */
    private IntersectionForDashboardMapVO createIntersectionForDashboardMapVO(Integer agencyId,
                                                                              DataHubIntersectionVO intersection,
                                                                              LocalDateTime alarmTimeFrom,
                                                                              LocalDateTime alarmTimeTo) {
        // Action for open alarm notification metric
        AlarmNotificationActionDataVO alarmNotiActionDataVO = AlarmNotificationActionDataVO.builder()
                .intUUID(intersection.getId())
                .readStatus(NotificationConstants.ReadStatus.UNREAD.getValue())
                .build();
        ActionVO openAlarmNotiActionVO = ActionVO.builder()
                .labelKey("alarm_notifications")
                .type(ActionType.REDIRECT)
                .target(ActionTarget.NOTIFICATIONS)
                .data(alarmNotiActionDataVO)
                .build();
        openAlarmNotiActionVO.acceptTranslator(translator);

        // Action for alarm record metric
        AlarmRecordsActionDataVO alarmRecordsActionDataVO = AlarmRecordsActionDataVO.builder()
                .agencyId(agencyId)
                .intUUID(intersection.getId())
                .fromTime(alarmTimeFrom)
                .toTime(alarmTimeTo)
                .build();
        ActionVO alarmRecordActionVO = ActionVO.builder()
                .labelKey("alarm_records")
                .type(ActionType.REDIRECT)
                .target(ActionTarget.ALARM_RECORDS)
                .data(alarmRecordsActionDataVO)
                .build();
        alarmRecordActionVO.acceptTranslator(translator);

        // Action for AoR% metric
        AnalysisActionDataVO aorAnalysisActionDataVO = AnalysisActionDataVO.builder()
                .agencyId(agencyId)
                .intUUID(intersection.getId())
                .analysisId(AnalysisType.ARRIVALS_ON_RED.getId())
                .build();
        ActionVO aorPercentActionVO = ActionVO.builder()
                .labelKey("aor_analysis")
                .type(ActionType.REDIRECT)
                .target(ActionTarget.ANALYSIS)
                .data(aorAnalysisActionDataVO)
                .build();
        aorPercentActionVO.acceptTranslator(translator);

        // Action for AoG% metric
        AnalysisActionDataVO aogAnalysisActionDataVO = AnalysisActionDataVO.builder()
                .agencyId(agencyId)
                .intUUID(intersection.getId())
                .analysisId(AnalysisType.ARRIVALS_ON_GREEN.getId())
                .build();
        ActionVO aogPercentActionVO = ActionVO.builder()
                .labelKey("aog_analysis")
                .type(ActionType.REDIRECT)
                .target(ActionTarget.ANALYSIS)
                .data(aogAnalysisActionDataVO)
                .build();
        aogPercentActionVO.acceptTranslator(translator);

        // Action for Volume metric
        AnalysisActionDataVO volumeAnalysisActionDataVO = AnalysisActionDataVO.builder()
                .agencyId(agencyId)
                .intUUID(intersection.getId())
                .analysisId(AnalysisType.COORDINATION.getId())
                .build();
        ActionVO volumePercentActionVO = ActionVO.builder()
                .labelKey("coord_analysis")
                .type(ActionType.REDIRECT)
                .target(ActionTarget.ANALYSIS)
                .data(volumeAnalysisActionDataVO)
                .build();
        volumePercentActionVO.acceptTranslator(translator);

        // Action for Avg Pedestrian Delay
        AnalysisActionDataVO avgPedDelayActionDataVO = AnalysisActionDataVO.builder()
                .agencyId(agencyId)
                .intUUID(intersection.getId())
                .analysisId(AnalysisType.PEDESTRIAN.getId())
                .build();
        ActionVO avgPedDelayActionVO = ActionVO.builder()
                .labelKey("avg_ped_delay")
                .type(ActionType.REDIRECT)
                .target(ActionTarget.ANALYSIS)
                .data(avgPedDelayActionDataVO)
                .build();
        avgPedDelayActionVO.acceptTranslator(translator);

        //Action for Avg Approach Delay
        AnalysisActionDataVO avgAppDelayActionDataVO = AnalysisActionDataVO.builder()
                .agencyId(agencyId)
                .intUUID(intersection.getId())
                .analysisId(AnalysisType.APPROACH_DELAY.getId())
                .build();
        ActionVO avgAppDelayActionVO = ActionVO.builder()
                .labelKey("avg_app_delay")
                .type(ActionType.REDIRECT)
                .target(ActionTarget.ANALYSIS)
                .data(avgAppDelayActionDataVO)
                .build();
        avgAppDelayActionVO.acceptTranslator(translator);

        //Action for Transition%
        AnalysisActionDataVO transCoordActionDataVO = AnalysisActionDataVO.builder()
                .agencyId(agencyId)
                .intUUID(intersection.getId())
                .analysisId(AnalysisType.COORDINATION_HEALTH.getId())
                .build();
        ActionVO transCoordActionVO = ActionVO.builder()
                .labelKey("coord_health")
                .type(ActionType.REDIRECT)
                .target(ActionTarget.ANALYSIS)
                .data(transCoordActionDataVO)
                .build();
        transCoordActionVO.acceptTranslator(translator);

        //Action for SF% - Split Failure
        AnalysisActionDataVO sfActionDataVO = AnalysisActionDataVO.builder()
                .agencyId(agencyId)
                .intUUID(intersection.getId())
                .analysisId(AnalysisType.SPLIT_FAILURE.getId())
                .build();
        ActionVO sfActionVO = ActionVO.builder()
                .labelKey("split")
                .type(ActionType.REDIRECT)
                .target(ActionTarget.ANALYSIS)
                .data(sfActionDataVO)
                .build();
        sfActionVO.acceptTranslator(translator);

        // Action for RLV metric
        AnalysisActionDataVO rlvAnalysisActionDataVO = AnalysisActionDataVO.builder()
                .agencyId(agencyId)
                .intUUID(intersection.getId())
                .analysisId(AnalysisType.RED_LIGHT_VIOLATION.getId())
                .build();
        ActionVO rlvPercentActionVO = ActionVO.builder()
                .labelKey("rlv_analysis")
                .type(ActionType.REDIRECT)
                .target(ActionTarget.ANALYSIS)
                .data(rlvAnalysisActionDataVO)
                .build();
        rlvPercentActionVO.acceptTranslator(translator);

        // Action for Preemption metric
        AnalysisActionDataVO preemptionVO = AnalysisActionDataVO.builder()
                .agencyId(agencyId)
                .intUUID(intersection.getId())
                .analysisId(AnalysisType.PREEMPTION_PRIORITY.getId())
                .build();
        ActionVO preemptionActionVO = ActionVO.builder()
                .labelKey("preemption_analysis")
                .type(ActionType.REDIRECT)
                .target(ActionTarget.ANALYSIS)
                .data(preemptionVO)
                .build();
        preemptionActionVO.acceptTranslator(translator);

        // Action for Queue Length metric
        AnalysisActionDataVO queueLengthVO = AnalysisActionDataVO.builder()
                .agencyId(agencyId)
                .intUUID(intersection.getId())
                .analysisId(AnalysisType.QUEUE_LENGTH.getId())
                .build();
        ActionVO queueLengthActionVO = ActionVO.builder()
                .labelKey("queue_length_analysis")
                .type(ActionType.REDIRECT)
                .target(ActionTarget.ANALYSIS)
                .data(queueLengthVO)
                .build();
        queueLengthActionVO.acceptTranslator(translator);

        Map<String, ActionVO> actionMap = new HashMap<>();
        actionMap.put(MetricConstants.OPEN_ALARM_NOTI_COUNT_ID, openAlarmNotiActionVO);
        actionMap.put(MetricConstants.ALARM_RECORD_COUNT_ID, alarmRecordActionVO);
        actionMap.put(MetricConstants.AOR_PERCENT_ID, aorPercentActionVO);
        actionMap.put(MetricConstants.AOG_PERCENT_ID, aogPercentActionVO);
        actionMap.put(MetricConstants.DETECTOR_ACTUATION_VOLUME_ID, volumePercentActionVO);
        actionMap.put(MetricConstants.AVG_PED_DELAY_ID, avgPedDelayActionVO);
        actionMap.put(MetricConstants.TRANSITION_PERCENT_ID, transCoordActionVO);
        actionMap.put(MetricConstants.SPLIT_FAILURE_ID, sfActionVO);
        actionMap.put(MetricConstants.AVG_APP_DELAY_ID, avgAppDelayActionVO);
        actionMap.put(MetricConstants.RED_LIGHT_VIOLATION_COUNT_ID, rlvPercentActionVO);
        actionMap.put(MetricConstants.PREEMPTION_PRIORITY_ID, preemptionActionVO);
        actionMap.put(MetricConstants.AVG_QUEUE_LENGTH_ID, queueLengthActionVO);

        return IntersectionForDashboardMapVO.builder()
                .id(intersection.getId())
                .name(intersection.getName())
                .lat(intersection.getLocation() != null ? intersection.getLocation().getLatitude() : null)
                .lon(intersection.getLocation() != null ? intersection.getLocation().getLongitude() : null)
                .actionMap(actionMap)
                .build();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public IntersectionListForDashboardTopRankResultObject getIntersectionsForTopRank(Integer agencyId,
                                                                                      List<String> intIds,
                                                                                      List<String> excludeIntIds,
                                                                                      Integer size)
            throws DataHubException {
        String email = SecurityUtils.getCurrentUserEmail();
        Optional<StudioUserDto> userDtoOptional;
        try {
            userDtoOptional = studioUserService.findByEmail(email);
        } catch (StudioException e) {
            log.error(STUDIO_USER_ERROR_MSG, e);
            return new IntersectionListForDashboardTopRankResultObject(null,
                    IntersectionListForDashboardTopRankResultObject.StatusCode.ERROR);
        }
        if (userDtoOptional.isEmpty()) {
            log.warn(STUDIO_USER_NOT_FOUND_MSG, email);
            return new IntersectionListForDashboardTopRankResultObject(null,
                    IntersectionListForDashboardTopRankResultObject.StatusCode.UNAUTHORIZED);
        }

        DataHubIntersectionSearchRequestVO searchRequestVO = DataHubIntersectionSearchRequestVO.builder()
                .agencyId(agencyId)
                .status(IntersectionStatus.AVAILABLE.getDataHub())
                .intersectionIds(intIds)
                .exclusionaryIds(excludeIntIds)
                .shouldPaginate(false)
                .build();

        DataHubIntersectionResponseVO response = getIntersectionsByFilter(searchRequestVO);
        List<DataHubIntersectionVO> intersections = response.getItems();
        List<String> availableIds = intersections.stream().map(DataHubIntersectionVO::getId).toList();

        Long userId = Long.valueOf(userDtoOptional.get().getId());
        Long typeId = NotificationType.ALARM_NOTIFICATION.getId();

        var filterDataVO = NotificationFilterDataVO.builder()
                .userId(userId)
                .agencyId(agencyId)
                .intIds(availableIds)
                .excludeIntIds(excludeIntIds)
                .typeId(typeId)
                .readStatus(NotificationConstants.ReadStatus.UNREAD.getValue())
                .build();

        Map<String, Long> intersectionIdToOpenAlarmCountMap = notificationRepository
                .findTopIntersectionsByUserIdAndAgencyIdAndTypeIdAndUnreadCount(filterDataVO, size);

        /*
         * theoretically, all the ranked intersections are in "available-intersections".
         * so, don't need to query to the db again to get the intersection name.
         * */
        Map<String, String> intersectionIdToIntersectionNameMap = new HashMap<>();
        for (DataHubIntersectionVO availableIntersection : intersections) {
            intersectionIdToIntersectionNameMap.put(availableIntersection.getId(), availableIntersection.getName());
        }

        var intersectionOpenAlarmVOs = new ArrayList<IntersectionForDashboardTopRankVO>();

        for (var intersectionIdToOpenAlarmCountPair : intersectionIdToOpenAlarmCountMap.entrySet()) {
            String intersectionId = intersectionIdToOpenAlarmCountPair.getKey();

            String intersectionName = intersectionIdToIntersectionNameMap.get(intersectionId);
            if (intersectionName == null) {
                intersectionName = CommonConstants.NOT_AVAILABLE;
            }

            Long openAlarmCount = intersectionIdToOpenAlarmCountPair.getValue();

            IntersectionForDashboardTopRankVO intersectionForDashboardTopRankVO = IntersectionForDashboardTopRankVO
                    .builder()
                    .name(intersectionName)
                    .openAlarmCount(openAlarmCount)
                    .build();

            intersectionOpenAlarmVOs.add(intersectionForDashboardTopRankVO);
        }

        var responseData = IntersectionListForDashboardTopRankResultObject.ResponseData.builder()
                .intersections(intersectionOpenAlarmVOs)
                .totalCount((long) intersectionIdToOpenAlarmCountMap.size())
                .build();

        return new IntersectionListForDashboardTopRankResultObject(responseData,
                IntersectionListForDashboardTopRankResultObject.StatusCode.SUCCESS);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ElementListForDashboardChartResultObject getAllElementsForChart(Integer agencyId,
                                                                           List<String> intIds,
                                                                           List<String> excludeIntIds)
            throws DataHubException {
        String email = SecurityUtils.getCurrentUserEmail();
        Optional<StudioUserDto> userDtoOptional;
        try {
            userDtoOptional = studioUserService.findByEmail(email);
        } catch (StudioException e) {
            log.error(STUDIO_USER_ERROR_MSG, e);
            return new ElementListForDashboardChartResultObject(null,
                    ElementListForDashboardChartResultObject.StatusCode.ERROR);
        }
        if (userDtoOptional.isEmpty()) {
            log.warn(STUDIO_USER_NOT_FOUND_MSG, email);
            return new ElementListForDashboardChartResultObject(null,
                    ElementListForDashboardChartResultObject.StatusCode.UNAUTHORIZED);
        }

        Long userId = Long.valueOf(userDtoOptional.get().getId());

        // Build filter object to filter unread notification
        NotificationFilterDataVO filterDataVO = NotificationFilterDataVO.builder()
                .userId(userId)
                .agencyId(agencyId)
                .intIds(intIds)
                .excludeIntIds(getExcludeIntersectionIds(agencyId, excludeIntIds))
                .typeId(NotificationType.ALARM_NOTIFICATION.getId())
                .readStatus(NotificationConstants.ReadStatus.UNREAD.getValue())
                .build();

        // Get all unread notifications
        List<Notification> unreadAlarmNotifications = notificationRepository.findAllByFilter(filterDataVO);

        // Count open alarm each alarm category
        Map<String, ElementForDashboardChartVO> categoryNameMap = new HashMap<>();

        for (Notification notification : unreadAlarmNotifications) {
            String categoryName = notification.getAlarmCategoryName();

            if (categoryNameMap.containsKey(categoryName)) {
                ElementForDashboardChartVO chartVO = categoryNameMap.get(categoryName);
                chartVO.setCount(chartVO.getCount() + 1);
            } else {
                ElementForDashboardChartVO newChartVO = ElementForDashboardChartVO.builder()
                        .name(categoryName)
                        .count(1L)
                        .build();
                categoryNameMap.put(categoryName, newChartVO);
            }
        }

        List<ElementForDashboardChartVO> elementForDashboardChartVOs = new ArrayList<>(categoryNameMap.values());
        var responseData = ElementListForDashboardChartResultObject.ResponseData.builder()
                .elements(elementForDashboardChartVOs)
                .totalCount((long) elementForDashboardChartVOs.size())
                .build();

        return new ElementListForDashboardChartResultObject(responseData,
                ElementListForDashboardChartResultObject.StatusCode.SUCCESS);
    }

    @Override
    public NotificationListForUserNotificationObject getElementsForUserNotification(Integer agencyId,
                                                                                    List<Long> typeIds,
                                                                                    Timestamp fromTime,
                                                                                    Timestamp toTime)
            throws DataHubException {
        String email = SecurityUtils.getCurrentUserEmail();
        Optional<StudioUserDto> userDtoOptional;
        try {
            userDtoOptional = studioUserService.findByEmail(email);
        } catch (StudioException e) {
            log.error(STUDIO_USER_ERROR_MSG, e);
            return new NotificationListForUserNotificationObject(null,
                    NotificationListForUserNotificationObject.StatusCode.ERROR);
        }
        if (userDtoOptional.isEmpty()) {
            log.warn(STUDIO_USER_NOT_FOUND_MSG, email);
            return new NotificationListForUserNotificationObject(null,
                    NotificationListForUserNotificationObject.StatusCode.UNAUTHORIZED);
        }

        Long userId = Long.valueOf(userDtoOptional.get().getId());

        var filterDataVO = NotificationFilterDataVO.builder()
                .userId(userId)
                .agencyId(agencyId)
                .typeIds(typeIds)
                .excludeIntIds(getExcludeIntersectionIds(agencyId, null))
                .createdAtFrom(fromTime)
                .createdAtTo(toTime)
                .build();

        List<Notification> notifications = notificationRepository.findAllByFilter(filterDataVO);
        Map<Long, Integer> typeIdsMap = new HashMap<>();

        long unReadCount = 0;
        long flagCount = 0;

        for (Notification notification : notifications) {
            Long typeId = notification.getTypeId();

            if (NotificationConstants.ReadStatus.UNREAD.getValue().equals(notification.getReadStatus())) {
                unReadCount++;
            }

            if (NotificationConstants.FlagStatus.FLAG.getValue().equals(notification.getFlagStatus())) {
                flagCount++;
            }

            if (typeIdsMap.containsKey(typeId)) {
                typeIdsMap.put(typeId, typeIdsMap.get(typeId) + 1);
            } else {
                typeIdsMap.put(typeId, 1);
            }
        }

        List<NotificationTypeDataVO> elements = new ArrayList<>();
        long totalCount = notifications.size();
        if (totalCount > 0) {
            for (Map.Entry<Long, Integer> entry : typeIdsMap.entrySet()) {
                Long typeId = entry.getKey();
                Integer notiCountForType = entry.getValue();
                NotificationType type = NotificationType.getById(typeId);
                if (type != null) {
                    NotificationTypeDataVO dataVO = NotificationTypeDataVO.builder()
                            .typeId(typeId)
                            .name(type.getTranslatedName())
                            .total(notiCountForType)
                            .percentage((double) notiCountForType / totalCount)
                            .build();
                    elements.add(dataVO);
                }
            }
        }

        var responseData = NotificationListForUserNotificationObject.ResponseData.builder()
                .notificationTypes(elements)
                .newNotification(unReadCount)
                .flagNotification(flagCount)
                .totalCount(totalCount)
                .build();

        return new NotificationListForUserNotificationObject(responseData,
                NotificationListForUserNotificationObject.StatusCode.SUCCESS);
    }

    @Override
    public NotificationListForUserNotificationObject getElementsForUserNotification(Integer agencyId,
                                                                                    List<Long> typeIds,
                                                                                    LocalDateTime fromTime,
                                                                                    LocalDateTime toTime)
            throws DataHubException {
        String email = SecurityUtils.getCurrentUserEmail();
        Optional<StudioUserDto> userDtoOptional;
        try {
            userDtoOptional = studioUserService.findByEmail(email);
        } catch (StudioException e) {
            log.error(STUDIO_USER_ERROR_MSG, e);
            return new NotificationListForUserNotificationObject(null,
                    NotificationListForUserNotificationObject.StatusCode.ERROR);
        }
        if (userDtoOptional.isEmpty()) {
            log.warn(STUDIO_USER_NOT_FOUND_MSG, email);
            return new NotificationListForUserNotificationObject(null,
                    NotificationListForUserNotificationObject.StatusCode.UNAUTHORIZED);
        }

        Long userId = Long.valueOf(userDtoOptional.get().getId());

        // First get agency schema to get timezoneId
        AgencySchemaResponseVO agencySchema = getAgencyInfo(agencyId);
        if (agencySchema == null || agencySchema.getTimezoneId() == null) {
            log.warn("No timezone found in agency schema for agency {}", agencyId);
            return new NotificationListForUserNotificationObject(null,
                    NotificationListForUserNotificationObject.StatusCode.AGENCY_TIMEZONE_NOT_FOUND);
        }

        Timestamp fromUTC = DateTimeConverter.toTimestamp(fromTime, agencySchema.getTimezoneId());
        Timestamp toUTC = DateTimeConverter.toTimestamp(toTime, agencySchema.getTimezoneId());

        var filterDataVO = NotificationFilterDataVO.builder()
                .userId(userId)
                .agencyId(agencyId)
                .typeIds(typeIds)
                .excludeIntIds(getExcludeIntersectionIds(agencyId, null))
                .createdAtFrom(fromUTC)
                .createdAtTo(toUTC)
                .build();

        List<Notification> notifications = notificationRepository.findAllByFilter(filterDataVO);
        Map<Long, Integer> typeIdsMap = new HashMap<>();

        long unReadCount = 0;
        long flagCount = 0;

        for (Notification notification : notifications) {
            Long typeId = notification.getTypeId();

            if (NotificationConstants.ReadStatus.UNREAD.getValue().equals(notification.getReadStatus())) {
                unReadCount++;
            }

            if (NotificationConstants.FlagStatus.FLAG.getValue().equals(notification.getFlagStatus())) {
                flagCount++;
            }

            if (typeIdsMap.containsKey(typeId)) {
                typeIdsMap.put(typeId, typeIdsMap.get(typeId) + 1);
            } else {
                typeIdsMap.put(typeId, 1);
            }
        }

        List<NotificationTypeDataVO> elements = new ArrayList<>();
        long totalCount = notifications.size();
        if (totalCount > 0) {
            for (Map.Entry<Long, Integer> entry : typeIdsMap.entrySet()) {
                Long typeId = entry.getKey();
                Integer notiCountForType = entry.getValue();
                NotificationType type = NotificationType.getById(typeId);
                if (type != null) {
                    NotificationTypeDataVO dataVO = NotificationTypeDataVO.builder()
                            .typeId(typeId)
                            .name(type.getTranslatedName())
                            .total(notiCountForType)
                            .percentage((double) notiCountForType / totalCount)
                            .build();
                    elements.add(dataVO);
                }
            }
        }

        var responseData = NotificationListForUserNotificationObject.ResponseData.builder()
                .notificationTypes(elements)
                .newNotification(unReadCount)
                .flagNotification(flagCount)
                .totalCount(totalCount)
                .build();

        return new NotificationListForUserNotificationObject(responseData,
                NotificationListForUserNotificationObject.StatusCode.SUCCESS);
    }

    private List<String> getExcludeIntersectionIds(Integer agencyId, List<String> excludeIntIds)
            throws DataHubException {

        DataHubIntersectionSearchRequestVO searchRequestVO = DataHubIntersectionSearchRequestVO.builder()
                .agencyId(agencyId)
                .status(IntersectionStatus.UNAVAILABLE.getDataHub())
                .shouldPaginate(false)
                .build();

        List<DataHubIntersectionVO> unavailableIntersections = getIntersectionsByFilterWithoutPaging(searchRequestVO);
        List<String> unavailableIntersectionIds = unavailableIntersections.stream()
                .map(DataHubIntersectionVO::getId)
                .toList();

        // Add unavailable intersection id to excludeIntIds
        if (Objects.isNull(excludeIntIds)) {
            excludeIntIds = unavailableIntersectionIds;
        } else {
            excludeIntIds.addAll(unavailableIntersectionIds);
        }
        return excludeIntIds;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SummaryStatisticForDashboardResultObject getSummaryStatistics(Integer agencyId,
                                                                         String[] filter,
                                                                         String[] sort,
                                                                         Integer page,
                                                                         Integer size,
                                                                         String[] metrics,
                                                                         Timestamp fromTime,
                                                                         Timestamp toTime) throws DataHubException {
        if (metrics == null || fromTime == null || toTime == null) {
            throw new IllegalArgumentException();
        }
        fromTime = roundDownToHour(fromTime);
        toTime = roundDownToHour(toTime);

        // TODO: Validate agencyId provided from input

        long duration = ChronoUnit.HOURS.between(fromTime.toInstant(), toTime.toInstant());
        if (!fromTime.before(toTime) || duration > 24) {
            return new SummaryStatisticForDashboardResultObject(
                    SummaryStatisticForDashboardStatusCode.INVALID_TIME_RANGE);
        }
        FilterSummaryStatisticHelper.Filter filterObj = FilterSummaryStatisticHelper.resolveFilter(filter);
        IntersectionFilterDataVO filterDataVO = IntersectionFilterDataVO.builder()
                .agencyId(agencyId)
                .status(IntersectionStatus.AVAILABLE.getInsight())
                .build();
        if (Objects.nonNull(filterObj)) {
            filterDataVO.setName(filterObj.getIntName());
        }
        DataHubIntersectionSearchRequestVO searchRequestVO = DataHubIntersectionSearchRequestVO.builder()
                .agencyId(agencyId)
                .status(IntersectionStatus.AVAILABLE.getDataHub())
                .shouldPaginate(false)
                .build();

        DataHubIntersectionResponseVO response = getIntersectionsByFilter(searchRequestVO);
        List<DataHubIntersectionVO> intersections = response.getItems();

        long totalCount = response.getTotalItems();

        List<String> intIds = intersections.stream()
                .map(DataHubIntersectionVO::getId)
                .toList();
        List<StatsTypeVO> statsTypeVOList = Arrays.stream(metrics)
                .map(this::buildStatsTypeVO)
                .filter(Objects::nonNull)
                .toList();
        List<SortSummaryStatisticHelper.MetricSort> metricSort = SortSummaryStatisticHelper.resolveMetricSort(sort,
                statsTypeVOList);

        SummaryStatisticRequestVO requestVO = SummaryStatisticRequestVO.builder()
                .agencyId(agencyId)
                .fromTime(fromTime)
                .toTime(toTime)
                .intIds(intIds)
                .statsTypeVOList(statsTypeVOList)
                .build();

        SummaryStatisticResultObject resultObject = invokeDoStatistics(requestVO);

        SummaryStatisticResultObject.StatusCode statusCode = resultObject.getStatusCode();
        SummaryStatisticVO summaryStatisticVO = resultObject.getData();

        if (statusCode != SummaryStatisticResultObject.StatusCode.SUCCESS || summaryStatisticVO == null
                || summaryStatisticVO.getStatisticsDataMap() == null) {
            return new SummaryStatisticForDashboardResultObject(SummaryStatisticForDashboardStatusCode.ERROR);
        }

        Map<String, Map<String, StatisticDataVO>> intStatisticDataMap = summaryStatisticVO.getStatisticsDataMap();
        List<SummaryIntersectionStatisticVO> intersectionStatisticVOList = buildIntersectionStatisticVOList(
                intStatisticDataMap, intersections);
        SummaryStatisticForDashboardVO summaryStatisticForDashboardVO = SummaryStatisticForDashboardVO.builder()
                .intersectionStatisticVOList(pageAndSort(intersectionStatisticVOList, metricSort, page, size))
                .totalCount(totalCount)
                .build();

        return new SummaryStatisticForDashboardResultObject(summaryStatisticForDashboardVO);
    }

    @Override
    public SummaryStatisticForDashboardResultObject getSummaryStatistics(Integer agencyId,
                                                                         String[] filter,
                                                                         String[] sort,
                                                                         Integer page,
                                                                         Integer size,
                                                                         String[] metrics,
                                                                         LocalDateTime fromTime,
                                                                         LocalDateTime toTime) throws DataHubException {
        if (metrics == null || fromTime == null || toTime == null) {
            throw new IllegalArgumentException();
        }

        // First get agency schema to get timezoneId
        AgencySchemaResponseVO agencySchema = getAgencyInfo(agencyId);
        if (agencySchema == null || agencySchema.getTimezoneId() == null) {
            log.warn("No timezone found in agency schema for agency {}", agencyId);
            return new SummaryStatisticForDashboardResultObject(
                    SummaryStatisticForDashboardStatusCode.AGENCY_TIMEZONE_NOT_FOUND);
        }

        Timestamp fromUTC = DateTimeConverter.toTimestamp(fromTime, agencySchema.getTimezoneId());
        Timestamp toUTC = DateTimeConverter.toTimestamp(toTime, agencySchema.getTimezoneId());

        return getSummaryStatistics(agencyId, filter, sort, page, size, metrics, fromUTC, toUTC);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public IntersectionListVolumeCountResultObject getIntersectionVolumeCount(LocalDateTime fromTime,
                                                                              LocalDateTime toTime,
                                                                              Integer agencyId,
                                                                              IntersectionOption intersectionOption,
                                                                              List<String> intersectionIds)
            throws DataHubException {
        Optional<IntersectionListVolumeCountResultObject> resultObject = validateGetIntersectionVolumeCount(fromTime,
                toTime, agencyId);
        if (resultObject.isPresent()) {
            return resultObject.get();
        }

        if (Objects.isNull(intersectionIds)) {
            intersectionIds = new ArrayList<>();
        }

        ResponseEntity<IntersectionVolumeCountResultObject> getIntersectionVolumeCountResponse;
        DataHubIntersectionSearchRequestVO searchRequestVO;
        AgencySchemaResponseVO agencySchema = getAgencyInfo(agencyId);
        if (agencySchema == null || agencySchema.getTimezoneId() == null) {
            log.warn("No timezone found in agency schema for agency {}", agencyId);
            return new IntersectionListVolumeCountResultObject(AGENCY_TIMEZONE_NOT_FOUND);
        }

        if (IntersectionOption.INCLUDE_ALL.equals(intersectionOption) || IntersectionOption.INCLUDE_SPECIFIC.equals(
                intersectionOption)) {
            searchRequestVO = DataHubIntersectionSearchRequestVO.builder()
                    .agencyId(agencyId)
                    .status(IntersectionStatus.AVAILABLE.getDataHub())
                    .shouldPaginate(false)
                    .intersectionIds(CollectionUtils.isEmpty(intersectionIds) ? null : intersectionIds)
                    .build();
        } else {
            searchRequestVO = DataHubIntersectionSearchRequestVO.builder()
                    .agencyId(agencyId)
                    .status(IntersectionStatus.AVAILABLE.getDataHub())
                    .shouldPaginate(false)
                    .exclusionaryIds(CollectionUtils.isEmpty(intersectionIds) ? null : intersectionIds)
                    .build();
        }

        List<DataHubIntersectionVO> intersections = getIntersectionsByFilterWithoutPaging(searchRequestVO);
        Map<String, String> intersectionMaps = intersections.stream()
                .collect(Collectors.toMap(DataHubIntersectionVO::getId, DataHubIntersectionVO::getName));
        try {
            PerfLogGetRequestVO searchRequest = buildPerfLogGetRequestVO(agencyId, fromTime, toTime,
                    intersectionMaps.keySet(),
                    agencySchema.getTimezoneId());
            getIntersectionVolumeCountResponse = PerfLogController.invokeGetIntersectionVolumeCount(
                    spmConfig.getPerflogServiceEndpoint(), searchRequest);
        } catch (HttpClientErrorException ex) {
            log.error("Cannot get intersection volume count from perflog service!", ex);

            return new IntersectionListVolumeCountResultObject(null,
                    IntersectionListVolumeCountResultObject.StatusCode.CAN_NOT_CALL_PEEFLOG_SERVICE);
        }

        IntersectionVolumeCountResultObject body = getIntersectionVolumeCountResponse.getBody();

        return this.buildIntersectionListVolumeCountResultObjectSuccessResponse(intersectionMaps, body);
    }

    private PerfLogGetRequestVO buildPerfLogGetRequestVO(Integer agencyId,
                                                         LocalDateTime fromTime,
                                                         LocalDateTime toTime,
                                                         Set<String> intersectionIds,
                                                         String agencyTimezone) {
        Timestamp fromUTC = DateTimeConverter.toTimestamp(fromTime, agencyTimezone);
        Timestamp toUTC = DateTimeConverter.toTimestamp(toTime, agencyTimezone);

        return PerfLogGetRequestVO.builder()
                .fromTime(fromUTC)
                .toTime(toUTC)
                .agencyId(agencyId)
                .intersectionIds(intersectionIds.stream().toList())
                .build();
    }

    private AgencySchemaResponseVO getAgencyInfo(Integer agencyId) throws DataHubException {
        return dataIntegrationService.getAgencySchema(agencyId);
    }

    private Optional<IntersectionListVolumeCountResultObject> validateGetIntersectionVolumeCount(LocalDateTime fromTime,
                                                                                                 LocalDateTime toTime,
                                                                                                 Integer agencyId) {
        if (fromTime == null || toTime == null) {
            log.error("Invalid input time");
            return Optional.of(new IntersectionListVolumeCountResultObject(null,
                    IntersectionListVolumeCountResultObject.StatusCode.INVALID_INPUT_TIME));
        }

        Optional<IntersectionListVolumeCountResultObject> resultObject = validateFromTimeAndToTime(fromTime, toTime);
        if (resultObject.isPresent()) {
            return resultObject;
        }

        if (agencyId == null) {
            return Optional.of(new IntersectionListVolumeCountResultObject(null,
                    IntersectionListVolumeCountResultObject.StatusCode.AGENCY_ID_NOT_NULL));
        }

        return Optional.empty();
    }

    private List<SummaryIntersectionStatisticVO> pageAndSort(List<SummaryIntersectionStatisticVO> input,
                                                             List<SortSummaryStatisticHelper.MetricSort> sort,
                                                             int page,
                                                             int size) {
        long skip = (long) page * size;
        if (ListUtil.hasNoItem(sort)) {
            return input.stream()
                    .skip(skip)
                    .limit(size)
                    .toList();
        }
        return input.stream()
                .sorted((o1, o2) -> SortSummaryStatisticHelper.comparator(o1, o2, sort))
                .skip(skip)
                .limit(size)
                .toList();
    }

    private Optional<IntersectionListVolumeCountResultObject> validateFromTimeAndToTime(LocalDateTime fromTime,
                                                                                        LocalDateTime toTime) {
        LocalDateTime now = LocalDateTime.now();

        // from > to
        if (fromTime.isAfter(toTime)) {
            log.error("From time is greater than to time");
            return Optional.of(
                    new IntersectionListVolumeCountResultObject(null,
                            IntersectionListVolumeCountResultObject.StatusCode.FROM_TIME_MUST_LESS_THAN_TO_TIME));
        }
        // from > now
        if (fromTime.isAfter(now)) {
            log.error("From time is greater than now");
            return Optional.of(new IntersectionListVolumeCountResultObject(null,
                    IntersectionListVolumeCountResultObject.StatusCode.INPUT_TIME_MUST_LESS_THAN_NOW));
        }
        // Max range time is 24h: from + 24 < to
        if (fromTime.plusHours(24).isBefore(toTime)) {
            log.error("Range time is over 24h");
            return Optional.of(new IntersectionListVolumeCountResultObject(null,
                    IntersectionListVolumeCountResultObject.StatusCode.INVALID_TIME_RANGE));
        }

        return Optional.empty();
    }

    private IntersectionListVolumeCountResultObject buildIntersectionListVolumeCountResultObjectSuccessResponse(
            Map<String, String> intersectionMaps,
            IntersectionVolumeCountResultObject intersectionVolumeCountResultObject
    ) {
        // Default response in case getIntersectionVolumeCountResponse return empty data or null
        IntersectionListVolumeCountResultObject responseVO = new IntersectionListVolumeCountResultObject(
                IntersectionListVolumeCountVO.builder()
                        .intersectionVolumeCountVOS(Collections.emptyList())
                        .build(),
                IntersectionListVolumeCountResultObject.StatusCode.SUCCESS);

        if (isIntersectionVolumeCountResultObjectHasData(intersectionVolumeCountResultObject)) {

            List<IntersectionVolumeCountVO> intersectionVolumeCountVOS = intersectionVolumeCountResultObject.getData()
                    .getIntersectionVolumeCountVOS()
                    .stream()
                    .map(vo -> IntersectionVolumeCountVOBuilder.buildIntersectionVolumeCountVO(vo, intersectionMaps.get(vo.getIntersectionId())))
                    .toList();
            intersectionVolumeCountVOS = new ArrayList<>(intersectionVolumeCountVOS);
            updateIntersectionVolumeCountVOS(intersectionMaps, intersectionVolumeCountVOS);

            responseVO = new IntersectionListVolumeCountResultObject(
                    IntersectionListVolumeCountVO.builder()
                            .intersectionVolumeCountVOS(intersectionVolumeCountVOS)
                            .build(),
                    IntersectionListVolumeCountResultObject.StatusCode.SUCCESS
            );
        }

        return responseVO;
    }

    private boolean isIntersectionVolumeCountResultObjectHasData(
            IntersectionVolumeCountResultObject intersectionVolumeCountResultObject) {
        return Objects.nonNull(intersectionVolumeCountResultObject)
                && Objects.nonNull(intersectionVolumeCountResultObject.getData())
                && ListUtil.hasItem(intersectionVolumeCountResultObject.getData().getIntersectionVolumeCountVOS());
    }

    private void updateIntersectionVolumeCountVOS(Map<String, String> intersectionsMap,
                                                  List<IntersectionVolumeCountVO> intersectionVolumeCountVOS) {

        // Sort by total count in descending order
        intersectionVolumeCountVOS.sort(Comparator.comparing(IntersectionVolumeCountVO::getTotalCount).reversed());

        for (int i = 0; i < intersectionVolumeCountVOS.size(); i++) {
            IntersectionVolumeCountVO volumeCountVO = intersectionVolumeCountVOS.get(i);
            volumeCountVO.setNumericalOrder(i + 1);
            volumeCountVO.setIntersectionName(intersectionsMap.get(volumeCountVO.getIntersectionId()));
        }
    }

    private SummaryStatisticResultObject invokeDoStatistics(SummaryStatisticRequestVO requestVO) {
        ResponseEntity<SummaryStatisticResultObject> responseEntity;
        try {
            responseEntity = SummaryStatisticInterComController
                    .invokeStatistics(spmConfig.getAnalysisServiceEndpoint(), requestVO);
        } catch (Exception e) {
            log.error("Error when invoke doStatistics", e);

            return new SummaryStatisticResultObject(SummaryStatisticResultObject.StatusCode.ERROR);
        }

        SummaryStatisticResultObject resultObject = responseEntity.getBody();
        if (responseEntity.getStatusCode() != HttpStatus.OK || resultObject == null) {
            return new SummaryStatisticResultObject(SummaryStatisticResultObject.StatusCode.ERROR);
        }

        return resultObject;
    }

    private List<SummaryIntersectionStatisticVO> buildIntersectionStatisticVOList(Map<String, Map<String, StatisticDataVO>> intStatisticDataMap,
                                                                                  List<DataHubIntersectionVO> intersections) {
        List<SummaryIntersectionStatisticVO> intersectionStatisticVOList = new ArrayList<>();
        for (DataHubIntersectionVO intersection : intersections) {
            String intUUID = intersection.getId();
            Map<String, StatisticDataVO> statisticDataVOMap = intStatisticDataMap.get(intUUID);
            if (statisticDataVOMap == null) {
                // This should not happen
                continue;
            }

            SummaryIntersectionStatisticVO intersectionStatisticVO = SummaryIntersectionStatisticVO.builder()
                    .intUUID(intUUID)
                    .intName(intersection.getName())
                    .statisticDataVOMap(statisticDataVOMap)
                    .build();
            intersectionStatisticVOList.add(intersectionStatisticVO);
        }

        return intersectionStatisticVOList;
    }

    private StatsTypeVO buildStatsTypeVO(String metric) {
        try {
            String[] metricParts = metric.split(":");

            if (metricParts.length == 1) {
                return new StatsTypeVO(metric);
            } else if (metricParts.length == 3) {
                Operator operator = Operator.resolveByLabel(metricParts[1]);
                Double value = Double.valueOf(metricParts[2]);
                return new StatsTypeVO(metricParts[0], operator, value);
            }

            return null;
        } catch (Exception e) {
            log.error("Error when buildStatsTypeVO", e);

            return null;
        }
    }

    private Timestamp roundDownToHour(Timestamp timestamp) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(timestamp);
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH),
                calendar.get(Calendar.HOUR_OF_DAY), 0, 0);

        return new Timestamp(calendar.getTime().getTime());
    }

    private DataHubIntersectionResponseVO getIntersectionsByFilter(DataHubIntersectionSearchRequestVO searchRequestVO)
            throws DataHubException {
        return dataIntegrationService.getIntersectionsByFilter(searchRequestVO);
    }

    private List<DataHubIntersectionVO> getIntersectionsByFilterWithoutPaging(DataHubIntersectionSearchRequestVO searchRequestVO)
            throws DataHubException {
        return dataIntegrationService.getIntersectionsByFilter(searchRequestVO).getItems();
    }

}
