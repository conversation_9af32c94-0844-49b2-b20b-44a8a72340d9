/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : DashboardStrategy.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.dashboarddata.strategy;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;

import com.siemens.spm.common.constant.IntersectionOption;
import com.siemens.spm.datahub.exception.DataHubException;
import com.siemens.spm.usermanagementservice.api.vo.request.IntersectionForDashboardMapSearchRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.ElementListForDashboardChartResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionListForDashboardMapResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionListForDashboardTopRankResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionListVolumeCountResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.NotificationListForUserNotificationObject;
import com.siemens.spm.usermanagementservice.api.vo.response.SummaryStatisticForDashboardResultObject;

public interface DashboardStrategy {

    /**
     * Get intersections for map of dashboard
     *
     * @param requestVO
     * @param orderByColumns
     * @param page
     * @param size
     * @return IntersectionListForDashboardMapResultObject
     */
    IntersectionListForDashboardMapResultObject getIntersectionsForMap(IntersectionForDashboardMapSearchRequestVO requestVO,
                                                                       String[] orderByColumns,
                                                                       Integer page,
                                                                       Integer size)
            throws DataHubException;

    /**
     * Get intersections for dashboard top rank
     *
     * @param agencyId
     * @param intIds
     * @param excludeIntIds
     * @param size
     * @return IntersectionListForDashboardTopRankResultObject
     */
    IntersectionListForDashboardTopRankResultObject getIntersectionsForTopRank(Integer agencyId,
                                                                               List<String> intIds,
                                                                               List<String> excludeIntIds,
                                                                               Integer size) throws DataHubException;

    /**
     * Get all elements for chart of dashboard
     *
     * @param agencyId
     * @param intIds
     * @param excludeIntIds
     * @return ElementListForDashboardChartResultObject
     */
    ElementListForDashboardChartResultObject getAllElementsForChart(Integer agencyId,
                                                                    List<String> intIds,
                                                                    List<String> excludeIntIds) throws DataHubException;

    NotificationListForUserNotificationObject getElementsForUserNotification(Integer agencyId,
                                                                             List<Long> typeId,
                                                                             Timestamp fromTime,
                                                                             Timestamp toTime) throws DataHubException;

    NotificationListForUserNotificationObject getElementsForUserNotification(Integer agencyId,
                                                                             List<Long> typeId,
                                                                             LocalDateTime fromTime,
                                                                             LocalDateTime toTime) throws DataHubException;

    SummaryStatisticForDashboardResultObject getSummaryStatistics(Integer agencyId,
                                                                  String[] filter,
                                                                  String[] sort,
                                                                  Integer page,
                                                                  Integer size,
                                                                  String[] metrics,
                                                                  Timestamp fromTime,
                                                                  Timestamp toTime) throws DataHubException;

    SummaryStatisticForDashboardResultObject getSummaryStatistics(Integer agencyId,
                                                                  String[] filter,
                                                                  String[] sort,
                                                                  Integer page,
                                                                  Integer size,
                                                                  String[] metrics,
                                                                  LocalDateTime fromTime,
                                                                  LocalDateTime toTime) throws DataHubException;

    /**
     * Get intersections volume count
     *
     * @param fromTime
     * @param toTime
     * @param agencyId
     * @param intersectionOption
     * @param intersectionIds
     * @return IntersectionListVolumeCountResultObject
     */
    IntersectionListVolumeCountResultObject getIntersectionVolumeCount(LocalDateTime fromTime,
                                                                       LocalDateTime toTime,
                                                                       Integer agencyId,
                                                                       IntersectionOption intersectionOption,
                                                                       List<String> intersectionIds)
            throws DataHubException;

}
