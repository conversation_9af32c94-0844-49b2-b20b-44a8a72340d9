package com.siemens.spm.usermanagementservice.dashboarddata.strategy.util;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.data.domain.Sort;
import org.springframework.util.StringUtils;

import com.siemens.spm.analysis.api.vo.summaryreport.StatsTypeVO;
import com.siemens.spm.common.constant.JpaConstants;
import com.siemens.spm.common.shared.vo.StatisticDataUnitVO;
import com.siemens.spm.common.shared.vo.StatisticDataVO;
import com.siemens.spm.common.util.ListUtil;
import com.siemens.spm.usermanagementservice.api.vo.SummaryIntersectionStatisticVO;
import lombok.Data;

import static com.siemens.spm.usermanagementservice.dashboarddata.strategy.util.FilterSummaryStatisticHelper.FIELD_ALLOWED;
import static com.siemens.spm.usermanagementservice.dashboarddata.strategy.util.FilterSummaryStatisticHelper.INT_NAME_CAMEL_CASE;

public final class SortSummaryStatisticHelper {

    private static final Map<String, String> SORT_TEXT_MAP = Map.of(
            JpaConstants.Sorting.DESC, "desc",
            JpaConstants.Sorting.ASC, "asc"
    );

    private SortSummaryStatisticHelper() {
    }

    public static List<MetricSort> resolveMetricSort(String[] sortString, List<StatsTypeVO> metricSelected) {
        List<MetricSort> result = new ArrayList<>();
        if (Objects.isNull(sortString) || ListUtil.hasNoItem(metricSelected)) {
            return result;
        }
        Set<String> metricSelectedName = metricSelected.stream().map(StatsTypeVO::getMetricId)
                .collect(Collectors.toSet());
        for (String sortElement : sortString) {
            // Struct of string: key:value
            if (!StringUtils.hasText(sortElement) || !sortElement.contains(":")) {
                continue;
            }

            String[] pair = sortElement.split(":");
            String metric = pair[0];

            Optional<Sort.Direction> order = Sort.Direction.fromOptionalString(SORT_TEXT_MAP.get(pair[1]));
            if (order.isEmpty()) {
                continue;
            }

            if (FIELD_ALLOWED.containsKey(metric)) {
                result.add(
                        new MetricSort(FIELD_ALLOWED.get(metric), "", order.get())
                );
                continue;
            }

            String[] metricAndIndicator = metric.split("\\.");

            if (metricAndIndicator.length <= 1) {
                continue;
            }

            if (!metricSelectedName.contains(metricAndIndicator[0])) {
                continue;
            }

            if (!Set.of("min", "max", "avg").contains(metricAndIndicator[1].toLowerCase())) {
                continue;
            }

            result.add(
                    new MetricSort(metricAndIndicator[0], metricAndIndicator[1], order.get())
            );

        }
        return result;
    }

    public static int comparator(SummaryIntersectionStatisticVO i1,
                                 SummaryIntersectionStatisticVO i2,
                                 List<MetricSort> metricSorts) {
        Object[] values = resolveValue(i1, metricSorts);
        Object[] valuesReference = resolveValue(i2, metricSorts);
        int length = metricSorts.size();
        for (int i = 0; i < length; i++) {
            Object value = values[i];
            Object valueReference = valuesReference[i];
            int compareResultTemp = compare(value, valueReference) * metricSorts.get(i).order();
            if (compareResultTemp == 0) {
                continue;
            }
            return compareResultTemp;
        }
        return 0;
    }

    private static int compare(Object x1, Object x2) {
        if (x1 instanceof String x1Str && x2 instanceof String x2Str) {
            return x1Str.compareTo(x2Str);
        } else if (x1 instanceof Double x1Double && x2 instanceof Double x2Double) {
            return Double.compare(x1Double, x2Double);
        } else {
            return 0;
        }
    }

    private static Object[] resolveValue(SummaryIntersectionStatisticVO i1, List<MetricSort> metricSorts) {
        List<Object> values = new ArrayList<>();
        Map<String, StatisticDataVO> statisticDataVOMap = i1.getStatisticDataVOMap();
        metricSorts.forEach(metricSort -> {
            if (metricSort.getMetric().equals(INT_NAME_CAMEL_CASE)) {
                values.add(i1.getIntName());
                return;
            }
            StatisticDataVO statisticDataVO = statisticDataVOMap.getOrDefault(metricSort.getMetric(),
                    StatisticDataVO.builder()
                            .min(StatisticDataUnitVO.builder().value(0.0).build())
                            .max(StatisticDataUnitVO.builder().value(0.0).build())
                            .average(StatisticDataUnitVO.builder().value(0.0).build())
                            .build());
            switch (metricSort.getIndicator()) {
            case "min":
                values.add(statisticDataVO.getMin().getValue());
                break;
            case "max":
                values.add(statisticDataVO.getMax().getValue());
                break;
            case "avg":
                values.add(statisticDataVO.getAverage().getValue());
                break;
            default:
                values.add(0.0);
                break;
            }
        });
        return values.toArray(new Object[] {});
    }

    @Data
    public static class MetricSort {
        private String metric;
        private String indicator; // MIN|MAX|AVG
        private Sort.Direction order;

        public MetricSort(String metric, String indicator, Sort.Direction order) {
            this.metric = metric;
            this.indicator = indicator;
            this.order = order;
        }

        public int order() {
            if (order == null) {
                return 1;
            } else if (order.isAscending()) {
                return 1;
            } else if (order.isDescending()) {
                return -1;
            } else {
                return 1;
            }
        }
    }

}
