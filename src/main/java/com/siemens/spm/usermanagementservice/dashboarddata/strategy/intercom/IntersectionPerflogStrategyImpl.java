package com.siemens.spm.usermanagementservice.dashboarddata.strategy.intercom;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import com.siemens.spm.perflogcrawler.api.intercom.IntersectionPerflogIterComController;
import com.siemens.spm.perflogcrawler.api.vo.IntersectionPerflogInternalVO;
import com.siemens.spm.perflogcrawler.api.vo.request.IntersectionPerflogSearchRequest;
import com.siemens.spm.perflogcrawler.api.vo.response.IntersectionPerflogInternalVOResultObject;
import com.siemens.spm.usermanagementservice.config.SpmConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@Transactional
public class IntersectionPerflogStrategyImpl implements IntersectionPerflogStrategy {

    @Autowired
    private SpmConfig spmConfig;

    /**
     * {@inheritDoc}
     */
    @Override
    public Map<String, IntersectionPerflogInternalVO> searchIntersectionPerflog(IntersectionPerflogSearchRequest searchRequest) {
        Map<String, IntersectionPerflogInternalVO> result = new HashMap<>();
        ResponseEntity<IntersectionPerflogInternalVOResultObject> responseEntity;
        try {
            responseEntity = IntersectionPerflogIterComController
                    .invokeSearchIntersectionPerflogInternal(spmConfig.getPerflogServiceEndpoint(), searchRequest);
        } catch (Exception e) {
            log.error("Cannot search intersection perflog data", e);

            return Collections.emptyMap();
        }

        IntersectionPerflogInternalVOResultObject resultObject = responseEntity.getBody();
        if (resultObject != null && resultObject.getData() != null) {
            List<IntersectionPerflogInternalVO> internalVOList = resultObject.getData()
                    .getIntersectionConfigSearchVOList();
            if (internalVOList == null || internalVOList.isEmpty()) {
                log.debug("Have no intersection perflog data");

                return Collections.emptyMap();
            }
            for (IntersectionPerflogInternalVO intersectionPerflog : internalVOList) {
                result.put(intersectionPerflog.getId(), intersectionPerflog);
            }

            return result;
        }

        return Collections.emptyMap();
    }

}
