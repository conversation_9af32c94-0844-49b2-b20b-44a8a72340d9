package com.siemens.spm.usermanagementservice.dashboarddata.strategy.util;

import java.lang.reflect.Field;
import java.util.Map;
import java.util.Objects;

import org.springframework.util.ReflectionUtils;
import org.springframework.util.StringUtils;

import lombok.Data;

public final class FilterSummaryStatisticHelper {

    private FilterSummaryStatisticHelper() {
    }

    public static final String INT_NAME_CAMEL_CASE = "intName";

    public static final String INT_NAME_SNAKE_CASE = "int_name";

    public static final Map<String, String> FIELD_ALLOWED = Map.of(
            INT_NAME_CAMEL_CASE, INT_NAME_CAMEL_CASE,
            INT_NAME_SNAKE_CASE, INT_NAME_CAMEL_CASE
    );

    public static Filter resolveFilter(String[] filterString) {
        if (Objects.isNull(filterString)) {
            return null;
        }
        Filter filterObject = new Filter();
        boolean hasFilter = false;
        for (String filter : filterString) {
            // Each filter is key:value
            if (!StringUtils.hasText(filter) || !filter.contains(":")) {
                continue;
            }
            String[] pair = filter.split(":");
            if (!FIELD_ALLOWED.containsKey(pair[0])) {
                continue;
            }
            Field field = ReflectionUtils.findField(Filter.class, FIELD_ALLOWED.get(pair[0]));
            if (Objects.isNull(field)) {
                continue;
            }
            ReflectionUtils.makeAccessible(field);
            ReflectionUtils.setField(field, filterObject, pair[1]);
            hasFilter = true;
        }
        if (!hasFilter) {
            return null;
        }
        return filterObject;
    }

    @Data
    public static class Filter {
        private String intName;
    }

}
