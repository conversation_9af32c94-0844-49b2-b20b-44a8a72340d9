/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : DashboardFacadeBean.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.dashboarddata.boundary;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.siemens.spm.common.constant.IntersectionOption;
import com.siemens.spm.datahub.exception.DataHubException;
import com.siemens.spm.usermanagementservice.api.boundary.DashboardService;
import com.siemens.spm.usermanagementservice.api.vo.request.IntersectionForDashboardMapSearchRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.ElementListForDashboardChartResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionListForDashboardMapResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionListForDashboardTopRankResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionListVolumeCountResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.NotificationListForUserNotificationObject;
import com.siemens.spm.usermanagementservice.api.vo.response.SummaryStatisticForDashboardResultObject;
import com.siemens.spm.usermanagementservice.dashboarddata.strategy.DashboardStrategy;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Transactional
@Slf4j
@RequiredArgsConstructor
public class DashboardFacadeBean implements DashboardService {

    private final DashboardStrategy dashboardStrategy;

    /**
     * {@inheritDoc}
     */
    @Override
    public IntersectionListForDashboardMapResultObject getIntersectionsForMap(IntersectionForDashboardMapSearchRequestVO requestVO,
                                                                              String[] orderByColumns,
                                                                              Integer page,
                                                                              Integer size) {
        try {
            return dashboardStrategy.getIntersectionsForMap(requestVO, orderByColumns, page, size);
        } catch (DataHubException e) {
            log.error("Error when get intersection from datahub", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public IntersectionListForDashboardTopRankResultObject getIntersectionsForTopRank(Integer agency,
                                                                                      List<String> intIds,
                                                                                      List<String> excludeIntIds,
                                                                                      Integer size) {
        try {
            return dashboardStrategy.getIntersectionsForTopRank(agency, intIds, excludeIntIds, size);
        } catch (DataHubException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ElementListForDashboardChartResultObject getAllElementsForChart(Integer agency,
                                                                           List<String> intIds,
                                                                           List<String> excludeIntIds) {
        try {
            return dashboardStrategy.getAllElementsForChart(agency, intIds, excludeIntIds);
        } catch (DataHubException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public NotificationListForUserNotificationObject getElementForUserNotification(Integer agencyId,
                                                                                   List<Long> typeIds,
                                                                                   Timestamp fromTime,
                                                                                   Timestamp toTime) {
        try {
            return dashboardStrategy.getElementsForUserNotification(agencyId, typeIds, fromTime, toTime);
        } catch (DataHubException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public NotificationListForUserNotificationObject getElementForUserNotification(Integer agencyId,
                                                                                   List<Long> typeIds,
                                                                                   LocalDateTime fromTime,
                                                                                   LocalDateTime toTime) {
        try {
            return dashboardStrategy.getElementsForUserNotification(agencyId, typeIds, fromTime, toTime);
        } catch (DataHubException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public SummaryStatisticForDashboardResultObject getSummaryStatistics(Integer agencyId,
                                                                         String[] filter,
                                                                         String[] sort,
                                                                         Integer page,
                                                                         Integer size,
                                                                         String[] metrics,
                                                                         Timestamp fromTime,
                                                                         Timestamp toTime) {
        try {
            return dashboardStrategy.getSummaryStatistics(agencyId, filter, sort, page, size, metrics, fromTime, toTime);
        } catch (DataHubException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public SummaryStatisticForDashboardResultObject getSummaryStatistics(Integer agencyId,
                                                                         String[] filter,
                                                                         String[] sort,
                                                                         Integer page,
                                                                         Integer size,
                                                                         String[] metrics,
                                                                         LocalDateTime fromTime,
                                                                         LocalDateTime toTime) {
        try {
            return dashboardStrategy.getSummaryStatistics(agencyId, filter, sort, page, size, metrics, fromTime, toTime);
        } catch (DataHubException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public IntersectionListVolumeCountResultObject getIntersectionVolumeCount(LocalDateTime fromTime,
                                                                              LocalDateTime toTime,
                                                                              Integer agencyId,
                                                                              IntersectionOption intersectionOption,
                                                                              List<String> intersectionIds) {
        try {
            return dashboardStrategy.getIntersectionVolumeCount(fromTime, toTime, agencyId, intersectionOption,
                    intersectionIds);
        } catch (DataHubException e) {
            throw new RuntimeException(e);
        }
    }

}
