package com.siemens.spm.usermanagementservice.agencydata.reconcile;

import com.siemens.spm.common.agency.master.AgencySchemaReadService;
import com.siemens.spm.common.agency.master.AgencySchemaWriteService;
import com.siemens.spm.common.agency.master.entity.AgencySchema;
import com.siemens.spm.datahub.api.boundary.DataIntegrationService;
import com.siemens.spm.datahub.api.vo.DataHubAgencyInfoVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.siemens.spm.usermanagementservice.config.AsyncConfig.RECONCILE_AGENCY_PROVISION_EXECUTOR;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 8/5/2025
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class AgencyProvisionReconcile {

    private final DataIntegrationService dataIntegrationService;

    private final AgencySchemaWriteService agencySchemaWriteService;

    private final AgencySchemaReadService agencySchemaReadService;

    /**
     * Scheduled job to check agency provision is consistent
     */
    @Scheduled(
        fixedRateString = "${spm.agency.synchronization.fixed-rate-ms:1800000}",
        initialDelayString = "${spm.agency.synchronization.initial-delay-ms:30000}",
        scheduler = RECONCILE_AGENCY_PROVISION_EXECUTOR
    )
    public void reconcileAgenciesProvision() {
        log.info("Starting scheduled reconcile agency provision");
        try {
            List<DataHubAgencyInfoVO> datahubProvisionedAgencies = dataIntegrationService.getProvisionedAgencies();
            List<AgencySchema> provisionedAgencies = agencySchemaReadService.findProvisionedAgencies();
            List<Integer> provisionedAgencyIds = provisionedAgencies.stream().map(AgencySchema::getId).toList();
            datahubProvisionedAgencies.forEach(agencySchema -> {
                if (!provisionedAgencyIds.contains(agencySchema.getAgencyId())) {
                    log.info("Agency {} was not provisioned in perflog crawler", agencySchema.getAgencyId());
                    AgencySchema agency = new AgencySchema();
                    agency.setId(agencySchema.getAgencyId());
                    agency.setAgencyName(agencySchema.getAgencyName());
                    agency.setSchema(agencySchema.getSchema());
                    agency.setActivated(agencySchema.getActivated());
                    agencySchemaWriteService.createAgencySchema(agency);
                }
            });
        } catch (Exception e) {
            log.error("Error synchronizing agency provisioning from datahub", e);
        }
        log.info("Finished scheduled reconcile agency provision");
    }

}
