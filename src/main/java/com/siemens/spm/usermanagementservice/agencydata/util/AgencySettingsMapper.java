/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AgencySettingsMapper.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.agencydata.util;

import com.siemens.spm.usermanagementservice.api.vo.AgencySettingsVO;
import com.siemens.spm.usermanagementservice.api.vo.LocationVO;
import com.siemens.spm.usermanagementservice.domain.AgencySettings;

public final class AgencySettingsMapper {

    private AgencySettingsMapper() {
    }

    /**
     * @param settings
     * @return
     */
    public static AgencySettingsVO fromAgencySettings(AgencySettings settings) {
        Double centralIntersectionLatitude = settings.getCentralIntersectionLatitude();
        Double centralIntersectionLongitude = settings.getCentralIntersectionLongitude();

        LocationVO centralPoint;
        if (centralIntersectionLongitude == null || centralIntersectionLatitude == null) {
            // Use NULL_ISLAND as default
            centralPoint = LocationVO.NULL_ISLAND;
        } else {
            centralPoint = new LocationVO(centralIntersectionLatitude, centralIntersectionLongitude);
        }

        return AgencySettingsVO.builder()
                .displaySettingsVO(settings.getDisplaySettingsVO())
                .zoneOffset(settings.getZoneOffset())
                .showUnavailableMapData(settings.getShowUnavailableMapData())
                .centerPoint(centralPoint)
                .saturation(settings.getAgencySaturationVO())
                .build();
    }

}
