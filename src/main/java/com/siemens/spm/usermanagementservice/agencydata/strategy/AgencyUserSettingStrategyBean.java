/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AgencyUserSettingStrategyBean.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.agencydata.strategy;

import com.siemens.spm.usermanagementservice.repository.AgencyUserSettingRepository;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Transactional
public class AgencyUserSettingStrategyBean implements AgencyUserSettingStrategy {

    private final AgencyUserSettingRepository agencyUserSettingRepository;

    @Override
    public void deleteAgencyUserSettingByAgencyId(@NotNull Integer agencyId) {
        agencyUserSettingRepository.deleteByAgencyId(agencyId);
    }
}
