/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AgencyBaseInfoMgmtStrategyBean.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.agencydata.strategy;

import com.siemens.spm.common.agency.master.entity.AgencySchema;
import com.siemens.spm.common.agency.supports.AgencyAware;
import com.siemens.spm.usermanagementservice.domain.AgencySettings;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.time.LocalDateTime;

@Slf4j
@Service
@RequiredArgsConstructor
public class AgencyBaseInfoMgmtStrategyBean implements AgencyBaseInfoMgmtStrategy {

    private final AgencySettingStrategy agencySettingStrategy;

    @Override
    @AgencyAware(agencyId = "[0].id")
    public void updateAgencySettingBaseInfoFromAgencySchema(@NonNull AgencySchema agencySchema) {
        Integer agencyId = agencySchema.getId();

        AgencySettings agencySettings = agencySettingStrategy.findAgencySettingById(agencyId)
                .orElse(AgencySettings.getDefault(agencyId));

        agencySettings.setLastModifiedAt(Timestamp.valueOf(LocalDateTime.now()));
        agencySettings.setName(agencySchema.getAgencyName());
        agencySettings.setStatus(agencySchema.getActivated());

        agencySettingStrategy.saveAgencySetting(agencySettings);
    }
}
