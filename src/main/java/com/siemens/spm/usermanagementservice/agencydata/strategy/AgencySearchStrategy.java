/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AgencySearchStrategy.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.agencydata.strategy;

import com.siemens.spm.usermanagementservice.api.vo.response.AgencyDetailResultObject;

public interface AgencySearchStrategy {

    /**
     * Get agency detail by Id
     *
     * @param agencyId
     * @return AgencyDetailResultObject
     */
    AgencyDetailResultObject getAgencyDetail(Integer agencyId);

}
