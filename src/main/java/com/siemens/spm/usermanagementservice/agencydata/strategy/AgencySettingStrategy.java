/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AgencySettingStrategy.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.agencydata.strategy;

import com.siemens.spm.usermanagementservice.domain.AgencySettings;

import java.util.Optional;

public interface AgencySettingStrategy {

    void saveAgencySetting(AgencySettings agencySettings);

    Optional<AgencySettings> findAgencySettingById(Integer agencyId);
}
