/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AgencyLicenseStrategy.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.agencydata.strategy;

import com.siemens.spm.usermanagementservice.api.vo.request.AgencyLicenseUpdateRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.AgencyLicenseResultObject;
import lombok.NonNull;

import java.util.List;

public interface AgencyLicenseStrategy {

    AgencyLicenseResultObject getAgencyLicenses(Integer agencyId);

    AgencyLicenseResultObject setAgencyLicenses(Integer agencyId, AgencyLicenseUpdateRequestVO requestVO);

    List<String> getLicenseIds(Integer agencyId);

    void deleteAgencyLicense(@NonNull Integer agencyId);

}
