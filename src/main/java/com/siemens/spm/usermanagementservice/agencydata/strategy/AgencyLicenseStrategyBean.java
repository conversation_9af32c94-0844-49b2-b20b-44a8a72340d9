/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AgencyLicenseStrategyBean.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.agencydata.strategy;

import com.siemens.spm.common.message.MessageService;
import com.siemens.spm.common.shared.domaintype.License;
import com.siemens.spm.common.util.BeanFinder;
import com.siemens.spm.spmstudiosdk.constant.AgencyConstant;
import com.siemens.spm.spmstudiosdk.dto.StudioAgencyDto;
import com.siemens.spm.spmstudiosdk.exception.StudioException;
import com.siemens.spm.spmstudiosdk.service.StudioAgencyService;
import com.siemens.spm.usermanagementservice.api.vo.LicenseVO;
import com.siemens.spm.usermanagementservice.api.vo.request.AgencyLicenseUpdateRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.AgencyLicenseResultObject;
import com.siemens.spm.usermanagementservice.domain.AgencyLicense;
import com.siemens.spm.usermanagementservice.repository.AgencyLicenseRepository;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@Transactional
public class AgencyLicenseStrategyBean implements AgencyLicenseStrategy {

    @Autowired
    private StudioAgencyService studioAgencyService;

    @Autowired
    private AgencyLicenseRepository agencyLicenseRepository;

    @Override
    public AgencyLicenseResultObject getAgencyLicenses(Integer agencyId) {
        Optional<StudioAgencyDto> agencyDtoOptional;
        try {
            agencyDtoOptional = studioAgencyService.getAgencyById(agencyId);
        } catch (StudioException e) {
            log.error("Error while fetching agency from Studio, agencyId: {}", agencyId, e);
            return new AgencyLicenseResultObject(AgencyLicenseResultObject.StatusCode.ERROR);
        }
        if (agencyDtoOptional.isEmpty()) {
            return new AgencyLicenseResultObject(AgencyLicenseResultObject.StatusCode.AGENCY_NOT_FOUND);
        }

        AgencyLicense agencyLicense = agencyLicenseRepository.findById(agencyId)
                .orElseGet(() -> AgencyLicense.getDefault(agencyId));
        List<String> allowedLicenseIds = agencyLicense.getLicensesIds();
        if (allowedLicenseIds == null) {
            allowedLicenseIds = new ArrayList<>();
        }

        AgencyLicenseResultObject.ResponseData responseData = AgencyLicenseResultObject.ResponseData.builder()
                .licenses(buildAllLicenses(allowedLicenseIds))
                .build();
        return new AgencyLicenseResultObject(responseData, AgencyLicenseResultObject.StatusCode.SUCCESS);
    }

    @Override
    public AgencyLicenseResultObject setAgencyLicenses(Integer agencyId, AgencyLicenseUpdateRequestVO requestVO) {
        List<String> licenseIds = requestVO.getLicenseIds();
        for (String licenseId : licenseIds) {
            License license = License.getById(licenseId);
            if (license == null) {
                log.debug("Not found license, id = {}", licenseId);

                return new AgencyLicenseResultObject(AgencyLicenseResultObject.StatusCode.INVALID_LICENSE);
            }
        }

        Optional<StudioAgencyDto> agencyDtoOptional;
        try {
            agencyDtoOptional = studioAgencyService.getAgencyById(agencyId);
        } catch (StudioException e) {
            log.error("Error while fetching agency from Studio, agencyId: {}", agencyId, e);
            return new AgencyLicenseResultObject(AgencyLicenseResultObject.StatusCode.ERROR);
        }
        if (agencyDtoOptional.isEmpty()) {
            return new AgencyLicenseResultObject(AgencyLicenseResultObject.StatusCode.AGENCY_NOT_FOUND);
        }

        // Add all alwaysEnabled licenses before save
        licenseIds.addAll(License.getAllAlwaysEnabledLicenseIds());
        licenseIds = licenseIds.stream()
                .distinct()
                .toList();

        // Do update or save
        AgencyLicense agencyLicense = agencyLicenseRepository.findById(agencyId)
                .orElseGet(() -> AgencyLicense.getDefault(agencyId));
        agencyLicense.setLicensesIds(licenseIds);
        agencyLicense = agencyLicenseRepository.save(agencyLicense);

        // Build response
        AgencyLicenseResultObject.ResponseData responseData = AgencyLicenseResultObject.ResponseData.builder()
                .licenses(buildAllLicenses(agencyLicense.getLicensesIds()))
                .build();

        return new AgencyLicenseResultObject(responseData, AgencyLicenseResultObject.StatusCode.SUCCESS);
    }

    @Override
    public List<String> getLicenseIds(Integer agencyId) {
        if (agencyId == AgencyConstant.SYSTEM_AGENCY_ID) {
            return Arrays.stream(License.values())
                    .map(License::getId)
                    .toList();
        }

        return agencyLicenseRepository.findById(agencyId)
                .map(AgencyLicense::getLicensesIds)
                .orElseGet(() -> AgencyLicense.getDefault(agencyId).getLicensesIds());
    }

    @Override
    public void deleteAgencyLicense(@NotNull Integer agencyId) {
        agencyLicenseRepository.deleteAgencyLicenseByAgencyId(agencyId);
    }

    /**
     * Build all licensed supported by system used to render in the client side.
     *
     * @param allowedLicenceIds list of allowed licence's id which already configured for specific agency
     * @return list of {@link LicenseVO}
     */
    private List<LicenseVO> buildAllLicenses(List<String> allowedLicenceIds) {
        MessageService messageService = BeanFinder.getDefaultMessageService();

        List<LicenseVO> licenseVOList = new ArrayList<>();
        for (License license : License.values()) {
            LicenseVO licenseVO = LicenseVO.builder()
                    .id(license.getId())
                    .name(license.getTranslatedName(messageService))
                    .description(license.getTranslatedDesc(messageService))
                    .build();

            licenseVO.setAlwaysEnabled(license.isAlwaysEnabled());
            licenseVO.setEnabled(allowedLicenceIds.contains(license.getId()));

            licenseVOList.add(licenseVO);
        }

        return licenseVOList;
    }

}
