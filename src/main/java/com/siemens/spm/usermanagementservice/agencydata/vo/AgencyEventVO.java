/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AgencyEventVO.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.agencydata.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.validation.ValidEnum;
import com.siemens.spm.usermanagementservice.common.ChangeTypeEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AgencyEventVO implements Serializable {

    @NotNull
    @JsonProperty("agencyId")
    private Integer agencyId;

    @NotNull
    @JsonProperty("id")
    private long id;

    @JsonProperty("changeType")
    @ValidEnum(enumClass = ChangeTypeEnum.class, ignoreCase = true, message = "invalid_change_type")
    private String changeType;

    @JsonProperty("changeTime")
    private Timestamp changeTime;

    @JsonProperty("changedBy")
    private String changedBy;

    @JsonProperty("metadata")
    private Object metadata;

    @JsonProperty("data")
    private NestedData data;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NestedData {

        @JsonProperty("data")
        private StudioAgencyVO data;

        @JsonProperty("metadata")
        private Object metadata;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StudioAgencyVO {

        @NotBlank
        @JsonProperty("agencyNo")
        private Integer agencyNo;

        @NotBlank
        @JsonProperty("agencyName")
        private String agencyName;

        @JsonProperty("activated")
        private Boolean activated;

        @JsonProperty("creationTime")
        private Timestamp creationTime;
    }
}
