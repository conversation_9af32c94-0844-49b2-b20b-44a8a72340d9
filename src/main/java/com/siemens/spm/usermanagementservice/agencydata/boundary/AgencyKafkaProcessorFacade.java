/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AgencyKafkaProcessorFacade.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.agencydata.boundary;

import com.siemens.spm.common.agency.kafka.boundary.AbstractAgencyKafkaProcessorFacade;
import com.siemens.spm.common.agency.master.AgencySchemaWriteService;
import com.siemens.spm.common.agency.master.entity.AgencySchema;
import com.siemens.spm.usermanagementservice.agencydata.strategy.AgencyBaseInfoMgmtStrategyBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

@Component
@ConditionalOnProperty(value = "spring.kafka.enable", havingValue = "true")
public class AgencyKafkaProcessorFacade extends AbstractAgencyKafkaProcessorFacade {

    private final AgencyBaseInfoMgmtStrategyBean agencyBaseInfoMgmtStrategyBean;

    public AgencyKafkaProcessorFacade(AgencySchemaWriteService agencySchemaWriteService,
                                      AgencyBaseInfoMgmtStrategyBean agencyBaseInfoMgmtStrategyBean) {
        super(agencySchemaWriteService);
        this.agencyBaseInfoMgmtStrategyBean = agencyBaseInfoMgmtStrategyBean;
    }

    @Override
    protected void handleCreateEvent(AgencySchema agencySchema) {
        super.handleCreateEvent(agencySchema);
        agencyBaseInfoMgmtStrategyBean.updateAgencySettingBaseInfoFromAgencySchema(agencySchema);
    }

    @Override
    protected void handleUpdateEvent(AgencySchema agencySchema) {
        super.handleUpdateEvent(agencySchema);
        agencyBaseInfoMgmtStrategyBean.updateAgencySettingBaseInfoFromAgencySchema(agencySchema);
    }

    @Override
    protected void handleDeleteEvent(Integer agencyId) {
        super.handleDeleteEvent(agencyId);
    }
}
