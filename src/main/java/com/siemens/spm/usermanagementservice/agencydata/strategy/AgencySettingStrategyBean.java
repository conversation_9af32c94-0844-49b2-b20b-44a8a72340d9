/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AgencySettingStrategyBean.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.agencydata.strategy;

import com.siemens.spm.common.agency.utils.AgencyPersistenceConstants;
import com.siemens.spm.usermanagementservice.domain.AgencySettings;
import com.siemens.spm.usermanagementservice.repository.AgencySettingsRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Component
@RequiredArgsConstructor
@Transactional(transactionManager = AgencyPersistenceConstants.AGENCY_TRANSACTION_MANAGER)
public class AgencySettingStrategyBean implements AgencySettingStrategy {

    private final AgencySettingsRepository agencySettingsRepository;

    @Override
    public Optional<AgencySettings> findAgencySettingById(Integer agencyId) {
        return agencySettingsRepository.findById(agencyId);
    }

    @Override
    public void saveAgencySetting(AgencySettings agencySettings) {
        agencySettingsRepository.save(agencySettings);
    }
}
