/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : CorridorStrategyBean.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.agencydata.strategy;

import com.siemens.spm.usermanagementservice.repository.CorridorRepository;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Transactional
public class CorridorStrategyBean implements CorridorStrategy {

    private final CorridorRepository corridorRepository;

    @Override
    public void deleteCorridorByAgencyId(@NotNull Integer agencyId) {
        corridorRepository.deleteByAgencyId(agencyId);
    }
}
