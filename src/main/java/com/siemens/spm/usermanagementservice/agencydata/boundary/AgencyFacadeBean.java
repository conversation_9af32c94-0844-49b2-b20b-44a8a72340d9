/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AgencyFacadeBean.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.agencydata.boundary;

import jakarta.validation.constraints.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.siemens.spm.usermanagementservice.agencydata.strategy.AgencyLicenseStrategy;
import com.siemens.spm.usermanagementservice.agencydata.strategy.AgencyMgmtStrategy;
import com.siemens.spm.usermanagementservice.agencydata.strategy.AgencySearchStrategy;
import com.siemens.spm.usermanagementservice.api.boundary.AgencyService;
import com.siemens.spm.usermanagementservice.api.vo.request.AgencyLicenseUpdateRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.IntersectionsInAgencyVerifyRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.SetAgencySettingsRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.AgencyDetailResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.AgencyLicenseResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.AgencySettingsResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionsInAgencyVerifyResultObject;

import java.util.Collection;

@Service
@Transactional
public class AgencyFacadeBean implements AgencyService {

    @Autowired
    private AgencyMgmtStrategy agencyMgmtStrategy;

    @Autowired
    private AgencySearchStrategy searchStrategy;

    @Autowired
    private AgencyLicenseStrategy agencyLicenseStrategy;

    public IntersectionsInAgencyVerifyResultObject verifyIntersectionsInAgencyInternal(
            @NotNull IntersectionsInAgencyVerifyRequestVO requestVO) {
        Integer agencyId = requestVO.getAgencyId();

        if (agencyId == null) {
            return new IntersectionsInAgencyVerifyResultObject(
                    IntersectionsInAgencyVerifyResultObject.StatusCode.MISSING_AGENCY_ID);
        }

        Collection<String> intersectionIds = requestVO.getIntersectionIds();

        // TODO
        IntersectionsInAgencyVerifyResultObject.StatusCode verifyIntersectionsResult = IntersectionsInAgencyVerifyResultObject.StatusCode.SUCCESS;

        return new IntersectionsInAgencyVerifyResultObject(verifyIntersectionsResult);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AgencyDetailResultObject getAgencyDetail(Integer agencyId) {
        return searchStrategy.getAgencyDetail(agencyId);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AgencySettingsResultObject getAgencySettings(Integer agencyId) {
        return agencyMgmtStrategy.getAgencySettings(agencyId);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AgencySettingsResultObject getMapSettings(Integer agencyId) {
        return agencyMgmtStrategy.getMapSettings(agencyId);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AgencySettingsResultObject setAgencySettings(Integer agencyId, SetAgencySettingsRequestVO requestVO) {
        return agencyMgmtStrategy.setAgencySettings(agencyId, requestVO);
    }

    @Override
    public AgencyLicenseResultObject setAgencyLicense(Integer agencyId, AgencyLicenseUpdateRequestVO requestVO) {
        return agencyLicenseStrategy.setAgencyLicenses(agencyId, requestVO);
    }

    @Override
    public AgencyLicenseResultObject getAgencyLicenses(Integer agencyId) {
        return agencyLicenseStrategy.getAgencyLicenses(agencyId);
    }

}
