/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AgencyMgmtStrategy.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.agencydata.strategy;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.siemens.spm.common.message.MessageService;
import com.siemens.spm.common.shared.vo.AlarmCategoryVO;
import com.siemens.spm.common.shared.vo.SimpleResultObject;
import com.siemens.spm.rule.api.intercom.AlarmCategoryInterComController;
import com.siemens.spm.rule.api.vo.request.AlarmCategorySearchRequestVO;
import com.siemens.spm.rule.api.vo.request.AlarmCategoryUpdateRequestVO;
import com.siemens.spm.rule.api.vo.response.AlarmCategoryListResponseVO;
import com.siemens.spm.usermanagementservice.agencydata.util.AgencySettingsMapper;
import com.siemens.spm.usermanagementservice.api.vo.AgencySaturationVO;
import com.siemens.spm.usermanagementservice.api.vo.AgencySettingsVO;
import com.siemens.spm.usermanagementservice.api.vo.DisplaySettingsVO;
import com.siemens.spm.usermanagementservice.api.vo.request.IntersectionsInAgencyVerifyRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.SetAgencySettingsRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.AgencySettingsResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.AgencySettingsResultObject.AgencySettingsStatusCode;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionsInAgencyVerifyResultObject;
import com.siemens.spm.usermanagementservice.config.SpmConfig;
import com.siemens.spm.usermanagementservice.domain.AgencySettings;
import com.siemens.spm.usermanagementservice.repository.AgencySettingsRepository;
import jakarta.transaction.Transactional;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
@Transactional
public class AgencyMgmtStrategy {

    @Autowired
    private SpmConfig spmConfig;

    @Autowired
    private AgencySettingsRepository agencySettingsRepo;

    @Autowired
    private MessageService translator;

    /**
     * Get Agency settings
     *
     * @param agencyId
     * @return
     */
    public AgencySettingsResultObject getAgencySettings(Integer agencyId) {
        if (agencyId == null) {
            throw new IllegalArgumentException();
        }

        Optional<AgencySettings> settingsOpt = agencySettingsRepo.findById(agencyId);

        AgencySettings settings = settingsOpt.orElseGet(() -> AgencySettings.getDefault(agencyId));
        List<AlarmCategoryVO> alarmCategoryVOList = null;

        AlarmCategorySearchRequestVO requestVO = AlarmCategorySearchRequestVO.builder()
                .agencyId(agencyId)
                .build();
        try {
            ResponseEntity<AlarmCategoryListResponseVO> responseEntity = AlarmCategoryInterComController
                    .invokeGetAlarmCategories(spmConfig.getRuleServiceEndpoint(), requestVO);

            AlarmCategoryListResponseVO responseVO = responseEntity.getBody();
            if (responseVO != null && responseVO.getData() != null && responseVO.getData().getCategories() != null) {
                alarmCategoryVOList = responseVO.getData().getCategories();
            }
        } catch (Exception e) {
            log.warn(
                    "Cannot request rule service to get alarm categories!" + " Detail request: {}. Detail message : {}",
                    requestVO, e.getMessage());

            return new AgencySettingsResultObject();
        }

        AgencySettingsVO settingsVO = AgencySettingsMapper.fromAgencySettings(settings);
        settingsVO.setAlarmCategoryList(alarmCategoryVOList);
        settingsVO.acceptTranslator(translator);
        return new AgencySettingsResultObject(settingsVO);
    }

    public AgencySettingsResultObject getMapSettings(Integer agencyId) {
        AgencySettingsResultObject resultObject = getAgencySettings(agencyId);

        // Filter out metrics which will not be shown in Dashboard map
        resultObject.getData().getDisplaySettingsVO().setAlarmRecordNumDisplaySettingVO(null);
        resultObject.getData().getDisplaySettingsVO().setCoordHealthDisplaySettingVO(null);
        resultObject.getData().getDisplaySettingsVO().setSfDisplaySettingVO(null);
        resultObject.getData().getDisplaySettingsVO().setAvgAppDelayDisplaySettingVO(null);
        // TODO Other new metrics
        return resultObject;
    }

    /**
     * @param agencyId
     * @param requestVO
     * @return
     */
    public AgencySettingsResultObject setAgencySettings(Integer agencyId, SetAgencySettingsRequestVO requestVO) {
        if (agencyId == null || requestVO == null) {
            throw new IllegalArgumentException();
        }

        AgencySettings settings;
        try {
            settings = updateOrCreateAgencySettings(agencyId, requestVO);
            updateAgencyAlarmCategories(agencyId, requestVO);
        } catch (JsonProcessingException e) {
            log.error("Cannot parse agency settings to json string!", e);
            return new AgencySettingsResultObject(null, AgencySettingsStatusCode.INVALID_AGENCY_SETTINGS);
        } catch (UpdateAlarmCategoryException e) {
            log.error("Cannot update alarm categories!", e);
            return new AgencySettingsResultObject(null, AgencySettingsStatusCode.UNKNOWN_ERROR);
        }

        // settings must not be null here
        agencySettingsRepo.save(settings);

        return new AgencySettingsResultObject(null, AgencySettingsStatusCode.NO_CONTENT);
    }

    private void updateAgencyAlarmCategories(Integer agencyId, SetAgencySettingsRequestVO requestVO)
            throws UpdateAlarmCategoryException {
        AlarmCategoryUpdateRequestVO alarmCategoryUpdateRequestVO = AlarmCategoryUpdateRequestVO.builder()
                .agencyId(agencyId)
                .alarmCategoryVOList(requestVO.getAlarmCategoryList())
                .build();

        ResponseEntity<SimpleResultObject> responseEntity;

        try {
            responseEntity = AlarmCategoryInterComController
                    .invokeUpdateAlarmCategories(spmConfig.getRuleServiceEndpoint(), alarmCategoryUpdateRequestVO);
        } catch (Exception e) {
            log.error("Cannot request rule service to update alarm categories!" + " Detail request: " + requestVO, e);
            throw new UpdateAlarmCategoryException();
        }

        if (!SimpleResultObject.SimpleStatusCode.SUCCESS.getHttpStatus().equals(responseEntity.getStatusCode())) {
            throw new UpdateAlarmCategoryException();
        }
    }

    /**
     * Update existing or create new Agency settings basing on default settings and request data
     *
     * @param agencyId
     * @param requestVO
     * @return
     * @throws JsonProcessingException
     */
    private AgencySettings updateOrCreateAgencySettings(Integer agencyId, SetAgencySettingsRequestVO requestVO)
            throws JsonProcessingException {
        if (agencyId == null || requestVO == null)
            throw new IllegalArgumentException();

        Optional<AgencySettings> settingsOpt = agencySettingsRepo.findById(agencyId);

        // Update existing settings
        // Create new settings based on default settings and request data
        AgencySettings settings = settingsOpt.orElseGet(() -> AgencySettings.getDefault(agencyId));

        // Merge with requested Agency settings

        // 1.Merge display settings
        DisplaySettingsVO reqDisplaySettingsVO = requestVO.getDisplaySettingsVO();
        if (reqDisplaySettingsVO != null) {
            settings.setDisplaySettingsVO(reqDisplaySettingsVO);
        }

        // 2.Update time zone
        String zoneOffset = requestVO.getZoneOffset();
        if (StringUtils.hasText(zoneOffset)) {
            settings.setZoneOffset(zoneOffset);
        }

        //3. Update saturation
        AgencySaturationVO agencySaturationVO = requestVO.getSaturationVO();
        settings.setAgencySaturationVO(agencySaturationVO);

        // 4.Update showUnavailableMapData
        Boolean showUnavailableMapData = requestVO.getShowUnavailableMapData();
        if (Objects.nonNull(requestVO.getShowUnavailableMapData())) {
            settings.setShowUnavailableMapData(showUnavailableMapData);
        }

        return settings;
    }

}
