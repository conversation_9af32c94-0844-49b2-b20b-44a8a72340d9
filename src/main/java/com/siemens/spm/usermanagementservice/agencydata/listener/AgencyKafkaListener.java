/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AgencyKafkaListener.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.agencydata.listener;

import com.siemens.spm.common.agency.kafka.dto.AgencySchemaPayloadDto;
import com.siemens.spm.common.agency.kafka.listener.AbstractAgencyKafkaListener;
import com.siemens.spm.common.agency.supports.provision.EnableAgencyProvisionAutoConfiguration;
import com.siemens.spm.common.kafka.common.KafkaDeserializerFactory;
import com.siemens.spm.common.kafka.config.KafkaConsumerConfig;
import com.siemens.spm.usermanagementservice.agencydata.boundary.AgencyKafkaProcessorFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerGroupMetadata;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Component
@ConditionalOnBean(annotation = EnableAgencyProvisionAutoConfiguration.class)
@ConditionalOnProperty(value = "spring.kafka.enable", havingValue = "true")
public class AgencyKafkaListener extends AbstractAgencyKafkaListener {

    @Value("${spring.kafka.topic.agency.name:datahub-delegate-agency-changed}")
    private String topicName;

    public AgencyKafkaListener(AgencyKafkaProcessorFacade agencyKafkaFacade) {
        super(agencyKafkaFacade,
                KafkaDeserializerFactory.constructJsonDeserializer(AgencySchemaPayloadDto.class, false));
    }

    @Override
    @KafkaListener(
            topics = "${spring.kafka.topic.agency.name:datahub-delegate-agency-changed}",
            containerFactory = KafkaConsumerConfig.CONCURRENT_CONSUMER_LISTENER,
            groupId = "${spring.kafka.topic.agency.group-id:insights-user-service-agency}"
    )
    public void listen(@Headers Map<String, Object> headers, ConsumerRecord<String, String> event,
                       KafkaConsumer<String, String> consumer) {
        Optional<ConsumerGroupMetadata> consumerGroupMetadataOpt = Optional.ofNullable(consumer.groupMetadata());
        log.info("Processing event with offset {} and partition {} by consumerId {} in groupId {} ", event.offset(),
                event.partition(), consumerGroupMetadataOpt.map(ConsumerGroupMetadata::memberId).orElse(null),
                consumerGroupMetadataOpt.map(ConsumerGroupMetadata::memberId).orElse(null));

        AgencySchemaPayloadDto eventVO = convertRawDataToEvent(event.value());

        doBusinessLogic(eventVO);

        log.info("Done processing event with eventId : {}", eventVO.getId());
    }

    @Override
    protected AgencySchemaPayloadDto convertRawDataToEvent(String rawData) {
        return deserializer.deserialize(topicName, rawData.getBytes(StandardCharsets.UTF_8));
    }

}
