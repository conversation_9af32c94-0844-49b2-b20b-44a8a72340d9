/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AgencySearchStrategyBean.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.agencydata.strategy;

import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.siemens.spm.spmstudiosdk.dto.StudioAgencyDto;
import com.siemens.spm.spmstudiosdk.exception.StudioException;
import com.siemens.spm.spmstudiosdk.service.StudioAgencyService;
import com.siemens.spm.usermanagementservice.agencydata.util.AgencyVOBuilder;
import com.siemens.spm.usermanagementservice.api.vo.AgencyDetailVO;
import com.siemens.spm.usermanagementservice.api.vo.response.AgencyDetailResultObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@Transactional
public class AgencySearchStrategyBean implements AgencySearchStrategy {

    @Autowired
    private StudioAgencyService studioAgencyService;

    /**
     * {@inheritDoc}
     */
    @Override
    public AgencyDetailResultObject getAgencyDetail(Integer agencyId) {
        Optional<StudioAgencyDto> studioAgencyDtoOptional;
        try {
            studioAgencyDtoOptional = studioAgencyService.getAgencyById(agencyId);
        } catch (StudioException e) {
            log.error("Error occurs when get all agencies from Studio", e);

            return new AgencyDetailResultObject(null, AgencyDetailResultObject.StatusCode.ERROR);
        }

        if (studioAgencyDtoOptional.isEmpty()) {
            return new AgencyDetailResultObject(null, AgencyDetailResultObject.StatusCode.AGENCY_NOT_FOUND);
        }

        AgencyDetailVO agencyDetailVO = AgencyVOBuilder.buildAgencyDetailVO(studioAgencyDtoOptional.get());

        return new AgencyDetailResultObject(agencyDetailVO, AgencyDetailResultObject.StatusCode.SUCCESS);
    }

}
