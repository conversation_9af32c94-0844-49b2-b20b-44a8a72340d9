/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AgencyBaseInfoMgmtStrategy.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.agencydata.strategy;

import com.siemens.spm.common.agency.master.entity.AgencySchema;
import lombok.NonNull;

public interface AgencyBaseInfoMgmtStrategy {
    void updateAgencySettingBaseInfoFromAgencySchema(@NonNull AgencySchema agencySchema);
}
