/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AgencyVOBuilder.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.agencydata.util;

import com.siemens.spm.common.constant.AgencyConstants;
import com.siemens.spm.spmstudiosdk.dto.StudioAgencyDto;
import com.siemens.spm.usermanagementservice.api.vo.AgencyDetailVO;

public final class AgencyVOBuilder {

    private AgencyVOBuilder() {
    }

    public static AgencyDetailVO buildAgencyDetailVO(StudioAgencyDto studioAgencyDto) {
        if (studioAgencyDto == null) {
            throw new IllegalArgumentException();
        }

        return AgencyDetailVO.builder()
                .id(studioAgencyDto.getAgencyNo())
                .name(studioAgencyDto.getAgencyName())
                .status(Boolean.TRUE.equals(studioAgencyDto.getActivated()) ?
                        AgencyConstants.Status.ACTIVE.name() :
                        AgencyConstants.Status.INACTIVE.name()
                )
                .build();
    }
}
