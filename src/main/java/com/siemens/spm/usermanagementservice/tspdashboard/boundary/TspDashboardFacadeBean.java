package com.siemens.spm.usermanagementservice.tspdashboard.boundary;

import com.siemens.spm.datahub.api.vo.DataHubTspEvent;
import com.siemens.spm.usermanagementservice.api.boundary.TspDashboardService;
import com.siemens.spm.usermanagementservice.api.vo.request.IntersectionForTspDashboardMapSearchRequest;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionListForTspDashboardMapResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.TspEventInternalResultObject;
import com.siemens.spm.usermanagementservice.tspdashboard.strategy.TspDashboardStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class TspDashboardFacadeBean implements TspDashboardService {

    @Autowired
    private TspDashboardStrategy tspDashboardStrategy;

    @Override
    public TspEventInternalResultObject sendTspEvent(DataHubTspEvent dataHubTspEvent) {
        return tspDashboardStrategy.sendTspEvent(dataHubTspEvent);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public IntersectionListForTspDashboardMapResultObject getIntersectionsForTspMap(
            IntersectionForTspDashboardMapSearchRequest searchRequest) {
        return tspDashboardStrategy.getIntersectionsForTspMap(searchRequest);
    }

}
