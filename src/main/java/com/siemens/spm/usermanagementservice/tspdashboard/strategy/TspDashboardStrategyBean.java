package com.siemens.spm.usermanagementservice.tspdashboard.strategy;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.querydsl.core.types.OrderSpecifier;
import com.siemens.spm.common.shared.domaintype.IntersectionStatus;
import com.siemens.spm.common.shared.exception.InvalidSortColumnException;
import com.siemens.spm.common.shared.exception.InvalidSortOrderException;
import com.siemens.spm.common.util.BeanFinder;
import com.siemens.spm.common.util.DateTimeUtils;
import com.siemens.spm.datahub.api.vo.DataHubTspEvent;
import com.siemens.spm.datahub.api.vo.response.DataHubVehicleLocation;
import com.siemens.spm.notification.service.NotificationSenderService;
import com.siemens.spm.perflogcrawler.api.vo.IntersectionPerflogInternalVO;
import com.siemens.spm.perflogcrawler.api.vo.request.IntersectionPerflogSearchRequest;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionForTspDashboardMapVO;
import com.siemens.spm.usermanagementservice.api.vo.TspEventIntersectionVO;
import com.siemens.spm.usermanagementservice.api.vo.VehicleLocationVO;
import com.siemens.spm.usermanagementservice.api.vo.request.IntersectionForTspDashboardMapSearchRequest;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionListForTspDashboardMapResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.TspEventInternalResultObject;
import com.siemens.spm.usermanagementservice.awsdata.vo.WebSocketContentVO;
import com.siemens.spm.usermanagementservice.awsdata.vo.WebSocketTopic;
import com.siemens.spm.usermanagementservice.dashboarddata.strategy.intercom.IntersectionPerflogStrategy;
import com.siemens.spm.usermanagementservice.dashboarddata.strategy.util.IntersectionForDashboardMapSearchHelper;
import com.siemens.spm.usermanagementservice.domain.Intersection;
import com.siemens.spm.usermanagementservice.repository.IntersectionRepository;
import com.siemens.spm.usermanagementservice.repository.filterdata.IntersectionFilterDataVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.time.Duration;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR> Nguyen & Anh Mai
 */
@Slf4j
@Service
@Transactional
public class TspDashboardStrategyBean implements TspDashboardStrategy {

    @Autowired
    private IntersectionRepository intersectionRepository;

    @Autowired
    private NotificationSenderService notificationSenderService;

    @Autowired
    private IntersectionPerflogStrategy intersectionPerflogStrategy;

    private static final int LAST_TSP_EVENT_TIME_DURATION_HOURS = 24;

    /**
     * {@inheritDoc}
     */
    @Override
    public TspEventInternalResultObject sendTspEvent(DataHubTspEvent dataHubTspEvent) {
        var intersectionForTspDashboardMapVO = intersectionForTspDashboardMapVOFromDataHubTspEvent(dataHubTspEvent);
        if (intersectionForTspDashboardMapVO == null) {
            log.error("Can not retrieve intersection from tsp event");

            return new TspEventInternalResultObject(TspEventInternalResultObject.StatusCode.ERROR);
        }

        WebSocketContentVO webSocketContentVO = WebSocketContentVO.builder()
                .topic(WebSocketTopic.TSP_EVENT.getValue())
                .tspIntersection(intersectionForTspDashboardMapVO)
                .build();

        String plainTspEvent;
        try {
            plainTspEvent = BeanFinder.getDefaultObjectMapper().writeValueAsString(webSocketContentVO);
        } catch (JsonProcessingException e) {
            log.error("Can not convert tsp event to json string", e);
            return new TspEventInternalResultObject(TspEventInternalResultObject.StatusCode.ERROR);
        }

        //        Iterable<TspWebSocketConnection> tspWebSocketConnectionIterable = tspWebSocketConnectionRepository.findAll();
        //        for (var tspWebSocketConnection : tspWebSocketConnectionIterable) {
        //            //  Check condition to send TSP event or not?
        //            if (!checkConditionSendTspEvent(tspWebSocketConnection, dataHubTspEvent)) {
        //                log.debug("The websocket client: {} is not available to receive TSP event: {}", tspWebSocketConnection,
        //                        dataHubTspEvent);
        //                continue;
        //            }
        //
        //            WebSocketRequestVO webSocketRequestVO = WebSocketRequestVO.builder()
        //                    .connectionId(tspWebSocketConnection.getId())
        //                    .message(plainTspEvent)
        //                    .build();
        //
        //            try {
        //                log.debug("Starting send TSP websocket msg: {}", webSocketRequestVO);
        //                notificationSenderService.sendMessage(webSocketRequestVO);
        //            } catch (NotificationSenderException e) {
        //                log.error("Exception occur while trying to send TSP websocket message", e);
        //                // NOTE: Should not return error here to ensure that event message must be sent to all other connections
        //            }
        //        }

        return new TspEventInternalResultObject(TspEventInternalResultObject.StatusCode.SUCCESS);
    }

    private TspEventIntersectionVO intersectionForTspDashboardMapVOFromDataHubTspEvent(
            DataHubTspEvent dataHubTspEvent) {
        Optional<Intersection> intersectionOptional = intersectionRepository
                .findById(dataHubTspEvent.getIntersectionUUID());
        if (intersectionOptional.isEmpty()) {
            return null;
        }

        Timestamp lastTspEventTime = DateTimeUtils.timestampFromLocalDateTime(dataHubTspEvent.getDateTimestamp(),
                ZoneId.of("UTC"));

        DataHubVehicleLocation vehicleLocation = dataHubTspEvent.getVehicleLocation();
        VehicleLocationVO vehicleLocationVO = VehicleLocationVO.builder()
                .latitude((double) vehicleLocation.getLatitude() / VehicleLocationVO.MICRO_RATIO)
                .longitude((double) vehicleLocation.getLongitude() / VehicleLocationVO.MICRO_RATIO)
                .heading(vehicleLocation.getHeading())
                .build();

        return TspEventIntersectionVO.builder()
                .id(dataHubTspEvent.getIntersectionUUID())
                .lastTspEventTime(lastTspEventTime)
                .vehicleId(dataHubTspEvent.getVehicleId())
                .requestId(dataHubTspEvent.getVehicleId())
                .routeNumber(dataHubTspEvent.getRouteNumber())
                .vehicleLocation(vehicleLocationVO)
                .build();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public IntersectionListForTspDashboardMapResultObject getIntersectionsForTspMap(
            IntersectionForTspDashboardMapSearchRequest searchRequest) {
        Pair<List<Intersection>, Long> intersectionCountPair;
        try {
            intersectionCountPair = getAndCountFromDB(searchRequest);
        } catch (InvalidSortColumnException ex) {
            log.error("Invalid sort column", ex);

            return new IntersectionListForTspDashboardMapResultObject(null,
                    IntersectionListForTspDashboardMapResultObject.StatusCode.INVALID_SORT_COLUMN);
        } catch (InvalidSortOrderException ex) {
            log.error("Invalid sort order", ex);

            return new IntersectionListForTspDashboardMapResultObject(null,
                    IntersectionListForTspDashboardMapResultObject.StatusCode.INVALID_SORT_ORDER);
        }

        List<Intersection> intersections = intersectionCountPair.getFirst();
        List<String> intersectionIds = intersections.stream()
                .map(Intersection::getId)
                .toList();

        IntersectionPerflogSearchRequest intersectionPerflogSearchRequest = IntersectionPerflogSearchRequest
                .builder()
                .agencyId(searchRequest.getAgencyId())
                .ids(intersectionIds)
                .build();

        Map<String, IntersectionPerflogInternalVO> intersectionPerflogs = intersectionPerflogStrategy
                .searchIntersectionPerflog(intersectionPerflogSearchRequest);

        List<IntersectionForTspDashboardMapVO> intersectionForTspDashboardMapVOs = createIntersectionForTspDashboard(
                intersections,
                intersectionPerflogs);

        var responseData = IntersectionListForTspDashboardMapResultObject
                .ResponseData.builder()
                .intersections(intersectionForTspDashboardMapVOs)
                .totalCount(intersectionCountPair.getSecond())
                .build();

        // Update current state of TSP dashboard
        updateTspDashboardWebSocketConnection(searchRequest);

        return new IntersectionListForTspDashboardMapResultObject(responseData,
                IntersectionListForTspDashboardMapResultObject.StatusCode.SUCCESS);
    }

    /**
     * Update TSP WebSocket Connection
     */
    private void updateTspDashboardWebSocketConnection(IntersectionForTspDashboardMapSearchRequest searchRequest) {
        //        Long userId = SecurityUtils.getCurrentUserId();

        //        List<TspWebSocketConnection> tspWebSocketConnectionList = tspWebSocketConnectionRepository
        //                .findByUserIdAndAgencyId(userId, agencyId);
        //        if (tspWebSocketConnectionList != null && !tspWebSocketConnectionList.isEmpty()) {
        //            for (var tspWebSocketConnection : tspWebSocketConnectionList) {
        //                tspWebSocketConnection.setTspAgencyId(searchRequest.getAgencyId());
        //
        //                Double[] topLeft = searchRequest.getTopLeft();
        //                if (topLeft.length == 2) {
        //                    LocationVO topLeftLocation = LocationVO.builder()
        //                            .latitude(topLeft[0])
        //                            .longitude(topLeft[1])
        //                            .build();
        //                    tspWebSocketConnection.setTopLeftMapView(topLeftLocation);
        //                }
        //
        //                Double[] bottomRight = searchRequest.getBottomRight();
        //                if (bottomRight.length == 2) {
        //                    LocationVO bottomRightLocation = LocationVO.builder()
        //                            .latitude(bottomRight[0])
        //                            .longitude(bottomRight[1])
        //                            .build();
        //                    tspWebSocketConnection.setBottomRightMapView(bottomRightLocation);
        //                }
        //            }
        //
        //            tspWebSocketConnectionRepository.saveAll(tspWebSocketConnectionList);
        //        }
    }

    private Pair<List<Intersection>, Long> getAndCountFromDB(
            IntersectionForTspDashboardMapSearchRequest searchRequest) {
        OrderSpecifier<String>[] orderSpecifiers = IntersectionForDashboardMapSearchHelper
                .createOrderBy(searchRequest.getOrderByColumns());

        Integer agencyId = searchRequest.getAgencyId();
        Double[] bottomRight = searchRequest.getBottomRight();
        Double[] topLeft = searchRequest.getTopLeft();
        Integer page = searchRequest.getPage();
        Integer size = searchRequest.getSize();

        IntersectionFilterDataVO filterDataVO = IntersectionFilterDataVO.builder()
                .agencyId(agencyId)
                .bottomRight(bottomRight)
                .topLeft(topLeft)
                .status(IntersectionStatus.AVAILABLE.name())
                .build();
        List<Intersection> intersections = intersectionRepository
                .findPageByFilter(filterDataVO, orderSpecifiers, page, size);
        Long totalCount = intersectionRepository.countTotalByFilter(filterDataVO);

        return Pair.of(intersections, totalCount);
    }

    /**
     * Create list of {@link IntersectionForTspDashboardMapVO} from list of {@link Intersection} and map of {@code
     * Map<String, IntersectionPerflogInternalVO>}
     *
     * @param intersections
     * @param intersectionPerflogInternalVOMap
     * @return {@code List<IntersectionForTspDashboardMapVO>}
     */
    private List<IntersectionForTspDashboardMapVO> createIntersectionForTspDashboard(
            List<Intersection> intersections,
            Map<String, IntersectionPerflogInternalVO> intersectionPerflogInternalVOMap) {
        List<IntersectionForTspDashboardMapVO> results = new ArrayList<>();

        // Only consider intersection have tspUpdatedTime in a specific duration relative to current time
        for (Intersection intersection : intersections) {
            String intersectionId = intersection.getId();
            IntersectionPerflogInternalVO intersectionPerflog = intersectionPerflogInternalVOMap.get(intersectionId);
            IntersectionForTspDashboardMapVO intersectionForTspDashboardMapVO = IntersectionForTspDashboardMapVO
                    .builder()
                    .id(intersection.getId())
                    .name(intersection.getName())
                    .lat(intersection.getLatitude())
                    .lon(intersection.getLongitude())
                    .lastTspEventTime(null)
                    .build();
            if (intersectionPerflog != null && intersectionPerflog.getTspUpdatedTime() != null) {
                Timestamp tspUpdatedTime = intersectionPerflog.getTspUpdatedTime();
                Timestamp tspUpdatedTimePlusDuration = new Timestamp(
                        tspUpdatedTime.getTime() + Duration.ofHours(LAST_TSP_EVENT_TIME_DURATION_HOURS).toMillis());
                Date currentTime = new Date();
                if (tspUpdatedTimePlusDuration.after(currentTime)) {
                    intersectionForTspDashboardMapVO.setLastTspEventTime(tspUpdatedTime);
                }
            }
            results.add(intersectionForTspDashboardMapVO);
        }

        return results;
    }

}
