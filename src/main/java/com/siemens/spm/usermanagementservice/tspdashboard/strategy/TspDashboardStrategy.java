package com.siemens.spm.usermanagementservice.tspdashboard.strategy;

import com.siemens.spm.datahub.api.vo.DataHubTspEvent;
import com.siemens.spm.usermanagementservice.api.vo.request.IntersectionForTspDashboardMapSearchRequest;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionListForTspDashboardMapResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.TspEventInternalResultObject;

public interface TspDashboardStrategy {

    /**
     * Send TSP event
     *
     * @param dataHubTspEvent
     * @return
     */
    TspEventInternalResultObject sendTspEvent(DataHubTspEvent dataHubTspEvent);

    /**
     * Get intersections for map of tsp dashboard
     *
     * @param searchRequest {@link IntersectionForTspDashboardMapSearchRequest}
     * @return {@link IntersectionListForTspDashboardMapResultObject}
     */
    IntersectionListForTspDashboardMapResultObject getIntersectionsForTspMap(IntersectionForTspDashboardMapSearchRequest searchRequest);

}
