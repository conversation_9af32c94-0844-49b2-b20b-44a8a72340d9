package com.siemens.spm.usermanagementservice.tactic.boundary;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.siemens.spm.usermanagementservice.api.boundary.TacticIntegrationService;
import com.siemens.spm.usermanagementservice.api.vo.response.TacticIntegrationResultObject;
import com.siemens.spm.usermanagementservice.tactic.strategy.TacticIntegrationStrategy;

@Service
public class TacticIntegrationFacadeBean implements TacticIntegrationService {

	@Autowired
	private TacticIntegrationStrategy strategy;

	@Override
	public TacticIntegrationResultObject redirectToAnalysis(String apiKey, String agencyId) throws Exception {
		return strategy.redirectToAnalysis(apiKey, agencyId);
	}

}
