package com.siemens.spm.usermanagementservice.tactic.strategy;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.siemens.spm.usermanagementservice.api.vo.response.TacticIntegrationResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.TacticIntegrationResultObject.TacticIntegrationStatusCode;
import com.siemens.spm.usermanagementservice.config.TacticConfig;
import com.siemens.spm.usermanagementservice.config.UserConfig;
import com.siemens.spm.usermanagementservice.repository.UserRepository;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class TacticIntegrationStrategyBean implements TacticIntegrationStrategy {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserConfig userConfig;

    @Autowired
    private TacticConfig tacticConfig;

    @Override
    public TacticIntegrationResultObject redirectToAnalysis(String apiKey, String agencyId) {
        //        if (apiKey == null) {
        //            log.error("Api key should not be null!");
        //            return new TacticIntegrationResultObject(null, TacticIntegrationStatusCode.INVALID_API_KEY);
        //        }
        //
        //        if (agencyId == null) {
        //            log.error("Agency should not be null!");
        //            return new TacticIntegrationResultObject(null, TacticIntegrationStatusCode.AGENCY_NOT_FOUND);
        //        }
        //
        //        String cipherText;
        //        try {
        //            cipherText = SymmetricAesEncryption.encode(apiKey, userConfig.getSpmSymmetricKey());
        //        } catch (AesException e) {
        //            throw new TacticException(e);
        //        }
        //        User user = userRepository.findOneByUserKey(cipherText);
        //        if (user == null) {
        //            log.error("User was not found by api key: {}", apiKey);
        //            return new TacticIntegrationResultObject(null, TacticIntegrationStatusCode.INVALID_API_KEY);
        //        }
        //
        //        Optional<Agency> agencyOpt = agencyRepository.findById(agencyId);
        //        if (agencyOpt.isEmpty()) {
        //            log.error("Agency was not found by agency id: {}", agencyId);
        //            return new TacticIntegrationResultObject(null, TacticIntegrationStatusCode.AGENCY_NOT_FOUND);
        //        }
        //
        //        List<AgencyUser> agencyUserList = agencyUserRepository.findAllByUserId(user.getId());
        //        if (agencyUserList == null || agencyUserList.isEmpty()) {
        //            log.error("No agency was found by user id: {}", user.getId());
        //            return new TacticIntegrationResultObject(null,
        //                    TacticIntegrationStatusCode.USER_DID_NOT_HAVE_ACCESS_TO_AGENCY);
        //        }
        //
        //        try {
        //            for (AgencyUser agencyUser : agencyUserList) {
        //                if (agencyId.equals(agencyUser.getAgencyId())
        //                        || UUIDConstants.ALL_AGENCIES_AGENCY_ID.equals(agencyUser.getAgencyId())) {
        //                    // Using the user and agency instead of agency_user to have the correct response
        //                    TacticIntegrationVO vo = buildResponse(user, agencyOpt.get(), agencyUser.getRoleName());
        //                    return new TacticIntegrationResultObject(vo, TacticIntegrationStatusCode.SUCCESS);
        //                }
        //            }
        //        } catch (Exception e) {
        //            throw new TacticException(e);
        //        }

        return new TacticIntegrationResultObject(null, TacticIntegrationStatusCode.USER_DID_NOT_HAVE_ACCESS_TO_AGENCY);
    }

    //    private TacticIntegrationVO buildResponse(User user, Agency agency, String roleName) {
    //        TokenPair tokenPair = generateAccessToken(user, agency, roleName);
    //        TacticIntegrationVO vo = new TacticIntegrationVO();
    //        vo.setRedirectUrl(userConfig.getWebUiEndpoint() + tacticConfig.getAnalysisPage());
    //        vo.setAccessToken(tokenPair.getAccessToken());
    //        vo.setRefreshToken(tokenPair.getRefreshToken());
    //
    //        return vo;
    //    }
    //
    //    private TokenPair generateAccessToken(User user, Agency agency, String roleName) {
    //        List<RolePermission> rolePermissions = rolePermissionRepository.findAllByRoleName(roleName);
    //        Authentication auth = new UsernamePasswordAuthenticationToken(user.getEmail(), "",
    //                AuthenticationUtil.getAnalysisOnlyAuthority(user, agency, rolePermissions));
    //
    //        Map<String, Object> additionalClaims = new HashMap<>();
    //        //        additionalClaims.put(JWTConstants.ACCESS_TOKEN_TYPE, AccessTokenType.TACTIC.getCode());
    //
    //        return new TokenPair("", 10, "", 10);
    //    }

}
