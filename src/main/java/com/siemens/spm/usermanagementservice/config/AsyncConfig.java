/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AsyncConfig.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.config;

import com.siemens.spm.common.constant.SProfile;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.core.task.TaskDecorator;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import java.util.Map;
import java.util.concurrent.Executor;

/**
 * NOTE: @Async has two limitations:
 * - it must be applied to public methods only
 * - self-invocation – calling the async method from within the same class – won't work
 */
@Configuration
@EnableAsync
@Profile({"!" + SProfile.SPRING_BOOT_TEST})
public class AsyncConfig implements AsyncConfigurer {

    @Value("${spring.scheduling.core_pool_size}")
    private int corePoolSize;

    @Value("${spring.scheduling.max_pool_size}")
    private int maxPoolSize;

    @Value("${spring.scheduling.queue_size}")
    private int queueSize;

    public static final String SCHEDULE_ASYNC_EXECUTOR = "scheduleAsyncExecutor";

    public static final String RECONCILE_AGENCY_PROVISION_EXECUTOR = "reconcileAgencyProvisionExecutor";

    @Override
    public Executor getAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueSize);

        executor.setThreadNamePrefix("spm-async-");

        executor.setTaskDecorator(getTaskDecorator());

        executor.initialize();

        return executor;
    }

    @Bean(RECONCILE_AGENCY_PROVISION_EXECUTOR)
    public TaskScheduler reconcileAgencyProvisionScheduler() {
        ThreadPoolTaskScheduler threadPoolTaskScheduler = new ThreadPoolTaskScheduler();
        threadPoolTaskScheduler.setPoolSize(corePoolSize);
        threadPoolTaskScheduler.setThreadNamePrefix("reconcile-agency-provision-scheduler-");
        return threadPoolTaskScheduler;
    }

    @Bean(SCHEDULE_ASYNC_EXECUTOR)
    public Executor scheduleAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueSize);

        executor.setThreadNamePrefix("spm-schedule-");

        executor.setTaskDecorator(getTaskDecorator());

        executor.initialize();

        return executor;
    }

    private TaskDecorator getTaskDecorator() {
        return runnable -> {
            Map<String, String> contextMap = MDC.getCopyOfContextMap();
            return () -> {
                try {
                    // Set the captured MDC context to the new thread
                    if (contextMap != null) {
                        MDC.setContextMap(contextMap);
                    }
                    runnable.run();
                } finally {
                    // Clear the MDC context after the task completes
                    MDC.clear();
                }
            };
        };
    }
}