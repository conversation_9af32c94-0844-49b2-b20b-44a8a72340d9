/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : UserConfig.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.config;

import jakarta.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Configuration
@Getter
public class UserConfig {

    @Autowired
    private Environment env;

    private static final long MILLIS_SECOND_PER_DAY = 86400000;

    private int userKeyLifeTimeInDay;

    private int userKeyExpiryNotificationInDays;

    private String webUiEndpoint;

    private long notificationLifeTime;

    @PostConstruct
    private void init() {
        webUiEndpoint = env.getProperty("spm.web-ui.endpoint");

        // User-key properties
        Integer userKeyLifeTime = env.getProperty("user.user-key.lifetime-in-day", Integer.class);
        userKeyLifeTimeInDay = (userKeyLifeTime != null && userKeyLifeTime > 0) ? userKeyLifeTime : 90;

        Integer userKeyNotificationInDays = env.getProperty("user.user-key.expiration.notification-before-days",
                Integer.class);
        userKeyExpiryNotificationInDays = (userKeyNotificationInDays == null || userKeyNotificationInDays < 0)
                ? 10
                : userKeyNotificationInDays;

        // notification
        Long notificationLifeTimeInDays = env.getProperty("spm.notification.life-time", Long.class);
        notificationLifeTime = (notificationLifeTimeInDays == null || notificationLifeTimeInDays <= 0) ? 7
                : notificationLifeTimeInDays;
    }

}
