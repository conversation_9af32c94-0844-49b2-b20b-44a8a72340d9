/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : RedisConfig.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import jakarta.annotation.PostConstruct;

@Getter
@Setter
@Configuration
public class SpmConfig {

    /**
     * HTTP endpoint of analysis service, including port
     */
    private String analysisServiceEndpoint;

    /**
     * HTTP endpoint of rule service, including port
     */
    private String ruleServiceEndpoint;

    /**
     * HTTP endpoint of perflog service, including port
     */
    private String perflogServiceEndpoint;

    @Autowired
    private Environment env;

    @PostConstruct
    public void init() {
        analysisServiceEndpoint = env.getProperty("spm.analysis.endpoint");
        ruleServiceEndpoint = env.getProperty("spm.rule-service.endpoint");
        perflogServiceEndpoint = env.getProperty("spm.perflog-crawler-service.endpoint");
    }

}
