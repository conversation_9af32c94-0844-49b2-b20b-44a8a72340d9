package com.siemens.spm.usermanagementservice.config;

import java.util.List;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;

import com.siemens.spm.usermanagementservice.sse.SseMessageListener;

@Configuration
public class MessagingConfig {
    public static final String NOTIFICATION_CHANNEL = "notification";
    public static final String TSP_CHANNEL = "tsp";

    public static final List<String> SUPPORTED_TOPICS = List.of(NOTIFICATION_CHANNEL, TSP_CHANNEL);

    @Bean
    RedisMessageListenerContainer container(
            @Qualifier("jedisConnectionFactory")
            RedisConnectionFactory connectionFactory,
            MessageListenerAdapter listenerAdapter) {

        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        container.addMessageListener(listenerAdapter, ChannelTopic.of(NOTIFICATION_CHANNEL));

        return container;
    }

    @Bean
    MessageListenerAdapter listenerAdapter(SseMessageListener receiver) {
        return new MessageListenerAdapter(receiver);
    }

}
