/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : RedisConfig.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.config;

import com.siemens.spm.perflogcrawler.api.vo.PerfLogCrawlTask;
import com.siemens.spm.perflogcrawler.api.vo.PerfLogTask;
import com.siemens.spm.usermanagementservice.sse.SseMessage;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;

import java.time.Duration;
import java.time.temporal.ChronoUnit;

import static com.siemens.spm.common.constant.AgencyConstants.AGENCY_SCHEMA_NAME_CACHE;
import static com.siemens.spm.usermanagementservice.intersectiondata.strategy.LocationStrategyImpl.TIMEZONE_CACHE;

@Configuration
public class RedisConfig {

    @Value("${cache.config.timezone.ttl-in-days:30}")
    private int timezoneTtl;

    @Value("${cache.config.agency.ttl-in-minutes:10}")
    private int agencyTtl;

    @Value("${cache.config.cache-prefix:[INSIGHTS-USERS]}")
    private String cachePrefix;

    @Bean
    public RedisCacheManager redisCacheManager(JedisConnectionFactory jedisConnectionFactory) {

        RedisCacheConfiguration commonConfig = RedisCacheConfiguration
                .defaultCacheConfig()
                .prefixCacheNameWith(cachePrefix);

        return RedisCacheManager.builder(jedisConnectionFactory)
                .withCacheConfiguration(
                        TIMEZONE_CACHE,
                        commonConfig
                                .entryTtl(Duration.of(timezoneTtl, ChronoUnit.DAYS))
                )
                .withCacheConfiguration(
                        AGENCY_SCHEMA_NAME_CACHE,
                        commonConfig
                                .entryTtl(Duration.of(agencyTtl, ChronoUnit.MINUTES))
                )
                .build();
    }

    @Bean
    public RedisTemplate<String, PerfLogTask> perfLogTaskRedisTemplate(JedisConnectionFactory jedisConnectionFactory) {
        final RedisTemplate<String, PerfLogTask> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(jedisConnectionFactory);

        return redisTemplate;
    }

    @Bean
    public RedisTemplate<String, PerfLogCrawlTask> perfLogCrawlTaskRedisTemplate(JedisConnectionFactory jedisConnectionFactory) {
        final RedisTemplate<String, PerfLogCrawlTask> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(jedisConnectionFactory);

        return redisTemplate;
    }

    @Bean
    public RedisSerializer<SseMessage> sseMessageRedisSerializer() {
        return new Jackson2JsonRedisSerializer<>(SseMessage.class);
    }

    @Bean
    public RedisTemplate<String, SseMessage> sseRedisTemplate(JedisConnectionFactory jedisConnectionFactory) {
        final RedisTemplate<String, SseMessage> redisTemplate = new RedisTemplate<>();
        redisTemplate.setValueSerializer(sseMessageRedisSerializer());
        redisTemplate.setConnectionFactory(jedisConnectionFactory);

        return redisTemplate;
    }

}
