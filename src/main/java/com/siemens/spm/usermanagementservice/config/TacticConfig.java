package com.siemens.spm.usermanagementservice.config;

import jakarta.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Configuration
@Getter
public class TacticConfig {

    private static final String DEFAULT_ANALYSIS_PAGE = "/analysis/perfomance-metric";

    @Autowired
    private Environment env;

    private String analysisPage;

    @PostConstruct
    private void init() {
        this.analysisPage = env.getProperty("spm.tactic.analysis-page", DEFAULT_ANALYSIS_PAGE);
    }

}
