/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : SwaggerConfig.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import com.siemens.spm.common.constant.SProfile;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;

@Configuration
@Profile({ SProfile.DEFAULT, SProfile.NATIVE, SProfile.DEV, SProfile.QA })
public class OpenApiConfig {

    @Bean
    public OpenAPI openApi() {
        final Info info = new Info()
                .title("User management service API")
                .description("APIs for User management service")
                .version("2.0.0")
                .contact(new Contact()
                        .name("<PERSON><PERSON>")
                        .email("<EMAIL>")
                )
                .license(new License().name("Apache 2.0"));

        return new OpenAPI()
                .info(info);
    }

}
