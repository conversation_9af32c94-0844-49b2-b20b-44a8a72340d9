package com.siemens.spm.usermanagementservice.config;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import jakarta.annotation.PostConstruct;
import jakarta.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;

import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Configuration(value = CorsConfig.CORS_CONFIG_BEAN_NAME)
public class CorsConfig implements CorsConfigurationSource {

    public static final String CORS_CONFIG_BEAN_NAME = "spmCorsConfigurationSource";

    @Value("${cors.allowed-origins:*}")
    private String allowedOrigins;

    @PostConstruct
    void postConstruct() {
        log.info("CorsConfig initialized with allowedOrigins: {}", allowedOrigins);
    }

    @Override
    public CorsConfiguration getCorsConfiguration(@NonNull HttpServletRequest request) {
        CorsConfiguration config = new CorsConfiguration();
        config.setAllowCredentials(true);
        config.setAllowedOriginPatterns(List.of(allowedOrigins.split(",")));
        config.setAllowedHeaders(Collections.singletonList("*"));
        config.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "OPTIONS", "DELETE", "PATCH"));
        return config;
    }

}
