/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : SecurityConfig.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.annotation.web.configurers.HeadersConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.encrypt.Encryptors;
import org.springframework.security.crypto.encrypt.TextEncryptor;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.HttpStatusEntryPoint;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.web.cors.CorsConfigurationSource;

import java.util.List;

/**
 * Enable security configuration. This annotation denotes configuration for spring security.
 */
@Configuration
public class SecurityConfig {

    @Autowired
    @Qualifier(CorsConfig.CORS_CONFIG_BEAN_NAME)
    private CorsConfigurationSource corsConfigurationSource;

    private static final List<String> AUTH_WHITE_LIST = List.of(
            "/actuator/**", "/configuration/**",
            "/v2/api-docs", "/v3/api-docs/**",
            "/swagger-ui/**", "/swagger*/**", "/webjars/**",
            "/v3/api-docs.yaml"
    );

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        AntPathRequestMatcher[] authWhiteListAntPathRequestMatchers = AUTH_WHITE_LIST.stream()
                .map(AntPathRequestMatcher::antMatcher)
                .toArray(AntPathRequestMatcher[]::new);

        http
                .csrf(AbstractHttpConfigurer::disable)
                .headers(headersConfigurer -> headersConfigurer
                        // to make accessible h2 console, it works as frame
                        .frameOptions(HeadersConfigurer.FrameOptionsConfig::disable)
                )
                .cors(corsConfigurer -> corsConfigurer.configurationSource(corsConfigurationSource))
                .authorizeHttpRequests(authorize -> authorize
                        .requestMatchers(
                                AntPathRequestMatcher.antMatcher(HttpMethod.PUT, "/api/v1/intersections/sync-data")
                        ).permitAll()
                        .requestMatchers(
                                AntPathRequestMatcher.antMatcher(HttpMethod.GET, "/api/v1/auth-api-key")
                        ).permitAll()
                        .requestMatchers(authWhiteListAntPathRequestMatchers).permitAll()
                        .requestMatchers(AntPathRequestMatcher.antMatcher("/internal-api/**")).permitAll()
                        .anyRequest().permitAll()
                )
                .exceptionHandling(e -> e.authenticationEntryPoint(new HttpStatusEntryPoint(HttpStatus.UNAUTHORIZED)))
                .sessionManagement(sessionMgmtConfigurer -> sessionMgmtConfigurer
                        .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                )
                .oauth2ResourceServer(oauth2ResourceServerConfigurer -> oauth2ResourceServerConfigurer
                        .jwt(Customizer.withDefaults())
                );

        return http.build();
    }

    @Bean
    public TextEncryptor textEncryptor() {
        return Encryptors.noOpText();
    }

}
