package com.siemens.spm.usermanagementservice.api.resource;

import java.net.URI;
import java.net.URISyntaxException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.util.UriComponentsBuilder;

import com.siemens.spm.common.security.SecurityConstants;
import com.siemens.spm.common.util.ResponseUtil;
import com.siemens.spm.usermanagementservice.api.boundary.TacticIntegrationService;
import com.siemens.spm.usermanagementservice.api.controller.TacticIntegrationController;
import com.siemens.spm.usermanagementservice.api.vo.TacticIntegrationVO;
import com.siemens.spm.usermanagementservice.api.vo.response.TacticIntegrationResultObject;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
public class TacticIntegrationResource implements TacticIntegrationController {

    @Autowired
    private TacticIntegrationService tacticIntegrationService;

    @Override
    public ResponseEntity<Object> redirectToAnalysisPage(String apiKey,
                                                         String agencyId,
                                                         String ruleId,
                                                         String intersectionId,
                                                         String analysisId,
                                                         String fromTime,
                                                         String toTime,
                                                         HttpServletResponse response) {
        ResponseEntity<Object> ret;

        TacticIntegrationResultObject resultObject = null;
        try {
            resultObject = tacticIntegrationService.redirectToAnalysis(apiKey, agencyId);
        } catch (Exception e) {
            log.error("Error occurs!", e);
        }

        if (resultObject == null) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }

        if (resultObject.isSuccessful()) {
            TacticIntegrationVO vo = resultObject.getData();

            try {
                URI redirectUrl = createAnalysisRedirectURI(vo.getRedirectUrl(), agencyId, ruleId, intersectionId,
                        analysisId, fromTime,
                        toTime);
                HttpHeaders httpHeaders = new HttpHeaders();
                httpHeaders.setLocation(redirectUrl);

                Cookie accessTokenCookie = new Cookie(SecurityConstants.ACCESS_TOKEN_COOKIE, vo.getAccessToken());
                accessTokenCookie.setHttpOnly(true);
                //                accessTokenCookie.setSecure(true);
                accessTokenCookie.setPath("/");

                Cookie refreshTokenCookie = new Cookie(SecurityConstants.REFRESH_TOKEN_COOKIE, vo.getRefreshToken());
                refreshTokenCookie.setHttpOnly(true);
                //                refreshTokenCookie.setSecure(true);
                refreshTokenCookie.setPath("/");

                response.addCookie(accessTokenCookie);
                response.addCookie(refreshTokenCookie);

                ret = new ResponseEntity<>(httpHeaders, HttpStatus.MOVED_PERMANENTLY);
            } catch (URISyntaxException e) {
                log.error(e.getMessage(), e);
                ret = new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
            }
        } else {
            ResponseEntity<TacticIntegrationResultObject> responseEntity = ResponseUtil.wrapOrNotFound(resultObject);
            ret = new ResponseEntity<>(responseEntity.getBody(), responseEntity.getStatusCode());
        }

        return ret;
    }

    @Override
    public ResponseEntity<Object> redirectToAnalysis(String apiKey,
                                                     String agencyId,
                                                     String ruleId,
                                                     String intersectionId,
                                                     String analysisId,
                                                     String fromTime,
                                                     String toTime,
                                                     HttpServletResponse response) {
        ResponseEntity<Object> ret;

        TacticIntegrationResultObject resultObject = null;
        try {
            resultObject = tacticIntegrationService.redirectToAnalysis(apiKey, agencyId);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        if (resultObject == null) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }

        if (resultObject.isSuccessful()) {
            TacticIntegrationVO vo = resultObject.getData();

            try {
                URI redirectUrl = createAnalysisRedirectURI(vo.getRedirectUrl(), agencyId, ruleId, intersectionId,
                        analysisId, fromTime,
                        toTime);
                HttpHeaders httpHeaders = new HttpHeaders();
                httpHeaders.setLocation(redirectUrl);

                Cookie accessTokenCookie = new Cookie(SecurityConstants.ACCESS_TOKEN_COOKIE, vo.getAccessToken());
                accessTokenCookie.setHttpOnly(true);
                //                accessTokenCookie.setSecure(true);
                accessTokenCookie.setPath("/");

                Cookie refreshTokenCookie = new Cookie(SecurityConstants.REFRESH_TOKEN_COOKIE, vo.getRefreshToken());
                refreshTokenCookie.setHttpOnly(true);
                //                refreshTokenCookie.setSecure(true);
                refreshTokenCookie.setPath("/");

                response.addCookie(accessTokenCookie);
                response.addCookie(refreshTokenCookie);

                ret = new ResponseEntity<>(httpHeaders, HttpStatus.MOVED_PERMANENTLY);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                ret = new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
            }
        } else {
            ResponseEntity<TacticIntegrationResultObject> responseEntity = ResponseUtil.wrapOrNotFound(resultObject);
            ret = new ResponseEntity<>(responseEntity.getBody(), responseEntity.getStatusCode());
        }

        return ret;
    }

    private URI createAnalysisRedirectURI(String url,
                                          String agencyId,
                                          String ruleId,
                                          String intersectionId,
                                          String analysisId,
                                          String fromTime,
                                          String toTime)
            throws URISyntaxException {

        URI redirectUrl = new URI(url);
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.newInstance()
                .uri(redirectUrl)
                .queryParam(AGENCY_ID_PARAM, agencyId);

        if (ruleId != null) {
            uriBuilder.queryParam(RULE_ID_PARAM, ruleId);
        }

        if (intersectionId != null) {
            // Lower case intersection id from TACTICS
            intersectionId = intersectionId.toLowerCase();

            uriBuilder.queryParam(INTERSECTION_ID_PARAM, intersectionId);
        }

        if (analysisId != null) {
            uriBuilder.queryParam(ANALYSIS_ID_PARAM, analysisId);
        }

        if (fromTime != null) {
            uriBuilder.queryParam(FROM_TIME_PARAM, fromTime);
        }

        if (toTime != null) {
            uriBuilder.queryParam(TO_TIME_PARAM, toTime);
        }

        return uriBuilder.build().toUri();
    }
}
