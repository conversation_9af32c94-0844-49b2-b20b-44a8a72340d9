// /*
//  * Copyright (C) Siemens.  All Rights Reserved.
//  *
//  * Source      : AgenciesResource.java
//  * Project     : SPM Platform
//  */
// package com.siemens.spm.usermanagementservice.api.resource;

// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.http.ResponseEntity;
// import org.springframework.security.access.prepost.PreAuthorize;
// import org.springframework.web.bind.annotation.RestController;

// import com.siemens.spm.common.util.ResponseUtil;
// import com.siemens.spm.usermanagementservice.api.boundary.AgencyService;
// import com.siemens.spm.usermanagementservice.api.controller.AgencyController;
// import com.siemens.spm.usermanagementservice.api.vo.request.AgencyLicenseUpdateRequestVO;
// import com.siemens.spm.usermanagementservice.api.vo.request.SetAgencySettingsRequestVO;
// import com.siemens.spm.usermanagementservice.api.vo.response.AgencyDetailResultObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.AgencyLicenseResultObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.AgencySettingsResultObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.AgencySettingsResultObject.AgencySettingsStatusCode;
// import lombok.extern.slf4j.Slf4j;

// /**
//  * REST controller for Agency Management.
//  */
// @Slf4j
// @RestController
// public class AgencyResource implements AgencyController {

//     @Autowired
//     private AgencyService agencyService;

//     /**
//      * {@inheritDoc}
//      */
//     @Override
//     // TODO: Authorization
//     public ResponseEntity<AgencyDetailResultObject> getAgencyDetail(final Integer agencyId) {
//         AgencyDetailResultObject resultObject;

//         try {
//             resultObject = agencyService.getAgencyDetail(agencyId);
//         } catch (Exception e) {
//             log.error("Error while getting agency detail", e);

//             resultObject = new AgencyDetailResultObject(null, AgencyDetailResultObject.StatusCode.ERROR);
//         }

//         return ResponseUtil.wrapOrNotFound(resultObject);
//     }

//     /**
//      * {@inheritDoc}
//      */
//     @Override
//     @PreAuthorize("hasPermission('settings', 'READ', #agencyId, null)")
//     public ResponseEntity<AgencySettingsResultObject> getAgencySettings(final Integer agencyId) {
//         AgencySettingsResultObject resultObject;
//         try {
//             resultObject = agencyService.getAgencySettings(agencyId);
//         } catch (Exception e) {
//             log.error("Error while getting agency settings", e);

//             resultObject = new AgencySettingsResultObject(null, AgencySettingsStatusCode.UNKNOWN_ERROR);
//         }

//         return ResponseUtil.wrapOrNotFound(resultObject);
//     }

//     /**
//      * {@inheritDoc}
//      */
//     @Override
//     @PreAuthorize("hasPermission('settings', 'UPDATE', #agencyId, null)")
//     public ResponseEntity<AgencySettingsResultObject> setAgencySettings(Integer agencyId,
//                                                                         SetAgencySettingsRequestVO requestVO) {
//         AgencySettingsResultObject resultObject;
//         try {
//             resultObject = agencyService.setAgencySettings(agencyId, requestVO);
//         } catch (Exception e) {
//             log.error("Error while setting agency settings", e);

//             resultObject = new AgencySettingsResultObject(null, AgencySettingsStatusCode.UNKNOWN_ERROR);
//         }

//         return ResponseUtil.wrapOrNotFound(resultObject);
//     }

//     @Override
//     @PreAuthorize("hasPermission('licenses', 'READ', #agencyId, null)")
//     public ResponseEntity<AgencyLicenseResultObject> getAgencyLicenses(Integer agencyId) {
//         AgencyLicenseResultObject resultObject;

//         try {
//             resultObject = agencyService.getAgencyLicenses(agencyId);
//         } catch (Exception e) {
//             log.error("Error while getting agency licenses", e);

//             resultObject = new AgencyLicenseResultObject(AgencyLicenseResultObject.StatusCode.ERROR);
//         }

//         return ResponseUtil.wrapOrNotFound(resultObject);
//     }

//     @Override
//     @PreAuthorize("hasPermission('licenses', 'UPDATE', #agencyId, null)")
//     public ResponseEntity<AgencyLicenseResultObject> setAgencyLicenses(Integer agencyId,
//                                                                        AgencyLicenseUpdateRequestVO requestVO) {
//         AgencyLicenseResultObject resultObject;

//         try {
//             resultObject = agencyService.setAgencyLicense(agencyId, requestVO);
//         } catch (Exception e) {
//             log.error("Error while setting agency licenses", e);

//             resultObject = new AgencyLicenseResultObject(AgencyLicenseResultObject.StatusCode.ERROR);
//         }

//         return ResponseUtil.wrapOrNotFound(resultObject);
//     }

// }
