/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : EnumConstraintValidator.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.api.vo.constraint.validator;

import com.siemens.spm.common.validation.ValidEnum;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.springframework.util.StringUtils;

public class ValidEnumValidator implements ConstraintValidator<ValidEnum, String> {

    private ValidEnum annotation;

    @Override
    public void initialize(ValidEnum annotation) {
        this.annotation = annotation;
    }

    @Override
    public boolean isValid(String valueForValidation, ConstraintValidatorContext constraintValidatorContext) {
        boolean result = false;

        if (!StringUtils.hasText(valueForValidation)) {
            return false;
        }

        Object[] enumValues = this.annotation.enumClass().getEnumConstants();

        if (enumValues != null) {
            for (Object enumValue : enumValues) {
                if (valueForValidation.equals(enumValue.toString()) || (this.annotation.ignoreCase()
                        && valueForValidation.equalsIgnoreCase(enumValue.toString()))) {
                    result = true;
                    break;
                }
            }
        }

        return result;
    }
}
