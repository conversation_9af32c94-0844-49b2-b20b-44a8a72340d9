// /*
//  * Copyright (C) Siemens.  All Rights Reserved.
//  *
//  * Source      : IntersectionsResource.java
//  * Project     : SPM Platform
//  */

// package com.siemens.spm.usermanagementservice.api.resource;

// import com.siemens.spm.common.shared.domaintype.IntersectionStatus;
// import com.siemens.spm.common.util.ResponseUtil;
// import com.siemens.spm.usermanagementservice.api.boundary.IntersectionService;
// import com.siemens.spm.usermanagementservice.api.controller.IntersectionController;
// import com.siemens.spm.usermanagementservice.api.vo.request.IntersectionSearchRequestVO;
// import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionDetailResultObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionDetailResultObject.StatusCode;
// import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionSearchResultObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionSimpleListResultObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionStatusHistoriesResultObject;
// import com.siemens.spm.usermanagementservice.domain.Intersection;

// import lombok.extern.slf4j.Slf4j;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.http.ResponseEntity;
// import org.springframework.security.access.prepost.PreAuthorize;
// import org.springframework.util.StringUtils;
// import org.springframework.web.bind.annotation.RestController;

// /**
//  * REST controller for Intersection Management.
//  */
// @Slf4j
// @RestController
// public class IntersectionsResource implements IntersectionController {

//     @Autowired
//     private IntersectionService intersectionService;

//     /**
//      * {@inheritDoc}
//      */
//     @Override
//     @PreAuthorize("hasAnyLicenses(#agencyId, 'intersection') && hasPermission('intersections', 'LIST', #agencyId, null)")
//     public ResponseEntity<IntersectionSearchResultObject> searchIntersections(Integer agencyId,
//                                                                               String text,
//                                                                               String status,
//                                                                               Double[] topLeft,
//                                                                               Double[] bottomRight,
//                                                                               String[] orderByColumns,
//                                                                               Integer page,
//                                                                               Integer size) {
//         if (isInvalidStatus(status)) {
//             return ResponseUtil.wrapOrNotFound(
//                     new IntersectionSearchResultObject(
//                             IntersectionSearchResultObject.StatusCode.INVALID_STATUS));
//         } else {
//             //re-assign enum mapped value to status to support case-insensitive filter
//             status = IntersectionStatus.toInsight(status);
//         }

//         IntersectionSearchResultObject resultObject;

//         // Default order is by name
//         String[] orders = orderByColumns;
//         if (orderByColumns == null || orderByColumns.length == 0) {
//             orders = new String[1];
//             orders[0] = Intersection.ColumnName.NAME;
//         }
        
//         var intersectionSearchRequestVO = IntersectionSearchRequestVO.builder()
//                 .agencyId(agencyId)
//                 .bottomRight(bottomRight)
//                 .orderByColumns(orders)
//                 .shouldPaginate(true)
//                 .page(page)
//                 .size(size)
//                 .status(status)
//                 .text(text)
//                 .topLeft(topLeft)
//                 .build();

//         try {
//             resultObject = intersectionService.searchIntersections(intersectionSearchRequestVO);
//         } catch (Exception e) {
//             log.error("Error while searching intersections", e);

//             resultObject = new IntersectionSearchResultObject(null, IntersectionSearchResultObject.StatusCode.ERROR);
//         }

//         return ResponseUtil.wrapOrNotFound(resultObject);
//     }

//     /**
//      * {@inheritDoc}
//      */
//     @Override
//     @PreAuthorize("hasAnyLicenses(#agencyId, 'intersection') && hasPermission('intersections', 'LIST', #agencyId, null)")
//     public ResponseEntity<IntersectionSimpleListResultObject> getAllSimpleIntersections(Integer agencyId,
//                                                                                         String status) {
//         if (isInvalidStatus(status)) {
//             return ResponseUtil.wrapOrNotFound(
//                     new IntersectionSimpleListResultObject(
//                             IntersectionSimpleListResultObject.StatusCode.INVALID_STATUS));
//         } else {
//             //re-assign enum mapped value to status to support case-insensitive filter
//             status = IntersectionStatus.toInsight(status);
//         }

//         IntersectionSimpleListResultObject resultObject;
//         try {
//             resultObject = intersectionService.getAllSimpleIntersections(agencyId, status);
//         } catch (Exception e) {
//             log.error("Error while getting all simple intersections", e);

//             resultObject = new IntersectionSimpleListResultObject(null,
//                     IntersectionSimpleListResultObject.StatusCode.ERROR);
//         }

//         return ResponseUtil.wrapOrNotFound(resultObject);
//     }

//     /**
//      * {@inheritDoc}
//      */
//     @Override
//     @PreAuthorize("hasAnyLicenses(#agencyId, 'intersection') && hasPermission('intersections', 'READ', #agencyId, null)")
//     public ResponseEntity<IntersectionDetailResultObject> getIntersectionDetail(Integer agencyId,
//                                                                                 final String intersectionId) {
//         IntersectionDetailResultObject resultObject;

//         try {
//             resultObject = intersectionService.getIntersectionDetail(agencyId, intersectionId);
//         } catch (Exception e) {
//             log.error("Error while getting intersection detail", e);

//             resultObject = new IntersectionDetailResultObject(null, StatusCode.ERROR);
//         }

//         return ResponseUtil.wrapOrNotFound(resultObject);
//     }

//     @Override
//     @PreAuthorize("hasAnyLicenses(#agencyId, 'intersection') && hasPermission('intersections', 'READ', #agencyId, null)")
//     public ResponseEntity<IntersectionStatusHistoriesResultObject> getIntersectionStatusHistories(Integer agencyId,
//                                                                                                   String intersectionId,
//                                                                                                   Integer page,
//                                                                                                   Integer size) {
//         IntersectionStatusHistoriesResultObject resultObject;

//         try {
//             resultObject = intersectionService.getIntersectionStatusHistories(agencyId, intersectionId, page, size);
//         } catch (Exception e) {
//             log.error("Error while getting intersection status histories", e);
//             resultObject = new IntersectionStatusHistoriesResultObject(null,
//                     IntersectionStatusHistoriesResultObject.StatusCode.ERROR);
//         }
//         return ResponseUtil.wrapOrNotFound(resultObject);
//     }

//     /**
//      * Check if input string is not empty and not matched any {@code IntersectionStatus} value
//      *
//      * @param status Input status for validating (case-insensitive)
//      */
//     private boolean isInvalidStatus(String status) {
//         if (StringUtils.hasText(status) && IntersectionStatus.toInsight(status) == null) {
//             log.error("Invalid STATUS [{}].", status);
//             return true;
//         }
//         return false;
//     }
// }
