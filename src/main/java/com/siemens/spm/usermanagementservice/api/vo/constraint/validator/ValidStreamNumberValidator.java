/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : ValidStreamNumberValidator.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.api.vo.constraint.validator;

import com.siemens.spm.usermanagementservice.api.vo.constraint.ValidStreamNumber;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class ValidStreamNumberValidator implements ConstraintValidator<ValidStreamNumber, Integer> {

  @Override
  public boolean isValid(Integer number, ConstraintValidatorContext constraintValidatorContext) {
    return number == null || (number >= 1 && number <= 16);
  }

}
