/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : ValidDisplaySettingsValidator.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.api.vo.constraint.validator;

import java.util.HashMap;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import com.siemens.spm.usermanagementservice.api.vo.ColorSettingVO;
import com.siemens.spm.usermanagementservice.api.vo.DisplaySettingsVO;
import com.siemens.spm.usermanagementservice.api.vo.MetricDisplaySettingVO;
import com.siemens.spm.usermanagementservice.api.vo.constraint.ValidDisplaySettings;

public class ValidDisplaySettingsValidator implements ConstraintValidator<ValidDisplaySettings, DisplaySettingsVO> {

    private static final Pattern colorCodePattern = Pattern.compile("\\#[0-9A-Fa-f]{6}");

    @Override
    public boolean isValid(DisplaySettingsVO displaySettingsVO, ConstraintValidatorContext context) {
        if (displaySettingsVO == null) {
            return true;
        }

        if (!isValidColorDisplaySetting(displaySettingsVO.getUnreadAlarmNotiNumDisplaySettingVO())) {
            return false;
        }
        if (!isValidColorDisplaySetting(displaySettingsVO.getAlarmRecordNumDisplaySettingVO())) {
            return false;
        }
        if (!isValidColorDisplaySetting(displaySettingsVO.getAorPercentDisplaySettingVO())
                || !isValidPercentValueDisplaySetting(displaySettingsVO.getAorPercentDisplaySettingVO())) {
            return false;
        }
        return isValidColorDisplaySetting(displaySettingsVO.getCoordHealthDisplaySettingVO())
                && isValidPercentValueDisplaySetting(displaySettingsVO.getCoordHealthDisplaySettingVO());
    }

    /**
     * Check color setting of a specific {@link MetricDisplaySettingVO} is valid or not
     *
     * @param numericDisplaySettingVO {@link MetricDisplaySettingVO}
     * @return {@code true} if metric is valid color, other wise return {@code false}
     */
    private boolean isValidColorDisplaySetting(MetricDisplaySettingVO numericDisplaySettingVO) {
        if (numericDisplaySettingVO == null)
            return false;

        List<ColorSettingVO> colorSettingList = numericDisplaySettingVO.getColorSettingVOList();
        if (numericDisplaySettingVO.getLabelKey() == null || colorSettingList == null || colorSettingList.isEmpty()) {
            return false;
        }

        // Check if value ranges overlap and color codes are duplicated
        return isValidColorSettingList(colorSettingList);
    }

    private boolean isValidColorSettingList(List<ColorSettingVO> colorSettingList) {
        if (colorSettingList == null) {
            return false;
        }

        colorSettingList.sort(ColorSettingVO.getFromValueComparator());

        HashMap<String, ColorSettingVO> colorMap = new HashMap<>();
        for (int i = 0; i < colorSettingList.size(); i++) {
            ColorSettingVO iColor = colorSettingList.get(i);
            if (!isValidColorSetting(iColor)) {
                return false;
            }

            // Check color uniqueness
            String key = iColor.getColor().toUpperCase();
            if (colorMap.get(key) != null) {
                return false; // Non-unique color code
            }
            colorMap.put(key, iColor);

            // Check fromValue overlapping
            if (i < colorSettingList.size() - 1) {
                ColorSettingVO jColor = colorSettingList.get(i + 1);
                if (jColor.getFromValue() == null) {
                    return false;
                }
                if (iColor.getFromValue() != null && iColor.getFromValue() >= jColor.getFromValue()) {
                    return false;
                }
            }
        }

        return true;
    }

    private boolean isValidPercentValueDisplaySetting(MetricDisplaySettingVO percentValueDisplaySettingVO) {
        if (percentValueDisplaySettingVO == null)
            return false;

        List<ColorSettingVO> colorSettingList = percentValueDisplaySettingVO.getColorSettingVOList();
        for (ColorSettingVO vo : colorSettingList) {
            Double fromValue = vo.getFromValue();
            if (fromValue != null && (fromValue < 0 || fromValue > 100))
                return false;
        }
        return true;
    }

    private boolean isValidColorSetting(ColorSettingVO colorSettingVO) {
        String color = colorSettingVO.getColor();
        Matcher m = colorCodePattern.matcher(color);
        return m.matches();
    }

}
