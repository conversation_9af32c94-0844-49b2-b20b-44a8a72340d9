/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : ValidCorridorIntersectionsValidator.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.api.vo.constraint.validator;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import com.siemens.spm.usermanagementservice.api.vo.constraint.ValidCorridorIntersections;
import com.siemens.spm.usermanagementservice.api.vo.request.CorridorIntersectionCreateRequestVO;

public class ValidCorridorIntersectionsValidator
        implements ConstraintValidator<ValidCorridorIntersections, List<CorridorIntersectionCreateRequestVO>> {

    @Override
    public boolean isValid(List<CorridorIntersectionCreateRequestVO> corridorIntersectionVOS,
                           ConstraintValidatorContext constraintValidatorContext) {
        // Allow nullable
        if (corridorIntersectionVOS == null || corridorIntersectionVOS.isEmpty()) {
            return true;
        }

        // Check duplicate corridor detail by number or intersection id
        Set<Integer> corridorIntersectionNumberSet = new HashSet<>();
        // Set of intersection id of entity Corridor Intersection
        Set<String> corridorIntersectionIntersectionIdSet = new HashSet<>();

        for (CorridorIntersectionCreateRequestVO corridorIntersectionVO : corridorIntersectionVOS) {
            if (corridorIntersectionNumberSet.contains(corridorIntersectionVO.getNumber())
                    || corridorIntersectionIntersectionIdSet.contains(corridorIntersectionVO.getIntersectionId())) {
                return false;
            }

            corridorIntersectionNumberSet.add(corridorIntersectionVO.getNumber());
            corridorIntersectionIntersectionIdSet.add(corridorIntersectionVO.getIntersectionId());
        }

        return true;
    }

}
