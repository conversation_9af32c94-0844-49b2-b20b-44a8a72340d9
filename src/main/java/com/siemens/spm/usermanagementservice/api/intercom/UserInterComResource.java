/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : UserInterComResource.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.api.intercom;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import com.siemens.spm.common.shared.vo.SimpleResultObject;
import com.siemens.spm.common.util.ResponseUtil;
import com.siemens.spm.usermanagementservice.api.boundary.UserService;
import com.siemens.spm.usermanagementservice.api.vo.Expiration;
import com.siemens.spm.usermanagementservice.api.vo.response.UsersForAlarmSummaryResultObject;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@Hidden
public class UserInterComResource implements UserInterComController {

    @Autowired
    private UserService userService;

    /**
     * {@inheritDoc}
     */
    @Override
    public ResponseEntity<SimpleResultObject> scanExpirationData(List<Expiration> expList) {
        SimpleResultObject resultObject;

        try {
            resultObject = userService.scanExpirationDataInternal(expList);
        } catch (Exception e) {
            log.error("Error while scanning expiration data", e);

            resultObject = new SimpleResultObject();
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    public ResponseEntity<UsersForAlarmSummaryResultObject> getUsersForAlarmSummary(Integer agencyId) {
        UsersForAlarmSummaryResultObject resultObject;
        try {
            resultObject = userService.getUsersForAlarmSummaryInternal(agencyId);
        } catch (Exception e) {
            log.error("Error while getting users for alarm summary", e);

            resultObject = new UsersForAlarmSummaryResultObject(null, SimpleResultObject.SimpleStatusCode.ERROR);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

}
