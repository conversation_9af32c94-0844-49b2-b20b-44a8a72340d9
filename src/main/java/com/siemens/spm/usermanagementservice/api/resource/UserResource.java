// package com.siemens.spm.usermanagementservice.api.resource;

// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.http.ResponseEntity;
// import org.springframework.security.access.prepost.PreAuthorize;
// import org.springframework.web.bind.annotation.RestController;

// import com.siemens.spm.common.util.ResponseUtil;
// import com.siemens.spm.usermanagementservice.api.boundary.UserService;
// import com.siemens.spm.usermanagementservice.api.controller.UserController;
// import com.siemens.spm.usermanagementservice.api.vo.response.UserSimpleListResultObject;
// import lombok.extern.slf4j.Slf4j;

// @Slf4j
// @RestController
// public class UserResource implements UserController {

//     @Autowired
//     private UserService userService;

//     @Override
//     @PreAuthorize("hasPermission('users', 'LIST', #agencyId, null)")
//     public ResponseEntity<UserSimpleListResultObject> getAllSimpleUsers(Integer agencyId) {
//         UserSimpleListResultObject resultObject;
//         try {
//             resultObject = userService.getAllSimpleUsers(agencyId);
//         } catch (Exception e) {
//             resultObject = new UserSimpleListResultObject(UserSimpleListResultObject.UserSimpleListStatusCode.ERROR);
//         }
//         return ResponseUtil.wrapOrNotFound(resultObject);
//     }

// }
