package com.siemens.spm.usermanagementservice.api.intercom;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import com.siemens.spm.common.util.ResponseUtil;
import com.siemens.spm.usermanagementservice.api.boundary.CorridorService;
import com.siemens.spm.usermanagementservice.api.vo.request.CorridorSearchRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.CorridorInternalSearchResultObject;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@Hidden
public class CorridorInterComResource implements CorridorInterComController {

    @Autowired
    private CorridorService corridorService;

    @Override
    public ResponseEntity<CorridorInternalSearchResultObject> getAllCorridorsByFilter(CorridorSearchRequestVO searchRequest) {
        CorridorInternalSearchResultObject resultObject;

        try {
            resultObject = corridorService.getAllCorridorsByFilterInternal(searchRequest);
        } catch (Exception e) {
            log.error("Error while getting all corridors by filter", e);

            resultObject = new CorridorInternalSearchResultObject();
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

}
