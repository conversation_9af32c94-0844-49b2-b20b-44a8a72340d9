// package com.siemens.spm.usermanagementservice.api.resource;

// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.http.ResponseEntity;
// import org.springframework.web.bind.annotation.RestController;

// import com.siemens.spm.common.util.ResponseUtil;
// import com.siemens.spm.usermanagementservice.api.boundary.TspDashboardService;
// import com.siemens.spm.usermanagementservice.api.controller.TspDashboardController;
// import com.siemens.spm.usermanagementservice.api.vo.request.IntersectionForTspDashboardMapSearchRequest;
// import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionListForTspDashboardMapResultObject;
// import lombok.extern.slf4j.Slf4j;

// @Slf4j
// @RestController
// public class TspDashboardResource implements TspDashboardController {

//     private static final String UNEXPECTED_ERROR_MESSAGE = "unexpected error";

//     @Autowired
//     private TspDashboardService tspDashboardService;

//     /**
//      * {@inheritDoc}
//      */
//     @Override
//     public ResponseEntity<IntersectionListForTspDashboardMapResultObject> getIntersectionsForTspMap(Integer agencyId,
//                                                                                                     Double[] topLeft,
//                                                                                                     Double[] bottomRight,
//                                                                                                     String[] orderByColumns,
//                                                                                                     Integer page,
//                                                                                                     Integer size) {
//         IntersectionListForTspDashboardMapResultObject resultObject;
//         try {
//             IntersectionForTspDashboardMapSearchRequest searchRequest = IntersectionForTspDashboardMapSearchRequest
//                     .builder()
//                     .agencyId(agencyId)
//                     .topLeft(topLeft)
//                     .bottomRight(bottomRight)
//                     .orderByColumns(orderByColumns)
//                     .page(page)
//                     .size(size)
//                     .build();

//             resultObject = tspDashboardService.getIntersectionsForTspMap(searchRequest);
//         } catch (Exception e) {
//             log.error(UNEXPECTED_ERROR_MESSAGE, e);

//             resultObject = new IntersectionListForTspDashboardMapResultObject(null,
//                     IntersectionListForTspDashboardMapResultObject.StatusCode.ERROR);
//         }

//         return ResponseUtil.wrapOrNotFound(resultObject);
//     }

// }
