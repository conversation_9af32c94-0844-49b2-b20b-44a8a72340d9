/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : NotificationResource.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.api.resource;

import java.sql.Timestamp;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import com.siemens.spm.common.util.ResponseUtil;
import com.siemens.spm.usermanagementservice.api.boundary.NotificationService;
import com.siemens.spm.usermanagementservice.api.controller.NotificationControllerV2;
import com.siemens.spm.usermanagementservice.api.vo.request.NotificationSearchRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.NotificationUpdatedRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.NotificationDetailsResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.NotificationLatestUnreadResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.NotificationManipulateResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.NotificationSearchResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.NotificationTypeListResultObject;

import lombok.extern.slf4j.Slf4j;

/**
 * REST controller for managing notifications of users
 */
@Slf4j
@RestController
public class NotificationResourceV2 implements NotificationControllerV2 {

    private static final String ERROR_OCCURS_MESSAGE = "Error occurs!";

    @Autowired
    private NotificationService notificationService;

    /**
     * {@inheritDoc}
     */
    @Override
    //    @PreAuthorize("hasPermission('notifications', 'LIST', null, @notificationAuthorizationHelper.getOwnerId(#notificationId))")
    public ResponseEntity<NotificationDetailsResultObject> getNotificationDetails(Long notificationId) {
        NotificationDetailsResultObject resultObject;

        try {
            resultObject = notificationService.getNotificationDetails(notificationId);
        } catch (Exception ex) {
            log.error(ERROR_OCCURS_MESSAGE, ex);

            resultObject = new NotificationDetailsResultObject(null, NotificationDetailsResultObject.StatusCode.ERROR);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResponseEntity<NotificationLatestUnreadResultObject> getLatestUnreadNotifications(Integer agencyId,
                                                                                             Integer page,
                                                                                             Integer size) {
        NotificationLatestUnreadResultObject resultObject;

        try {
            resultObject = notificationService.getLatestUnreadNotifications(agencyId, page, size);
        } catch (Exception e) {
            log.error(ERROR_OCCURS_MESSAGE, e);

            resultObject = new NotificationLatestUnreadResultObject(null,
                    NotificationLatestUnreadResultObject.StatusCode.ERROR);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResponseEntity<NotificationSearchResultObject> searchNotifications(Integer agencyId,
                                                                              String intersection,
                                                                              String text,
                                                                              Long fromDate,
                                                                              Long toDate,
                                                                              Long typeId,
                                                                              String readStatus,
                                                                              String flagStatus,
                                                                              String[] orderByColumns,
                                                                              Integer page,
                                                                              Integer size) {
        NotificationSearchResultObject resultObject;

        try {
            Timestamp createdAtFrom = (fromDate != null) ? new Timestamp(fromDate) : null;
            Timestamp createdAtTo = (toDate != null) ? new Timestamp(toDate) : null;

            NotificationSearchRequestVO searchRequest = NotificationSearchRequestVO.builder()
                    .createdAtFrom(createdAtFrom)
                    .createdAtTo(createdAtTo)
                    .flagStatus(flagStatus)
                    .agencyId(agencyId)
                    .intersection(intersection)
                    .orderByColumns(orderByColumns)
                    .page(page)
                    .readStatus(readStatus)
                    .size(size)
                    .text(text)
                    .typeId(typeId)
                    .build();

            resultObject = notificationService.searchNotifications(searchRequest);
        } catch (Exception ex) {
            log.error(ERROR_OCCURS_MESSAGE, ex);

            resultObject = new NotificationSearchResultObject(null, NotificationSearchResultObject.StatusCode.ERROR);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    //    @PreAuthorize("hasPermission('notifications/{notificationId}', 'UPDATE', null, @notificationAuthorizationHelper.getOwnerId(#updateRequest.notiIds))")
    public ResponseEntity<NotificationManipulateResultObject> updateNotifications(
            NotificationUpdatedRequestVO updateRequest) {
        NotificationManipulateResultObject resultObject;

        try {
            resultObject = notificationService.updateNotifications(updateRequest);
        } catch (Exception ex) {
            log.error(ERROR_OCCURS_MESSAGE, ex);

            resultObject = new NotificationManipulateResultObject(NotificationManipulateResultObject.StatusCode.ERROR);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResponseEntity<NotificationManipulateResultObject> markAllNotificationsAsRead(Integer agencyId) {
        NotificationManipulateResultObject resultObject;

        try {
            resultObject = notificationService.markAllNotificationsAsRead(agencyId);
        } catch (Exception ex) {
            log.error(ERROR_OCCURS_MESSAGE, ex);

            resultObject = new NotificationManipulateResultObject(NotificationManipulateResultObject.StatusCode.ERROR);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    //    @PreAuthorize("hasPermission('notifications/{notificationId}', 'DELETE', null, @notificationAuthorizationHelper.getOwnerId(#notiIds))")
    public ResponseEntity<NotificationManipulateResultObject> deleteNotifications(List<Long> notiIds) {
        NotificationManipulateResultObject resultObject;

        try {
            resultObject = notificationService.deleteNotifications(notiIds);
        } catch (Exception ex) {
            log.error(ERROR_OCCURS_MESSAGE, ex);

            resultObject = new NotificationManipulateResultObject(NotificationManipulateResultObject.StatusCode.ERROR);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResponseEntity<NotificationTypeListResultObject> getAllNotificationTypes() {
        NotificationTypeListResultObject resultObject;

        try {
            resultObject = notificationService.getAllNotificationTypes();
        } catch (Exception ex) {
            log.error(ERROR_OCCURS_MESSAGE, ex);

            resultObject = new NotificationTypeListResultObject(null,
                    NotificationTypeListResultObject.StatusCode.ERROR);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

}
