/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionInterComResource.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.api.intercom;

import java.time.LocalDateTime;

import jakarta.validation.constraints.NotNull;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import com.siemens.spm.common.util.ResponseUtil;
import com.siemens.spm.usermanagementservice.api.boundary.DataHubSynchronizerService;
import com.siemens.spm.usermanagementservice.api.boundary.IntersectionService;
import com.siemens.spm.usermanagementservice.api.vo.request.IntersectionSearchRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionInternalSearchResultObject;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@Hidden
public class IntersectionInterComResource implements IntersectionInterComController {

    @Autowired
    private IntersectionService intersectionService;

    @Autowired
    private DataHubSynchronizerService dataHubSynchronizerService;

    /**
     * {@inheritDoc}
     */
    @Override
    public ResponseEntity<IntersectionInternalSearchResultObject> searchIntersections(@NotNull IntersectionSearchRequestVO searchRequest) {
        IntersectionInternalSearchResultObject resultObject;

        try {
            resultObject = intersectionService.searchIntersectionsInternal(searchRequest);
        } catch (Exception e) {
            log.error("Error occurs while search intersections!", e);

            resultObject = new IntersectionInternalSearchResultObject();
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void syncIntersections() {
        try {
            dataHubSynchronizerService.syncIntersections();
        } catch (Exception e) {
            log.error("Error occurs while sync intersections!", e);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void initCache(Integer agencyId, String intUUID, LocalDateTime fromTime, LocalDateTime toTime) {
        try {
            dataHubSynchronizerService.initCache(agencyId, intUUID, fromTime, toTime);
        } catch (Exception e) {
            log.error("Error occurs while init cache!", e);
        }
    }
}
