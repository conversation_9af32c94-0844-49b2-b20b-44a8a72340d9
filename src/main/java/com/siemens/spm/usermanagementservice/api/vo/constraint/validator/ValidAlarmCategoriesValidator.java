package com.siemens.spm.usermanagementservice.api.vo.constraint.validator;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import com.siemens.spm.common.shared.vo.AlarmCategoryVO;
import com.siemens.spm.usermanagementservice.api.vo.constraint.ValidAlarmCategories;

public class ValidAlarmCategoriesValidator implements ConstraintValidator<ValidAlarmCategories, List<AlarmCategoryVO>> {

    @Override
    public boolean isValid(List<AlarmCategoryVO> value, ConstraintValidatorContext context) {
        if (value == null || value.isEmpty()) {
            return false;
        }

        // Check duplicate alarm category name
        Set<String> alarmCategoryNameSet = new HashSet<>();
        for (AlarmCategoryVO alarmCategoryVO : value) {
            if (alarmCategoryNameSet.contains(alarmCategoryVO.getName())) {
                return false;
            }

            alarmCategoryNameSet.add(alarmCategoryVO.getName());
        }

        return true;
    }

}
