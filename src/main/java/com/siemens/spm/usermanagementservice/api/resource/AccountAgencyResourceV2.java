package com.siemens.spm.usermanagementservice.api.resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import com.siemens.spm.common.util.ResponseUtil;
import com.siemens.spm.usermanagementservice.api.boundary.AccountAgencyService;
import com.siemens.spm.usermanagementservice.api.controller.AccountAgencyControllerV2;
import com.siemens.spm.usermanagementservice.api.vo.request.UpdateNotiSettingRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.UpdateWidgetsSettingRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.AgencyUserSettingMetadataResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.AgencyUserSettingResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.NotiSettingResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.NotiSettingUpdatedResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.WidgetsSettingUpdatedResultObject;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
public class AccountAgencyResourceV2 implements AccountAgencyControllerV2 {

    @Autowired
    private AccountAgencyService accountAgencyService;

    @Override
    //    @PreAuthorize("hasPermission('account', 'READ', #agencyId, null)")
    public ResponseEntity<AgencyUserSettingResultObject> getUserSetting(Integer agencyId) {
        AgencyUserSettingResultObject resultObject;
        try {
            resultObject = accountAgencyService.getUserSetting(agencyId);
        } catch (Exception e) {
            log.error("Error when getting user setting", e);

            resultObject = new AgencyUserSettingResultObject(
                    AgencyUserSettingResultObject.AgencyUserSettingStatusCode.UNKNOWN_ERROR);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    //    @PreAuthorize("hasPermission('account', 'READ', #agencyId, null)")
    public ResponseEntity<NotiSettingResultObject> getNotiSetting(Integer agencyId) {
        NotiSettingResultObject resultObject;
        try {
            resultObject = accountAgencyService.getNotiSetting(agencyId);
        } catch (Exception e) {
            log.error("Error when getting notification setting", e);

            resultObject = new NotiSettingResultObject(
                    AgencyUserSettingResultObject.AgencyUserSettingStatusCode.UNKNOWN_ERROR);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    //    @PreAuthorize("hasPermission('account', 'READ', #agencyId, null)")
    public ResponseEntity<AgencyUserSettingMetadataResultObject> getUserSettingMetadata(Integer agencyId) {
        AgencyUserSettingMetadataResultObject resultObject;
        try {
            resultObject = accountAgencyService.getUserSettingMetadata(agencyId);
        } catch (Exception e) {
            log.error("Error when getting user setting metadata", e);

            resultObject = new AgencyUserSettingMetadataResultObject(
                    AgencyUserSettingResultObject.AgencyUserSettingStatusCode.UNKNOWN_ERROR);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    //    @PreAuthorize("hasPermission('account', 'UPDATE', #agencyId, null)")
    public ResponseEntity<WidgetsSettingUpdatedResultObject> updateAllWidgetsSetting(Integer agencyId,
                                                                                     UpdateWidgetsSettingRequestVO requestVO) {
        WidgetsSettingUpdatedResultObject resultObject;
        try {
            requestVO.setAgencyId(agencyId);
            resultObject = accountAgencyService.updateAllWidgetsSetting(requestVO);
        } catch (Exception e) {
            log.error("Error when updating all widgets setting", e);

            resultObject = new WidgetsSettingUpdatedResultObject(
                    WidgetsSettingUpdatedResultObject.WidgetSettingStatusCode.UNKNOWN_ERROR);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    //    @PreAuthorize("hasPermission('account', 'UPDATE', #agencyId, null)")
    public ResponseEntity<WidgetsSettingUpdatedResultObject> updateSpecificWidgetSetting(Integer agencyId,
                                                                                         UpdateWidgetsSettingRequestVO requestVO) {
        WidgetsSettingUpdatedResultObject resultObject;
        try {
            requestVO.setAgencyId(agencyId);
            resultObject = accountAgencyService.updateSpecificWidgetSetting(requestVO);
        } catch (Exception e) {
            log.error("Error when updating specific widget setting", e);

            resultObject = new WidgetsSettingUpdatedResultObject(
                    WidgetsSettingUpdatedResultObject.WidgetSettingStatusCode.UNKNOWN_ERROR);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    //        @PreAuthorize("hasPermission('account', 'UPDATE', #agencyId, null)")
    public ResponseEntity<NotiSettingUpdatedResultObject> updateNotiSetting(Integer agencyId,
                                                                            UpdateNotiSettingRequestVO requestVO) {
        NotiSettingUpdatedResultObject resultObject;
        try {
            requestVO.setAgencyId(agencyId);
            resultObject = accountAgencyService.updateNotiSetting(requestVO);
        } catch (Exception e) {
            log.error("Error when updating notification setting", e);

            resultObject = new NotiSettingUpdatedResultObject(null, NotiSettingUpdatedResultObject.StatusCode.ERROR);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

}
