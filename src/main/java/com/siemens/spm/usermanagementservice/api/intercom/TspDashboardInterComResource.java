package com.siemens.spm.usermanagementservice.api.intercom;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import com.siemens.spm.common.util.ResponseUtil;
import com.siemens.spm.datahub.api.vo.DataHubTspEvent;
import com.siemens.spm.usermanagementservice.api.boundary.TspDashboardService;
import com.siemens.spm.usermanagementservice.api.vo.response.TspEventInternalResultObject;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@Hidden
public class TspDashboardInterComResource implements TspDashboardInterComController {

    @Autowired
    private TspDashboardService tspDashboardService;

    @Override
    public ResponseEntity<TspEventInternalResultObject> sendTspEvent(DataHubTspEvent dataHubTspEvent) {
        TspEventInternalResultObject resultObject;
        try {
            resultObject = tspDashboardService.sendTspEvent(dataHubTspEvent);
        } catch (Exception e) {
            log.error("Error while sending TSP event", e);

            resultObject = new TspEventInternalResultObject(TspEventInternalResultObject.StatusCode.ERROR);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

}
