/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : NotificationInterComResource.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.api.intercom;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import com.siemens.spm.common.util.ResponseUtil;
import com.siemens.spm.usermanagementservice.api.boundary.NotificationService;
import com.siemens.spm.usermanagementservice.api.vo.ReleaseNoteVO;
import com.siemens.spm.usermanagementservice.api.vo.request.NotificationsCreateRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.NotificationsCreateResultObject;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@Hidden
public class NotificationInterComResource implements NotificationInterComController {

    @Autowired
    private NotificationService notificationService;

    /**
     * {@inheritDoc}
     */
    @Override
    public ResponseEntity<NotificationsCreateResultObject> createNotificationsForUsers(
            NotificationsCreateRequestVO requestVO) {
        NotificationsCreateResultObject resultObject;

        try {
            resultObject = notificationService.createNotificationsForUsersInternal(requestVO);
        } catch (Exception e) {
            log.error("Error occurs!", e);

            resultObject = new NotificationsCreateResultObject(NotificationsCreateResultObject.StatusCode.ERROR);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResponseEntity<NotificationsCreateResultObject> createReleaseNoteNotifications(ReleaseNoteVO releaseNoteVO) {
        NotificationsCreateResultObject resultObject;

        try {
            resultObject = notificationService.createReleaseNoteNotifications(releaseNoteVO);
        } catch (Exception e) {
            log.error("Error occurs!", e);

            resultObject = new NotificationsCreateResultObject(NotificationsCreateResultObject.StatusCode.ERROR);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

}
