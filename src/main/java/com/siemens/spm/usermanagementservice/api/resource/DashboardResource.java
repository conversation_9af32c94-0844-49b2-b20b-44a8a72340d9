// /*
//  * Copyright (C) Siemens.  All Rights Reserved.
//  *
//  * Source      : DashboardResource.java
//  * Project     : SPM Platform
//  */

// package com.siemens.spm.usermanagementservice.api.resource;

// import java.sql.Timestamp;
// import java.time.LocalDateTime;
// import java.util.List;

// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.http.ResponseEntity;
// import org.springframework.security.access.prepost.PreAuthorize;
// import org.springframework.web.bind.annotation.RestController;

// import com.siemens.spm.common.constant.IntersectionOption;
// import com.siemens.spm.common.util.ResponseUtil;
// import com.siemens.spm.usermanagementservice.api.boundary.AgencyService;
// import com.siemens.spm.usermanagementservice.api.boundary.DashboardService;
// import com.siemens.spm.usermanagementservice.api.controller.DashboardController;
// import com.siemens.spm.usermanagementservice.api.vo.request.IntersectionForDashboardMapSearchRequestVO;
// import com.siemens.spm.usermanagementservice.api.vo.response.AgencySettingsResultObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.AgencySettingsResultObject.AgencySettingsStatusCode;
// import com.siemens.spm.usermanagementservice.api.vo.response.ElementListForDashboardChartResultObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionListForDashboardMapResultObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionListForDashboardTopRankResultObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionListVolumeCountResultObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.NotificationListForUserNotificationObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.SummaryStatisticForDashboardResultObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.SummaryStatisticForDashboardResultObject.SummaryStatisticForDashboardStatusCode;
// import lombok.extern.slf4j.Slf4j;

// /**
//  * REST controller for dashboard
//  */
// @Slf4j
// @RestController
// public class DashboardResource implements DashboardController {

//     private static final String UNEXPECTED_ERROR = "Unexpected error";

//     @Autowired
//     private AgencyService agencyService;

//     @Autowired
//     private DashboardService dashboardService;

//     /**
//      * {@inheritDoc}
//      */
//     @Override
//     @PreAuthorize("hasPermission('dashboard', 'READ', #agencyId, null)")
//     public ResponseEntity<AgencySettingsResultObject> getMapSettings(final Integer agencyId) {
//         AgencySettingsResultObject resultObject;
//         try {
//             resultObject = agencyService.getMapSettings(agencyId);
//         } catch (Exception e) {
//             log.error(UNEXPECTED_ERROR, e);

//             resultObject = new AgencySettingsResultObject(null, AgencySettingsStatusCode.UNKNOWN_ERROR);
//         }

//         return ResponseUtil.wrapOrNotFound(resultObject);
//     }

//     /**
//      * {@inheritDoc}
//      */
//     @Override
//     @PreAuthorize("hasPermission('dashboard', 'READ', #agencyId, null)")
//     public ResponseEntity<IntersectionListForDashboardMapResultObject> getIntersectionsForMap(Integer agencyId,
//                                                                                               Double[] topLeft,
//                                                                                               Double[] bottomRight,
//                                                                                               LocalDateTime fromTime,
//                                                                                               LocalDateTime toTime,
//                                                                                               String[] orderByColumns,
//                                                                                               Integer page,
//                                                                                               Integer size) {
//         IntersectionListForDashboardMapResultObject resultObject;

//         try {
//             IntersectionForDashboardMapSearchRequestVO searchRequestVO = IntersectionForDashboardMapSearchRequestVO.builder()
//                     .agencyId(agencyId)
//                     .bottomRight(bottomRight)
//                     .topLeft(topLeft)
//                     .fromTime(fromTime != null ? Timestamp.valueOf(fromTime) : null)
//                     .toTime(toTime != null ? Timestamp.valueOf(toTime) : null)
//                     .build();

//             resultObject = dashboardService.getIntersectionsForMap(searchRequestVO, orderByColumns, page, size);
//         } catch (Exception e) {
//             log.error(UNEXPECTED_ERROR, e);

//             resultObject = new IntersectionListForDashboardMapResultObject(null,
//                     IntersectionListForDashboardMapResultObject.StatusCode.ERROR);
//         }

//         return ResponseUtil.wrapOrNotFound(resultObject);
//     }

//     /**
//      * {@inheritDoc}
//      */
//     @Override
//     @PreAuthorize("hasPermission('dashboard', 'READ', #agencyId, null)")
//     public ResponseEntity<IntersectionListForDashboardTopRankResultObject> getIntersectionsForTopRank(Integer agencyId,
//                                                                                                       List<String> intIds,
//                                                                                                       List<String> excludeIntIds,
//                                                                                                       Integer size) {
//         IntersectionListForDashboardTopRankResultObject resultObject;
//         try {
//             resultObject = dashboardService.getIntersectionsForTopRank(agencyId, intIds, excludeIntIds, size);
//         } catch (Exception e) {
//             log.error(UNEXPECTED_ERROR, e);

//             resultObject = new IntersectionListForDashboardTopRankResultObject(null,
//                     IntersectionListForDashboardTopRankResultObject.StatusCode.ERROR);
//         }

//         return ResponseUtil.wrapOrNotFound(resultObject);
//     }

//     /**
//      * {@inheritDoc}
//      */
//     @Override
//     @PreAuthorize("hasPermission('dashboard', 'READ', #agencyId, null)")
//     public ResponseEntity<ElementListForDashboardChartResultObject> getAllElementsForChart(Integer agencyId,
//                                                                                            List<String> intIds,
//                                                                                            List<String> excludeIntIds) {
//         ElementListForDashboardChartResultObject resultObject;
//         try {
//             resultObject = dashboardService.getAllElementsForChart(agencyId, intIds, excludeIntIds);
//         } catch (Exception e) {
//             log.error(UNEXPECTED_ERROR, e);

//             resultObject = new ElementListForDashboardChartResultObject(null,
//                     ElementListForDashboardChartResultObject.StatusCode.ERROR);
//         }

//         return ResponseUtil.wrapOrNotFound(resultObject);
//     }

//     @Override
//     @PreAuthorize("hasPermission('dashboard', 'READ', #agencyId, null)")
//     public ResponseEntity<NotificationListForUserNotificationObject> getNotificationForUserNotification(Integer agencyId,
//                                                                                                         List<Long> typeIds,
//                                                                                                         LocalDateTime fromTime,
//                                                                                                         LocalDateTime toTime) {
//         NotificationListForUserNotificationObject resultObject;
//         try {
//             resultObject = dashboardService.getElementForUserNotification(agencyId, typeIds,
//                     Timestamp.valueOf(fromTime), Timestamp.valueOf(toTime));
//         } catch (Exception e) {
//             log.error(UNEXPECTED_ERROR, e);

//             resultObject = new NotificationListForUserNotificationObject(null,
//                     NotificationListForUserNotificationObject.StatusCode.ERROR);
//         }

//         return ResponseUtil.wrapOrNotFound(resultObject);
//     }

//     @Override
//     @PreAuthorize("hasPermission('dashboard', 'READ', #agencyId, null)")
//     public ResponseEntity<SummaryStatisticForDashboardResultObject> getSummaryStatistics(Integer agencyId,
//                                                                                          String[] filter,
//                                                                                          String[] sort,
//                                                                                          Integer page,
//                                                                                          Integer size,
//                                                                                          String[] metrics,
//                                                                                          LocalDateTime fromTime,
//                                                                                          LocalDateTime toTime) {
//         SummaryStatisticForDashboardResultObject resultObject;
//         try {
//             resultObject = dashboardService.getSummaryStatistics(agencyId, filter, sort, page, size, metrics,
//                     Timestamp.valueOf(fromTime), Timestamp.valueOf(toTime));
//         } catch (Exception e) {
//             log.error(UNEXPECTED_ERROR, e);

//             resultObject = new SummaryStatisticForDashboardResultObject(SummaryStatisticForDashboardStatusCode.ERROR);
//         }

//         return ResponseUtil.wrapOrNotFound(resultObject);
//     }

//     @Override
//     @PreAuthorize("hasPermission('dashboard', 'READ', #agencyId, null)")
//     public ResponseEntity<IntersectionListVolumeCountResultObject> getIntersectionVolumeCount(LocalDateTime fromTime,
//                                                                                               LocalDateTime toTime,
//                                                                                               Integer agencyId,
//                                                                                               IntersectionOption intersectionOption,
//                                                                                               List<String> intersectionIds) {
//         IntersectionListVolumeCountResultObject resultObject;
//         try {
//             resultObject = dashboardService.getIntersectionVolumeCount(fromTime, toTime, agencyId, intersectionOption,
//                     intersectionIds);
//         } catch (Exception e) {
//             log.error(UNEXPECTED_ERROR, e);

//             resultObject = new IntersectionListVolumeCountResultObject(null,
//                     IntersectionListVolumeCountResultObject.StatusCode.ERROR);
//         }

//         return ResponseUtil.wrapOrNotFound(resultObject);
//     }

// }
