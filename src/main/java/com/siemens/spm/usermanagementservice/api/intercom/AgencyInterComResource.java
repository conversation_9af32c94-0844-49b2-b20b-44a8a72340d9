/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AgencyInterComResource.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.api.intercom;

import java.util.List;

import jakarta.validation.constraints.NotNull;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import com.siemens.spm.common.util.ResponseUtil;
import com.siemens.spm.usermanagementservice.agencydata.strategy.AgencyLicenseStrategy;
import com.siemens.spm.usermanagementservice.api.boundary.AgencyService;
import com.siemens.spm.usermanagementservice.api.vo.request.IntersectionsInAgencyVerifyRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.AgencySettingsResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionsInAgencyVerifyResultObject;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@Hidden
public class AgencyInterComResource implements AgencyInterComController {

    @Autowired
    private AgencyService agencyService;

    @Autowired
    private AgencyLicenseStrategy agencyLicenseStrategy;

    /**
     * {@inheritDoc}
     */
    @Override
    public ResponseEntity<IntersectionsInAgencyVerifyResultObject> verifyIntersectionsInAgency(
            @NotNull IntersectionsInAgencyVerifyRequestVO verifyRequest) {
        IntersectionsInAgencyVerifyResultObject resultObject;

        try {
            resultObject = agencyService.verifyIntersectionsInAgencyInternal(verifyRequest);
        } catch (Exception e) {
            log.error("Error while verifying intersections in agency", e);

            resultObject = new IntersectionsInAgencyVerifyResultObject(
                    IntersectionsInAgencyVerifyResultObject.StatusCode.ERROR);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    public ResponseEntity<AgencySettingsResultObject> getAgencySettings(Integer agencyId) {
        AgencySettingsResultObject resultObject;
        try {
            resultObject = agencyService.getAgencySettings(agencyId);
        } catch (Exception e) {
            log.error("Error while getting agency settings", e);

            resultObject = new AgencySettingsResultObject(null,
                    AgencySettingsResultObject.AgencySettingsStatusCode.UNKNOWN_ERROR);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    public ResponseEntity<List<String>> getLicenseIds(@PathVariable Integer agencyId) {
        return ResponseEntity.ok(agencyLicenseStrategy.getLicenseIds(agencyId));
    }

}
