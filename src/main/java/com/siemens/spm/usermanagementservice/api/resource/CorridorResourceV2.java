/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : CorridorResource.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.api.resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RestController;

import com.siemens.spm.common.util.ResponseUtil;
import com.siemens.spm.usermanagementservice.api.boundary.CorridorService;
import com.siemens.spm.usermanagementservice.api.controller.CorridorControllerV2;
import com.siemens.spm.usermanagementservice.api.vo.request.CorridorCreateRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.CorridorListRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.CorridorStatusUpdateRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.CorridorUpdateRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.IntersectionAvailableSearchRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.CorridorListResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.CorridorListResultObject.CorridorsStatusCode;
import com.siemens.spm.usermanagementservice.api.vo.response.CorridorResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.CorridorResultObject.CorridorStatusCode;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionInternalSearchResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionInternalSearchResultObject.StatusCode;

import lombok.extern.slf4j.Slf4j;

/**
 * REST controller for corridor management.
 */
@Slf4j
@RestController
public class CorridorResourceV2 implements CorridorControllerV2 {

    @Autowired
    private CorridorService corridorService;

    /**
     * {@inheritDoc}
     */
    @Override
    @PreAuthorize("hasAnyLicenses(#agencyId, 'corridor') && hasPermission('corridors', 'CREATE', #agencyId, null)")
    public ResponseEntity<CorridorResultObject> createCorridor(Integer agencyId,
                                                               CorridorCreateRequestVO createRequestVO) {
        CorridorResultObject resultObject;
        try {
            createRequestVO.setAgencyId(agencyId);
            resultObject = corridorService.createCorridor(createRequestVO);
        } catch (Exception e) {
            log.error("Error while creating corridor", e);

            resultObject = new CorridorResultObject(null, CorridorStatusCode.ERROR);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    @PreAuthorize("hasAnyLicenses(#agencyId, 'corridor') " +
            "&& hasPermission('corridors', 'UPDATE', @corridorAuthorizationHelper.verifyAndGetAgencyId(#agencyId, #corridorId), null)")
    public ResponseEntity<CorridorResultObject> updateCorridor(Integer agencyId,
                                                               String corridorId,
                                                               CorridorUpdateRequestVO corridorVO) {
        CorridorResultObject resultObject;
        try {
            resultObject = corridorService.updateCorridor(corridorId, corridorVO);
        } catch (Exception e) {
            log.error("Error while updating corridor", e);

            resultObject = new CorridorResultObject(null, CorridorStatusCode.ERROR);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    @PreAuthorize("hasAnyLicenses(#agencyId, 'corridor') " +
            "&& hasPermission('corridors', 'READ', @corridorAuthorizationHelper.verifyAndGetAgencyId(#agencyId, #corridorId), null)")
    public ResponseEntity<CorridorResultObject> getCorridorDetail(Integer agencyId, String corridorId) {
        CorridorResultObject resultObject;
        try {
            resultObject = corridorService.getCorridorDetail(corridorId);
        } catch (Exception e) {
            log.error("Error while getting corridor detail", e);

            resultObject = new CorridorResultObject(null, CorridorStatusCode.ERROR);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    @PreAuthorize("hasAnyLicenses(#agencyId, 'corridor') && hasPermission('corridors', 'LIST', #agencyId, null)")
    public ResponseEntity<CorridorListResultObject> getCorridors(Integer agencyId,
                                                                 String text,
                                                                 String status,
                                                                 String[] orderByColumns,
                                                                 Integer page,
                                                                 Integer size,
                                                                 boolean shouldPaginate) {
        CorridorListRequestVO corridorListRequestVO = CorridorListRequestVO.builder()
                .text(text)
                .status(status)
                .agencyId(agencyId)
                .orderByColumns(orderByColumns)
                .page(page)
                .shouldPaginate(shouldPaginate)
                .size(size)
                .build();

        CorridorListResultObject resultObject;
        try {
            resultObject = corridorService.getCorridors(corridorListRequestVO);
        } catch (Exception e) {
            log.error("Error while getting corridors", e);

            resultObject = new CorridorListResultObject(null, CorridorsStatusCode.ERROR);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    @PreAuthorize("hasAnyLicenses(#agencyId, 'corridor') " +
            "&& hasPermission('corridors', 'UPDATE', @corridorAuthorizationHelper.verifyAndGetAgencyId(#agencyId, #requestVO.ids), null)")
    public ResponseEntity<CorridorResultObject> updateCorridorStatus(Integer agencyId,
                                                                     CorridorStatusUpdateRequestVO requestVO) {
        CorridorResultObject resultObject;
        try {
            resultObject = corridorService.updateCorridorStatus(requestVO);
        } catch (Exception e) {
            log.error("Error while updating corridor status", e);

            resultObject = new CorridorResultObject(null, CorridorStatusCode.ERROR);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    public ResponseEntity<IntersectionInternalSearchResultObject> searchAvailableIntersections(Integer agencyId,
                                                                                               String corridorId,
                                                                                               String text,
                                                                                               String[] orderByColumns,
                                                                                               Integer page,
                                                                                               Integer size) {
        IntersectionInternalSearchResultObject resultObject;
        try {
            IntersectionAvailableSearchRequestVO requestVO = IntersectionAvailableSearchRequestVO.builder()
                    .agencyId(agencyId)
                    .corridorId(corridorId)
                    .text(text)
                    .orderByColumns(orderByColumns)
                    .page(page)
                    .size(size)
                    .build();

            resultObject = corridorService.searchAvailableIntersections(requestVO);
        } catch (Exception e) {
            log.error("Error while searching available intersections", e);

            resultObject = new IntersectionInternalSearchResultObject(null, StatusCode.ERROR);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

}
