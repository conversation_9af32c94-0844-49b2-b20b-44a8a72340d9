// /*
//  * Copyright (C) Siemens.  All Rights Reserved.
//  *
//  * Source      : AccountResource.java
//  * Project     : SPM Platform
//  */
// package com.siemens.spm.usermanagementservice.api.resource;

// import jakarta.validation.Valid;

// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.http.HttpHeaders;
// import org.springframework.http.ResponseEntity;
// import org.springframework.web.bind.annotation.RequestBody;
// import org.springframework.web.bind.annotation.RestController;

// import com.siemens.spm.common.util.ResponseUtil;
// import com.siemens.spm.usermanagementservice.api.boundary.AccountService;
// import com.siemens.spm.usermanagementservice.api.controller.AccountController;
// import com.siemens.spm.usermanagementservice.api.vo.request.LanguageRequestVO;
// import com.siemens.spm.usermanagementservice.api.vo.request.UserKeyRequestVO;
// import com.siemens.spm.usermanagementservice.api.vo.response.AgencyUserAccessResultObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.ChangeLanguageResultObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.ChangeLanguageResultObject.ChangeLanguageResultStatusCode;
// import com.siemens.spm.usermanagementservice.api.vo.response.UserKeyResultObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.UserKeyResultObject.UserKeyStatusCode;
// import com.siemens.spm.usermanagementservice.api.vo.response.UserResultObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.UserResultObject.UserStatusCode;
// import io.swagger.v3.oas.annotations.Parameter;
// import lombok.extern.slf4j.Slf4j;

// /**
//  * REST controller for managing the current user's account.
//  */
// @Slf4j
// @RestController
// public class AccountResource implements AccountController {

//     @Autowired
//     private AccountService accountService;

//     @Override
//     public ResponseEntity<AgencyUserAccessResultObject> accessAgency(Integer agencyId) {
//         AgencyUserAccessResultObject resultObject;
//         HttpHeaders headers = new HttpHeaders();
//         try {
//             resultObject = accountService.accessAgency(agencyId);
//         } catch (Exception e) {
//             log.error("Error while authenticating user", e);
//             resultObject = new AgencyUserAccessResultObject(null, AgencyUserAccessResultObject.StatusCode.ERROR);
//         }

//         return ResponseUtil.wrapOrNotFound(resultObject, headers);
//     }

//     @Override
//     public ResponseEntity<UserResultObject> getAccount() {
//         UserResultObject resultObject;

//         try {
//             resultObject = accountService.getCurrentUser();
//         } catch (Exception e) {
//             log.error("Error when getting current user", e);

//             resultObject = new UserResultObject(null, UserStatusCode.ERROR);
//         }

//         return ResponseUtil.wrapOrNotFound(resultObject);
//     }

//     @Override
//     public ResponseEntity<UserKeyResultObject> getUserKey(UserKeyRequestVO currentPasswordVO) {
//         UserKeyResultObject resultObject;

//         try {
//             resultObject = accountService.getUserKey(currentPasswordVO);
//         } catch (Exception e) {
//             log.error("Error when getting user key", e);

//             resultObject = new UserKeyResultObject(null, UserKeyStatusCode.ERROR);
//         }

//         return ResponseUtil.wrapOrNotFound(resultObject);
//     }

//     @Override
//     public ResponseEntity<UserKeyResultObject> createUserKey(
//             @Parameter(name = "request_body", description = "Current password") @Valid @RequestBody
//             UserKeyRequestVO currentPasswordVO) {
//         UserKeyResultObject resultObject;

//         try {
//             resultObject = accountService.createUserKey(currentPasswordVO);
//         } catch (Exception e) {
//             log.error("Error when creating user key", e);

//             resultObject = new UserKeyResultObject(null, UserKeyStatusCode.ERROR);
//         }

//         return ResponseUtil.wrapOrNotFound(resultObject);
//     }

//     @Override
//     public ResponseEntity<ChangeLanguageResultObject> updateLanguage(
//             @Parameter(name = "request_body", description = "Language change request") @RequestBody
//             LanguageRequestVO langRequestVO) {
//         ChangeLanguageResultObject resultObject;
//         try {
//             resultObject = accountService.updateLanguage(langRequestVO);
//         } catch (Exception e) {
//             log.error("Error when changing language", e);

//             resultObject = new ChangeLanguageResultObject(null, ChangeLanguageResultStatusCode.ERROR);
//         }

//         return ResponseUtil.wrapOrNotFound(resultObject);
//     }

// }
