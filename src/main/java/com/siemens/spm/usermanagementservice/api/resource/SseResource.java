// package com.siemens.spm.usermanagementservice.api.resource;

// import java.util.List;
// import java.util.Optional;

// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.http.ResponseEntity;
// import org.springframework.web.bind.annotation.GetMapping;
// import org.springframework.web.bind.annotation.PathVariable;
// import org.springframework.web.bind.annotation.RequestBody;
// import org.springframework.web.bind.annotation.RequestParam;
// import org.springframework.web.bind.annotation.RestController;
// import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

// import com.siemens.spm.common.security.SecurityUtils;
// import com.siemens.spm.common.util.ResponseUtil;
// import com.siemens.spm.spmstudiosdk.dto.StudioUserDto;
// import com.siemens.spm.spmstudiosdk.exception.StudioException;
// import com.siemens.spm.spmstudiosdk.service.StudioUserService;
// import com.siemens.spm.usermanagementservice.api.controller.SseController;
// import com.siemens.spm.usermanagementservice.api.vo.request.SseSubscriptionRequestVO;
// import com.siemens.spm.usermanagementservice.api.vo.response.SseManipulationResultObject;
// import com.siemens.spm.usermanagementservice.config.MessagingConfig;
// import com.siemens.spm.usermanagementservice.sse.ConnectionNotFoundException;
// import com.siemens.spm.usermanagementservice.sse.InvalidTopicException;
// import com.siemens.spm.usermanagementservice.sse.SseException;
// import com.siemens.spm.usermanagementservice.sse.SseManager;
// import lombok.extern.slf4j.Slf4j;

// @Slf4j
// @RestController
// public class SseResource implements SseController {

//     @Autowired
//     private SseManager sseManager;

//     @Autowired
//     private StudioUserService studioUserService;

//     @Override
//     public SseEmitter establishConnection(Integer agencyId) {
//         String email = SecurityUtils.getCurrentUserEmail();

//         Optional<StudioUserDto> studioUserDtoOptional;
//         try {
//             studioUserDtoOptional = studioUserService.findByEmail(email);
//         } catch (StudioException e) {
//             log.error("Error while fetching user by email from Studio", e);

//             return null;
//         }
//         if (studioUserDtoOptional.isEmpty()) {
//             log.error("User with email {} not found from Studio", email);
//             return null;
//         }

//         Long userId = Long.valueOf(studioUserDtoOptional.get().getId());

//         try {
//             return sseManager.establishConnection(userId, agencyId, List.of(MessagingConfig.NOTIFICATION_CHANNEL));
//         } catch (SseException e) {
//             log.error("Error while establishing SSE connection", e);
//             return null;
//         }
//     }

//     @Override
//     public ResponseEntity<SseManipulationResultObject> subscribeTopics(
//             @PathVariable("connection_id") String connectionId,
//             @RequestBody SseSubscriptionRequestVO subscriptionRequestVO) {
//         // TODO: Validate user has access to the topics
//         try {
//             sseManager.subscribeTopics(connectionId, subscriptionRequestVO.getTopics());

//             return ResponseUtil.wrapOrNotFound(
//                     new SseManipulationResultObject(SseManipulationResultObject.StatusCode.SUCCESS));
//         } catch (InvalidTopicException e) {
//             return ResponseUtil.wrapOrNotFound(
//                     new SseManipulationResultObject(SseManipulationResultObject.StatusCode.INVALID_TOPIC));
//         } catch (ConnectionNotFoundException e) {
//             return ResponseUtil.wrapOrNotFound(
//                     new SseManipulationResultObject(SseManipulationResultObject.StatusCode.CONNECTION_NOT_FOUND));
//         }
//     }

//     @Override
//     public ResponseEntity<SseManipulationResultObject> unsubscribeTopics(
//             @PathVariable("connection_id") String connectionId,
//             @RequestParam List<String> topics) {
//         // TODO: Validate user has access to the topics
//         try {
//             sseManager.unsubscribeTopics(connectionId, topics);

//             return ResponseUtil.wrapOrNotFound(
//                     new SseManipulationResultObject(SseManipulationResultObject.StatusCode.SUCCESS));
//         } catch (InvalidTopicException e) {
//             return ResponseUtil.wrapOrNotFound(
//                     new SseManipulationResultObject(SseManipulationResultObject.StatusCode.INVALID_TOPIC));
//         } catch (ConnectionNotFoundException e) {
//             return ResponseUtil.wrapOrNotFound(
//                     new SseManipulationResultObject(SseManipulationResultObject.StatusCode.CONNECTION_NOT_FOUND));
//         }
//     }

//     @GetMapping("/test")
//     public ResponseEntity<String> testTimeout(@RequestParam(value = "timeout", required = false) Integer timeout) {
//         if (timeout == null) {
//             timeout = 100;
//         }
//         try {
//             Thread.sleep(timeout * 1000L);
//         } catch (InterruptedException e) {
//             log.error("Error while testing timeout", e);

//             Thread.currentThread().interrupt();
//         }

//         return ResponseEntity.of(Optional.of("OK"));
//     }

// }
