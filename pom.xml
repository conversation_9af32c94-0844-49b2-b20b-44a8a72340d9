<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.3.5</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>com.siemens.spm</groupId>
    <artifactId>spm-user-mgmt-service</artifactId>
    <version>3.2.0</version>
    <name>spm-user-mgmt-service</name>
    <description>User Management Service</description>

    <properties>
        <java.version>17</java.version>
        <skip.it>true</skip.it>

        <spm-agency-supports.version>3.2.0</spm-agency-supports.version>
        <spm-notification-lib.version>3.2.0</spm-notification-lib.version>
        <spm-user-mgmt-service-contract.version>3.2.0</spm-user-mgmt-service-contract.version>
        <spm-rule-service-contract.version>3.2.0</spm-rule-service-contract.version>
        <spm-analysis-service-contract.version>3.2.0</spm-analysis-service-contract.version>
        <spm-datahub-sdk.version>3.2.0</spm-datahub-sdk.version>
        <spm-perflog-crawler-contract.version>3.2.0</spm-perflog-crawler-contract.version>
        <spm-studio-sdk.version>3.2.0</spm-studio-sdk.version>

        <jakarta.interceptor-api.version>2.2.0</jakarta.interceptor-api.version>
        <commons-text.version>1.12.0</commons-text.version>

        <xmlunit-core.version>2.10.0</xmlunit-core.version>

        <!-- Jacoco properties (using for code coverage report) -->
        <sonar.jacoco.version>0.8.12</sonar.jacoco.version>
        <sonar.java.coveragePlugin>jacoco</sonar.java.coveragePlugin>
        <sonar.jacoco.reportPaths>${project.build.directory}/jacoco.exec</sonar.jacoco.reportPaths>
        <!-- list of unnecessary files to be ignored from code analysis -->
        <sonar.ignore>
            **/Application.java,
            **/config/*.java
        </sonar.ignore>
        <sonar.coverage.exclusions>${sonar.ignore}</sonar.coverage.exclusions>
        <sonar.exclusions>${sonar.ignore}</sonar.exclusions>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.yunex</groupId>
            <artifactId>data-exchange</artifactId>
            <version>1.0.0</version>
        </dependency>
        
        <dependency>
            <groupId>com.siemens.spm</groupId>
            <artifactId>spm-agency-supports</artifactId>
            <version>${spm-agency-supports.version}</version>
        </dependency>

        <dependency>
            <groupId>com.siemens.spm</groupId>
            <artifactId>spm-notification-lib</artifactId>
            <version>${spm-notification-lib.version}</version>
        </dependency>

        <!-- Contract dependencies -->
        <dependency>
            <groupId>com.siemens.spm</groupId>
            <artifactId>spm-user-mgmt-service-contract</artifactId>
            <version>${spm-user-mgmt-service-contract.version}</version>
        </dependency>

        <dependency>
            <groupId>com.siemens.spm</groupId>
            <artifactId>spm-rule-service-contract</artifactId>
            <version>${spm-rule-service-contract.version}</version>
        </dependency>

        <dependency>
            <groupId>com.siemens.spm</groupId>
            <artifactId>spm-analysis-service-contract</artifactId>
            <version>${spm-analysis-service-contract.version}</version>
        </dependency>

        <dependency>
            <groupId>com.siemens.spm</groupId>
            <artifactId>spm-datahub-sdk</artifactId>
            <version>${spm-datahub-sdk.version}</version>
        </dependency>

        <dependency>
            <groupId>com.siemens.spm</groupId>
            <artifactId>spm-perflog-crawler-contract</artifactId>
            <version>${spm-perflog-crawler-contract.version}</version>
        </dependency>

        <dependency>
            <groupId>com.siemens.spm</groupId>
            <artifactId>spm-studio-sdk</artifactId>
            <version>${spm-studio-sdk.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-oauth2-resource-server</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>

        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>${postgresql.version}</version>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.xmlunit</groupId>
                    <artifactId>xmlunit-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.xmlunit</groupId>
            <artifactId>xmlunit-core</artifactId>
            <version>${xmlunit-core.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Redis -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>

        <!-- Kafka -->
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>

        <!-- Other -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
            <version>${commons-text.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>jakarta.interceptor</groupId>
            <artifactId>jakarta.interceptor-api</artifactId>
            <version>${jakarta.interceptor-api.version}</version>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <version>5.2.0</version>
            <scope>test</scope>
        </dependency>

        <!--        Test container -->
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>postgresql</artifactId>
            <version>1.20.1</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>kafka</artifactId>
            <version>1.20.0</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.locationtech.jts</groupId>
            <artifactId>jts-core</artifactId>
            <version>1.20.0</version>
        </dependency>

        <dependency>
            <groupId>com.redis</groupId>
            <artifactId>testcontainers-redis</artifactId>
            <version>2.2.2</version>
            <scope>test</scope>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-failsafe-plugin</artifactId>
                <configuration>
                    <skipITs>${skip.it}</skipITs>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.cyclonedx</groupId>
                <artifactId>cyclonedx-maven-plugin</artifactId>
                <version>2.7.6</version>
                <configuration>
                    <projectType>library</projectType>
                    <schemaVersion>1.5</schemaVersion>
                    <includeBomSerialNumber>true</includeBomSerialNumber>
                    <includeCompileScope>true</includeCompileScope>
                    <includeProvidedScope>true</includeProvidedScope>
                    <includeRuntimeScope>true</includeRuntimeScope>
                    <includeSystemScope>true</includeSystemScope>
                    <includeTestScope>false</includeTestScope>
                    <includeLicenseText>false</includeLicenseText>
                    <outputFormat>all</outputFormat>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>makeAggregateBom</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>${sonar.jacoco.version}</version>
                    <executions>
                        <execution>
                            <id>default-prepare-agent</id>
                            <goals>
                                <goal>prepare-agent</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>default-report</id>
                            <phase>prepare-package</phase>
                            <goals>
                                <goal>report</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <distributionManagement>
        <repository>
            <id>maven-repo-product</id>
            <name>maven-repo-product</name>
            <url>${env.MAVEN_REPO_PRODUCT_URL}</url>
        </repository>
        <!--<snapshotRepository>
            <id>maven-repo</id>
            <name>maven-repo</name>
            <url>${env.MAVEN_REPO_URL}</url>
        </snapshotRepository> -->
    </distributionManagement>
</project>
