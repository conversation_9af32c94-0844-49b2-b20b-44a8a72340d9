# ----------------------------------------------
# Include the scanning pipeline configuration
# ----------------------------------------------
# This brings in the predefined scanning pipeline from the external project `mocca/scvm/scvm-scanning-pipeline`.
# It includes all necessary settings for vulnerability scanning and SBOM processing.
include:
  - project: mocca/scvm/scvm-scanning-pipeline
    file: .gitlab-ci-template.yml
    ref: release

image: ${MOCCA_CLI_BUILD_IMAGE}

variables:
  K8S_NAMESPACE: insights
  CHART_NAME: insights--spm-user-mgmt-service
  MAVEN_REPO_USER_NAME: $ARTIFACTORY_USER
  MAVEN_REPO_PASSWORD: $ARTIFACTORY_PASSWD
  MAVEN_REPO_URL: https://artifactory.mocca.yunextraffic.cloud/artifactory/its-dev/

cache:
  paths:
    - .m2/repository/

default:
  tags:
    - small

######################################################################
# Stages
######################################################################
stages:
  - build
  - lint
  - images
  - chart
  - deploy
  - security-code
  - security-cluster

build:maven:
  stage: build
  script:
    - mocca maven build -s ./settings.xml
  artifacts:
    paths:
      - target

lint:sonar:
  stage: lint
  allow_failure: true
  script:
    - mocca maven sonar
  needs: [ "build:maven" ]

docker:build-push-image: &docker
  stage: images
  tags:
    - medium
  before_script:
    - echo ${ARTIFACTORY_PASSWD} | docker login -u ${ARTIFACTORY_USER} --password-stdin ${ITS_DOCKER_REGISTRY}
  script:
    - mocca docker build
    - mocca docker push
  dependencies:
    - build:maven
  artifacts:
    expire_in: 1 hour
    paths:
      - docker-sha.txt

######################################################################
# Create Helm Chart                                                  #
######################################################################

helm:build-push-chart:
  stage: chart
  cache: { }
  script:
    - mocca helm build
    - mocca helm push
  artifacts:
    expire_in: 1 hour
    paths:
      - '**/*.tgz'
  needs: [ 'docker:build-push-image' ]
  tags:
    - medium
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
    - if: '$CI_COMMIT_TAG'
    - if: '$DEV_DEPLOY == "true"'

######################################################################
# Deploy                                                             #
######################################################################

deploy:cloud-dev:
  stage: deploy
  needs: [ 'helm:build-push-chart', 'docker:build-push-image' ]
  cache: { }
  script:
    - mocca aws update-kubeconfig
    # use `export PRJ_DEPLOY_ARGS='--set global.baseDomain=${BASIC_DOMAIN}'` to set the correct baseDomain
    - export PRJ_DEPLOY_ARGS='--set global.baseDomain=${BASIC_DOMAIN}'
    # - mocca helm build
    - mocca helm deploy
    # use `export PRJ_DEPLOY_ARGS='--set global.baseDomain=${BASIC_DOMAIN}'` to set the correct baseDomain
    # use  `mocca helm deploy` to deploy chart to on-premise-cluster
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
    - if: '$CI_COMMIT_TAG'
    - if: '$DEV_DEPLOY == "true"'

######################################################################
# Security                                                           #
######################################################################

security:nmap:
  stage: security-cluster
  script:
    # add 'export NMAP_EXCLUDE_PORTS=80,443' to accept default ports
    # use 'mocca security nmap <URL>' to run nmap on <URL>
    - echo 'template security:nmap'
    - export NMAP_EXCLUDE_PORTS=80,443
    - mocca security nmap
  retry: 2
  allow_failure: true # This is a temporary solution due to CI Runners for this job always get errors
  rules:
    - if: "$CI_COMMIT_TAG"
    - if: '$CI_COMMIT_BRANCH == "master"'

security:kubeaudit:
  stage: security-cluster
  script:
    # use 'mocca aws update-kubeconfig' to configure kubectl in gitlab runner
    # use 'mocca security kubeaudit' to run kubeaudit
    - echo 'template security:kubeaudit'
    - mocca aws update-kubeconfig
    - mocca security kubeaudit
  retry: 2
  allow_failure: true # This is a temporary solution due to CI Runners for this job always get errors
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'

######################################################################
# Update insights-release umbrella Helm Chart                          #
######################################################################
trigger:version-update:
  needs: [ "helm:build-push-chart" ]
  stage: deploy
  variables:
    # note: any variables defined in the global variables section are also inherited!
    UPSTREAM_VERSION: ${CI_COMMIT_TAG}
    UPSTREAM_APPLICATION: ${CHART_NAME}
  trigger:
    project: 'us/siwave/spm/insights-release'
    branch: main
    strategy: depend
  rules:
    # this job is enabled(remember it's manual) only if created tag follows pattern vx.y.z
    # where x, y and z are digits(0-99). This tag only should be created in master branch
    - if: '$CI_COMMIT_TAG =~ /^\d{1,2}\.\d{1,2}\.\d{1,2}$/'

###############################################
# Scanning Trigger Job                        #
###############################################
# This job will trigger the vulnerability scanning process based on the external template.
# It will use the `scvm:scan-trigger` definition from the included scanning pipeline.
# The environment for the scan can be set to `staging` or `production` as needed.
# By default, it uses the `main` branch which is staging
scvm:scan-trigger:
  extends: .scvm:scan-trigger
  variables:
    CONFIG_PROFILE: siwave
  rules:
    - if: "$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH"
    - if: "$CI_COMMIT_TAG"