# Maven
# Build your Java project and run tests with Apache Maven.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/java

trigger:
  - master
pool: k8s-dev
steps:
  - checkout: self
    fetchDepth: 0

  - task: DownloadSecureFile@1
    name: settingsXml
    displayName: 'Download settings.xml'
    inputs:
      secureFile: 'settings.xml'
  - script: |
      # Set up the path to the downloaded settings.xml
      settings_xml="$(settingsXml.secureFilePath)"
      
      # Run Maven with the -s option to specify the location of the settings.xml file
      mvn clean install -s "$settings_xml"
    displayName: 'Build with custom settings.xml'

  - task: Bash@3
    inputs:
      targetType: 'inline'
      script: |
        mvn sonar:sonar -Djavax.net.ssl.trustStorePassword=changeit -Djavax.net.ssl.trustStore=/usr/lib/jvm/java-17-openjdk-amd64/lib/security/cacerts -Dsonar.login=$(token) -Dsonar.host.url=$(host) -Dsonar.projectKey=INSIGHTS-spm-user-mgmt-service -Dsonar.projectName=INSIGHTS-spm-user-mgmt-service
      failOnStderr: true

    displayName: 'SonarScan'

  - task: Bash@3
    inputs:
      targetType: 'inline'
      script: |
        ls -lrt ./target
        curl -k -X "POST" "https://dependency-track.local.egs-dev.site/api/v1/bom" \
        -H 'Content-Type: multipart/form-data' \
        -H 'X-Api-Key: JvcaupXarbEQQOg8fNIkHyl7554dJt4S ' \
        -F "project=c523d770-8f74-43b8-9a3c-f0b6733ae751" \
        -F "autoCreate=true" \
        -F "projectName=spm-user-mgmt-service" \
        -F "projectVersion=master" \
        -F "bom=@./target/bom.xml"  2>&1
      failOnStderr: true
    displayName: 'dependency-check'
