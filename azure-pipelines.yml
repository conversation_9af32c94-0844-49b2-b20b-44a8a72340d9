trigger:
  - master

resources:
  repositories:
    - repository: self

pool:
  name: k8s-dev
  demands:
    - Agent.Name -equals Agent001

variables:
  - group: gitlab-yunex

stages:
  - stage: Build
    displayName: Repository Synchronization
    jobs:
      - job: Build
        displayName: Setup Git repo
        steps:
          - task: DownloadSecureFile@1
            name: downloadgitlabkey
            inputs:
              secureFile: $(KEY_NAME)
          - task: Bash@3
            displayName: Setup Git repo
            inputs:
              targetType: 'inline'
              script: |-
                rm -rf .git
                git init
                eval $(ssh-agent -s)
                chmod 400 $(downloadgitlabkey.secureFilePath)
                ssh-add $(downloadgitlabkey.secureFilePath)
                git remote add 'origin' $(AZURE_REPO)
                git remote set-url origin $(AZURE_REPO)
                git remote add 'moccagitlab' $(GITLAB_REPO_ADD)
                git remote set-url moccagitlab $(GITLAB_REPO_ADD)
                git fetch --all
                git push moccagitlab origin/$(SOURCE_BRANCH):refs/heads/$(DESTINATION_BRANCH)