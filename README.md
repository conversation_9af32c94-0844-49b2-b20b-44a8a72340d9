# DB Port Forwarding

You need to use different port to connect to DBs on different environments
```
export KUBECONFIG="USER_FOLDER\.kube\dev_config"
kubectl port-forward service/studio--postgres-cluster-pooler 5432:5432 -n studio
```

# DH-Integration Forwarding
```
kubectl port-forward service/datahub--spm-integration-service 8080:80 -n datahub
```

# Alarm Rule Service Forwarding
```
kubectl port-forward service/insights--spm-rule-service 7001:80 -n insights
```

# Open API Spec

http://localhost:9100/user-service/swagger-ui/index.html