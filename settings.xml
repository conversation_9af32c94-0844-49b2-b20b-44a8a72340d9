<settings>
    <servers>
        <server>
            <id>maven-repo-product</id>
            <username>${env.MAVEN_REPO_USER_NAME}</username>
            <password>${env.MAVEN_REPO_PASSWORD}</password>
        </server>
        <!-- <server>
            <id>maven-repo</id>
            <username>${env.MAVEN_REPO_USER_NAME}</username>
            <password>${env.MAVEN_REPO_PASSWORD}</password>
        </server> -->
    </servers>
    <profiles>
        <profile>
            <id>mocca-artifactory</id>
            <repositories>
                <repository>
                    <id>maven-repo-product</id>
                    <name>maven-repo-product</name>
                    <url>${env.MAVEN_REPO_PRODUCT_URL}</url>
                </repository>
                <!-- <repository>
                    <id>maven-repo</id>
                    <name>maven-repo</name>
                    <url>${env.MAVEN_REPO_URL}</url>
                </repository> -->
            </repositories>
        </profile>
    </profiles>
    <activeProfiles>
        <activeProfile>mocca-artifactory</activeProfile>
    </activeProfiles>
</settings>
