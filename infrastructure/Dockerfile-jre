FROM openjdk:17-jdk-oracle

ARG JAR_FILE=app.jar
ARG PORT=80
# Available profiles: dev, unittest, qa, prod
ARG PROFILE=qa
ARG MAX_MEMORY=1024

EXPOSE $PORT

ENV SPRING_OUTPUT_ANSI_ENABLED=ALWAYS
ENV PROFILE=$PROFILE
ENV MAX_MEMORY=$MAX_MEMORY

ADD $JAR_FILE app.jar
COPY ./run.sh /run.sh
RUN chmod 755 /run.sh
#ENTRYPOINT java -Xmx$MAX_MEMORY'm' -Duser.timezone=UTC -Djava.security.egd=file:/dev/./urandom -Dspring.profiles.active=$PROFILE -jar /app.jar
CMD ["/run.sh"]