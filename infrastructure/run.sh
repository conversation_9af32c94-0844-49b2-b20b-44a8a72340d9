#!/bin/sh

# Set OS variable
ENV_FILE="/vault/secrets/app.env"
# support k8s env export
if test -f "$ENV_FILE"; then
	   source ${ENV_FILE}
   fi
   
sed -i SPM_CONFIG_QA/${SPM_CONFIG_QA} bootstrap.yml
sed -i SPM_CONFIG_STAGING/${SPM_CONFIG_STAGING} bootstrap.yml
sed -i SPM_CONFIG_PROD/${SPM_CONFIG_PROD} bootstrap.yml

java -Xmx$MAX_MEMORY'm' -Duser.timezone=UTC -Djava.security.egd=file:/dev/./urandom -Dspring.profiles.active=$PROFILE -jar /app.jar