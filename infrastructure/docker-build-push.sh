#!/bin/bash
set -e

ARTIFACTORY_URL=$1
ARTIFACTORY_USER=$2
ARTIFACTORY_TOKEN=$3
APP_NAME=$4
PORT=$5
ENV=$6

if [ -z $ARTIFACTORY_URL ]; then
    echo "Usage: deploy-ecs.sh <ARTIFACTORY_URL>"
    exit 1
fi

if [ -z $ARTIFACTORY_USER ]; then
    echo "Usage: deploy-ecs.sh <ARTIFACTORY_USER>"
    exit 1
fi

if [ -z $ARTIFACTORY_TOKEN ]; then
    echo "Usage: deploy-ecs.sh"
    exit 1
fi

BUILD_NUMBER=`cat spm-platform-build.txt`
APP_VERSION=2.0.0

# Login to docker
echo "Login to docker"
echo "$ARTIFACTORY_TOKEN" | docker login --username "$ARTIFACTORY_USER" "$ARTIFACTORY_URL" --password-stdin

echo "Profile: $PROFILE"
echo "Build number: $BUILD_NUMBER"

echo "Build ${APP_NAME} - ${PORT}";
docker build -f Dockerfile-jre \
    --build-arg JAR_FILE=$APP_NAME-$APP_VERSION.jar \
    --build-arg PORT=${PORT} \
    -t $ARTIFACTORY_URL/$APP_NAME-$ENV:$BUILD_NUMBER \
    .

echo "Push iamge"
docker push $ARTIFACTORY_URL/$APP_NAME-$ENV:$BUILD_NUMBER

# Logout docker
docker logout
